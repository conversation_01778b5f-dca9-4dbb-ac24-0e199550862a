using UnityEngine;
using UnityEditor;

/// <summary>
/// NoiseMapGenerator的自定义编辑器
/// 适配新的ScriptableObject结构
/// </summary>
[CustomEditor(typeof(NoiseMapGenerator))]
public class NoiseMapGeneratorEditor : Editor
{
    private NoiseMapGenerator generator;

    void OnEnable()
    {
        generator = (NoiseMapGenerator)target;
    }

    public override void OnInspectorGUI()
    {
        serializedObject.Update();

        // 标题
        EditorGUILayout.Space();
        GUIStyle titleStyle = new GUIStyle(EditorStyles.boldLabel)
        {
            fontSize = 16,
            alignment = TextAnchor.MiddleCenter
        };
        EditorGUILayout.LabelField("噪声地图生成器", titleStyle);
        EditorGUILayout.Space();

        // 设置引用
        SerializedProperty settingsProperty = serializedObject.FindProperty("settings");
        SerializedProperty textureRendererProperty = serializedObject.FindProperty("textureRenderer");

        EditorGUILayout.PropertyField(settingsProperty, new GUIContent("噪声地图设置"));
        EditorGUILayout.PropertyField(textureRendererProperty, new GUIContent("纹理渲染器"));

        EditorGUILayout.Space();

        // 控制按钮
        EditorGUILayout.BeginHorizontal();

        if (GUILayout.Button("生成地图"))
        {
            if (generator.GetSettings() != null)
            {
                generator.GenerateMap();
            }
            else
            {
                EditorUtility.DisplayDialog("错误", "请先分配NoiseMapSettings资源！", "确定");
            }
        }

        if (GUILayout.Button("保存地图"))
        {
            SaveMapToFile();
        }

        EditorGUILayout.EndHorizontal();

        // 设置信息显示
        if (generator.GetSettings() != null)
        {
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("当前设置信息", EditorStyles.boldLabel);
            EditorGUI.indentLevel++;
            var settings = generator.GetSettings();
            EditorGUILayout.LabelField($"地图尺寸: {settings.mapWidth} x {settings.mapHeight}");
            EditorGUILayout.LabelField($"随机种子: {settings.seed}");
            EditorGUILayout.LabelField($"噪声缩放: {settings.noiseScale}");
            EditorGUILayout.LabelField($"倍频程: {settings.octaves}");
            EditorGUI.indentLevel--;
        }
        else
        {
            EditorGUILayout.HelpBox("请分配一个NoiseMapSettings资源！", MessageType.Warning);
        }

        // 统计信息
        DrawStatistics();

        serializedObject.ApplyModifiedProperties();
    }

    private void SaveMapToFile()
    {
        if (generator.GetNoiseMap() == null)
        {
            EditorUtility.DisplayDialog("错误", "没有生成的地图数据！请先生成地图。", "确定");
            return;
        }

        string path = EditorUtility.SaveFilePanel("保存噪声地图", "Assets", "NoiseMap", "png");
        if (!string.IsNullOrEmpty(path))
        {
            Texture2D texture;
            if (generator.GetSettings() != null && generator.useTerrainColoring && generator.colorMode != TerrainColorMode.Grayscale)
            {
                texture = NoiseMapGenerator.TextureFromHeightMapWithTerrain(generator.GetNoiseMap(), generator.colorMode, generator.textureFilterMode);
            }
            else
            {
                texture = NoiseMapGenerator.TextureFromHeightMap(generator.GetNoiseMap(), generator.textureFilterMode);
            }

            byte[] bytes = texture.EncodeToPNG();
            System.IO.File.WriteAllBytes(path, bytes);

            Debug.Log($"地图已保存到: {path}");

            // 清理纹理
            if (Application.isEditor)
            {
                DestroyImmediate(texture);
            }
            else
            {
                Destroy(texture);
            }
        }
    }

    private void DrawStatistics()
    {
        if (generator.GetNoiseMap() != null)
        {
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("地图统计", EditorStyles.boldLabel);
            var stats = generator.GetMapStatistics();
            EditorGUI.indentLevel++;
            EditorGUILayout.LabelField($"高度范围: {stats.minHeight:F3} - {stats.maxHeight:F3}");
            EditorGUILayout.LabelField($"平均高度: {stats.averageHeight:F3}");
            EditorGUILayout.LabelField($"水域占比: {stats.waterPercentage:F1}%");
            EditorGUILayout.LabelField($"陆地占比: {stats.landPercentage:F1}%");
            EditorGUI.indentLevel--;
        }
    }
}