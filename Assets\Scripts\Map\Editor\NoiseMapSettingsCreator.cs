using UnityEngine;
using UnityEditor;
using System.IO;

/// <summary>
/// NoiseMapSettings资源创建工具
/// </summary>
public class NoiseMapSettingsCreator
{
    [MenuItem("Assets/Create/Map Generation/Default Noise Map Settings")]
    public static void CreateDefaultNoiseMapSettings()
    {
        // 创建默认设置
        NoiseMapSettings settings = ScriptableObject.CreateInstance<NoiseMapSettings>();
        
        // 设置默认值
        settings.name = "DefaultNoiseMapSettings";
        
        // 确保Settings目录存在
        string settingsPath = "Assets/Settings";
        if (!Directory.Exists(settingsPath))
        {
            Directory.CreateDirectory(settingsPath);
        }
        
        // 创建资源文件
        string assetPath = Path.Combine(settingsPath, "DefaultNoiseMapSettings.asset");
        
        // 如果文件已存在，创建一个新的名称
        if (File.Exists(assetPath))
        {
            assetPath = AssetDatabase.GenerateUniqueAssetPath(assetPath);
        }
        
        AssetDatabase.CreateAsset(settings, assetPath);
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
        
        // 选中新创建的资源
        EditorUtility.FocusProjectWindow();
        Selection.activeObject = settings;
        
        Debug.Log($"默认NoiseMapSettings已创建: {assetPath}");
    }
    
    [MenuItem("Tools/Map Generation/Create Default Settings")]
    public static void CreateDefaultSettingsFromMenu()
    {
        CreateDefaultNoiseMapSettings();
    }
}
