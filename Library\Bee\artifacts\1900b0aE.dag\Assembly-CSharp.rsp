-target:library
-out:"Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll"
-refout:"Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.ref.dll"
-define:UNITY_2022_3_33
-define:UNITY_2022_3
-define:UNITY_2022
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:PLATFORM_ARCH_64
-define:UNITY_64
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_VIRTUALTEXTURING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_VIDEO
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:ENABLE_NAVIGATION_PACKAGE_DEBUG_VISUALIZATION
-define:ENABLE_NAVIGATION_HEIGHTMESH_RUNTIME_SUPPORT
-define:ENABLE_NAVIGATION_UI_REQUIRES_PACKAGE
-define:PLATFORM_STANDALONE
-define:TEXTCORE_1_0_OR_NEWER
-define:PLATFORM_STANDALONE_WIN
-define:UNITY_STANDALONE_WIN
-define:UNITY_STANDALONE
-define:UNITY_UGP_API
-define:ENABLE_RUNTIME_GI
-define:ENABLE_MOVIES
-define:ENABLE_NETWORK
-define:ENABLE_NVIDIA
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
-define:ENABLE_CLUSTER_SYNC
-define:ENABLE_CLUSTERINPUT
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
-define:PLATFORM_INITIALIZES_MEMORY_MANAGER_EXPLICITLY
-define:ENABLE_MONO
-define:NET_STANDARD_2_0
-define:NET_STANDARD
-define:NET_STANDARD_2_1
-define:NETSTANDARD
-define:NETSTANDARD2_1
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_WIN
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:UEE
-define:DOTWEEN
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"Assets/Plugins/Demigiant/DemiLib/Core/DemiLib.dll"
-r:"Assets/Plugins/Demigiant/DOTween/DOTween.dll"
-r:"Assets/Plugins/Demigiant/DOTweenPro/DOTweenPro.dll"
-r:"Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Libraries/Harmony.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/NetStandard/ref/2.1.0/netstandard.dll"
-r:"Library/PackageCache/com.unity.nuget.newtonsoft-json@3.2.1/Runtime/Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.visualscripting@1.9.8/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UltimateEditorEnhancer-Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UltimateEditorEnhancer.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/VTabs.ref.dll"
-analyzer:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
"Assets/Scripts/BaseClass/SingletonBase.cs"
"Assets/Scripts/BaseClass/SingletonMono.cs"
"Assets/Scripts/BaseClass/SingletonMonoX.cs"
"Assets/Scripts/DataSO/RoleSO.cs"
"Assets/Scripts/DialogueTree/DialogueNodeSO.cs"
"Assets/Scripts/DialogueTree/DialoguePlayer.cs"
"Assets/Scripts/DialogueTree/DialogueTreeSO.cs"
"Assets/Scripts/DialogueTree/HandleNode/HandlerBranching.cs"
"Assets/Scripts/DialogueTree/HandleNode/HandlerCondition.cs"
"Assets/Scripts/DialogueTree/HandleNode/HandlerEnd.cs"
"Assets/Scripts/DialogueTree/HandleNode/HandlerImplement.cs"
"Assets/Scripts/DialogueTree/HandleNode/HandlerRandom.cs"
"Assets/Scripts/DialogueTree/HandleNode/HandlerSequence.cs"
"Assets/Scripts/DialogueTree/HandleNode/HandlerShout.cs"
"Assets/Scripts/DialogueTree/HandleNode/Handler基础模板.cs"
"Assets/Scripts/DialogueTree/HandleNode/NodeHandlerFactory.cs"
"Assets/Scripts/DialogueTree/TreeNode/NodeBranching.cs"
"Assets/Scripts/DialogueTree/TreeNode/NodeBranchingCond.cs"
"Assets/Scripts/DialogueTree/TreeNode/NodeCondition.cs"
"Assets/Scripts/DialogueTree/TreeNode/NodeEnd.cs"
"Assets/Scripts/DialogueTree/TreeNode/NodeImplement.cs"
"Assets/Scripts/DialogueTree/TreeNode/NodeRandom.cs"
"Assets/Scripts/DialogueTree/TreeNode/NodeRoot.cs"
"Assets/Scripts/DialogueTree/TreeNode/NodeSequence.cs"
"Assets/Scripts/DialogueTree/TreeNode/NodeShout.cs"
"Assets/Scripts/DialogueTree/ViewDialogueInspector.cs"
"Assets/Scripts/DialogueTree/ViewDialogueNode.cs"
"Assets/Scripts/DialogueTree/ViewDialogueTree.cs"
"Assets/Scripts/Manager/AudioManager.cs"
"Assets/Scripts/Manager/BootSetting.cs"
"Assets/Scripts/Manager/ConfigurationManager.cs"
"Assets/Scripts/Manager/EventCenter.cs"
"Assets/Scripts/Manager/LanguageManager.cs"
"Assets/Scripts/Map/NoiseMapGenerator.cs"
"Assets/Scripts/TSVReader.cs"
"Assets/TutorialInfo/Scripts/Readme.cs"
-langversion:9.0
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
/additionalfile:"Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"