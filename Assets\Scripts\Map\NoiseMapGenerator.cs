using UnityEngine;

/// <summary>
/// 高度重分布类型
/// </summary>
public enum RedistributionType
{
    None,           // 不进行重分布
    Power,          // 幂函数重分布
    Curve,          // 自定义曲线重分布
    Terraces,       // 梯田效果
    InversePower    // 反向幂函数（突出低地）
}

/// <summary>
/// 地形着色模式
/// </summary>
public enum TerrainColorMode
{
    Grayscale,      // 灰度图
    Biomes,         // 生物群系着色
    Elevation       // 高度着色
}

/// <summary>
/// 噪声地图生成器
/// 基于Red Blob Games的技术方案实现随机地图生成
/// 参考: https://www.redblobgames.com/maps/terrain-from-noise/
/// </summary>
[System.Serializable]
public class NoiseMapGenerator : MonoBehaviour
{
    [Header("配置设置")]
    [SerializeField] private NoiseMapSettings settings;

    [Header("预览设置")]
    [SerializeField] private Renderer textureRenderer;

    // 生成的噪声数据
    private float[,] noiseMap;

    // 地图统计信息
    [System.Serializable]
    public struct MapStatistics
    {
        public float minHeight;
        public float maxHeight;
        public float averageHeight;
        public float waterPercentage;
        public float landPercentage;
        public int totalPixels;
    }

    private MapStatistics currentStats;

    void Start()
    {
        if (settings != null && settings.autoUpdate)
        {
            GenerateMap();
        }
    }

    /// <summary>
    /// 生成噪声地图
    /// </summary>
    public void GenerateMap()
    {
        if (settings == null)
        {
            Debug.LogError("NoiseMapSettings 未设置！请在Inspector中分配一个NoiseMapSettings资源。");
            return;
        }

        if (settings.useCustomAmplitudes)
        {
            noiseMap = GenerateNoiseMapWithCustomAmplitudes(settings.mapWidth, settings.mapHeight, settings.seed, settings.noiseScale, settings.customAmplitudes, settings.lacunarity, settings.offset, settings.normalizeAmplitudes);
        }
        else
        {
            noiseMap = GenerateNoiseMap(settings.mapWidth, settings.mapHeight, settings.seed, settings.noiseScale, settings.octaves, settings.persistence, settings.lacunarity, settings.offset);
        }

        if (settings.useRedistribution)
        {
            ApplyRedistribution(noiseMap, settings.redistributionType, settings.redistributionExponent, settings.redistributionCurve, settings.terraceCount, settings.terraceSmoothing);
        }

        // 应用圆滑化处理
        if (settings.useSmoothTerrain)
        {
            ApplySmoothTerrain(noiseMap, settings.smoothingIterations, settings.smoothingStrength);
        }

        // 计算统计信息
        if (settings.showStatistics)
        {
            CalculateMapStatistics();
        }

        DisplayMap();

        // 自动保存
        if (settings.autoSaveOnGenerate)
        {
            AutoSaveMap();
        }
    }

    /// <summary>
    /// 生成噪声地图数据
    /// </summary>
    /// <param name="mapWidth">地图宽度</param>
    /// <param name="mapHeight">地图高度</param>
    /// <param name="seed">随机种子</param>
    /// <param name="scale">噪声缩放</param>
    /// <param name="octaves">倍频程数量</param>
    /// <param name="persistence">持续性</param>
    /// <param name="lacunarity">间隙性</param>
    /// <param name="offset">偏移量</param>
    /// <returns>噪声地图数组</returns>
    public static float[,] GenerateNoiseMap(int mapWidth, int mapHeight, int seed, float scale, int octaves, float persistence, float lacunarity, Vector2 offset)
    {
        float[,] noiseMap = new float[mapWidth, mapHeight];

        // 使用种子初始化随机数生成器
        System.Random prng = new System.Random(seed);
        Vector2[] octaveOffsets = new Vector2[octaves];

        // 为每个倍频程生成独立的随机偏移（基于种子，确保可重现性）
        for (int i = 0; i < octaves; i++)
        {
            float offsetX = prng.Next(-100000, 100000);
            float offsetY = prng.Next(-100000, 100000);
            octaveOffsets[i] = new Vector2(offsetX, offsetY);
        }

        if (scale <= 0)
        {
            scale = 0.0001f;
        }

        float maxNoiseHeight = float.MinValue;
        float minNoiseHeight = float.MaxValue;

        float halfWidth = mapWidth / 2f;
        float halfHeight = mapHeight / 2f;

        // 生成噪声
        for (int y = 0; y < mapHeight; y++)
        {
            for (int x = 0; x < mapWidth; x++)
            {
                float amplitude = 1;
                float frequency = 1;
                float noiseHeight = 0;

                // 多倍频程噪声叠加
                for (int i = 0; i < octaves; i++)
                {
                    // 正确的偏移应用：用户偏移在频率缩放之前应用
                    float baseX = (x - halfWidth + offset.x) / scale;
                    float baseY = (y - halfHeight + offset.y) / scale;
                    float sampleX = baseX * frequency + octaveOffsets[i].x;
                    float sampleY = baseY * frequency + octaveOffsets[i].y;

                    // Unity的Perlin噪声返回0-1，我们转换为-1到1
                    float perlinValue = Mathf.PerlinNoise(sampleX, sampleY) * 2 - 1;
                    noiseHeight += perlinValue * amplitude;

                    amplitude *= persistence;
                    frequency *= lacunarity;
                }

                if (noiseHeight > maxNoiseHeight)
                {
                    maxNoiseHeight = noiseHeight;
                }
                else if (noiseHeight < minNoiseHeight)
                {
                    minNoiseHeight = noiseHeight;
                }

                noiseMap[x, y] = noiseHeight;
            }
        }

        // 标准化到0-1范围
        for (int y = 0; y < mapHeight; y++)
        {
            for (int x = 0; x < mapWidth; x++)
            {
                noiseMap[x, y] = Mathf.InverseLerp(minNoiseHeight, maxNoiseHeight, noiseMap[x, y]);
            }
        }

        return noiseMap;
    }

    /// <summary>
    /// 使用自定义振幅生成噪声地图
    /// 参考Red Blob Games的技术方案，支持自定义每个倍频程的振幅
    /// </summary>
    /// <param name="mapWidth">地图宽度</param>
    /// <param name="mapHeight">地图高度</param>
    /// <param name="seed">随机种子</param>
    /// <param name="scale">噪声缩放</param>
    /// <param name="amplitudes">自定义振幅数组</param>
    /// <param name="lacunarity">间隙性</param>
    /// <param name="offset">偏移量</param>
    /// <param name="normalize">是否标准化振幅</param>
    /// <returns>噪声地图数组</returns>
    public static float[,] GenerateNoiseMapWithCustomAmplitudes(int mapWidth, int mapHeight, int seed, float scale, float[] amplitudes, float lacunarity, Vector2 offset, bool normalize)
    {
        float[,] noiseMap = new float[mapWidth, mapHeight];

        if (amplitudes == null || amplitudes.Length == 0)
        {
            Debug.LogWarning("自定义振幅数组为空，使用默认值");
            amplitudes = new float[] { 1f, 0.5f, 0.25f, 0.125f };
        }

        int octaves = amplitudes.Length;

        // 使用种子初始化随机数生成器
        System.Random prng = new System.Random(seed);
        Vector2[] octaveOffsets = new Vector2[octaves];

        for (int i = 0; i < octaves; i++)
        {
            float offsetX = prng.Next(-100000, 100000);
            float offsetY = prng.Next(-100000, 100000);
            octaveOffsets[i] = new Vector2(offsetX, offsetY);
        }

        if (scale <= 0)
        {
            scale = 0.0001f;
        }

        // 计算振幅总和用于标准化
        float amplitudeSum = 0f;
        if (normalize)
        {
            for (int i = 0; i < amplitudes.Length; i++)
            {
                amplitudeSum += amplitudes[i];
            }
        }
        else
        {
            amplitudeSum = 1f; // 不标准化时设为1
        }

        float maxNoiseHeight = float.MinValue;
        float minNoiseHeight = float.MaxValue;

        float halfWidth = mapWidth / 2f;
        float halfHeight = mapHeight / 2f;

        // 生成噪声
        for (int y = 0; y < mapHeight; y++)
        {
            for (int x = 0; x < mapWidth; x++)
            {
                float frequency = 1;
                float noiseHeight = 0;

                // 多倍频程噪声叠加 - 使用自定义振幅
                for (int i = 0; i < octaves; i++)
                {
                    // 正确的偏移应用：用户偏移在频率缩放之前应用
                    float baseX = (x - halfWidth + offset.x) / scale;
                    float baseY = (y - halfHeight + offset.y) / scale;
                    float sampleX = baseX * frequency + octaveOffsets[i].x;
                    float sampleY = baseY * frequency + octaveOffsets[i].y;

                    // Unity的Perlin噪声返回0-1，我们转换为-1到1
                    float perlinValue = Mathf.PerlinNoise(sampleX, sampleY) * 2 - 1;
                    noiseHeight += perlinValue * amplitudes[i];

                    frequency *= lacunarity;
                }

                // 标准化
                if (normalize)
                {
                    noiseHeight /= amplitudeSum;
                }

                if (noiseHeight > maxNoiseHeight)
                {
                    maxNoiseHeight = noiseHeight;
                }
                else if (noiseHeight < minNoiseHeight)
                {
                    minNoiseHeight = noiseHeight;
                }

                noiseMap[x, y] = noiseHeight;
            }
        }

        // 标准化到0-1范围
        for (int y = 0; y < mapHeight; y++)
        {
            for (int x = 0; x < mapWidth; x++)
            {
                noiseMap[x, y] = Mathf.InverseLerp(minNoiseHeight, maxNoiseHeight, noiseMap[x, y]);
            }
        }

        return noiseMap;
    }

    /// <summary>
    /// 应用高度重分布
    /// 支持多种重分布方式：幂函数、自定义曲线、梯田效果等
    /// </summary>
    /// <param name="noiseMap">噪声地图</param>
    /// <param name="type">重分布类型</param>
    /// <param name="exponent">重分布指数</param>
    /// <param name="curve">自定义曲线</param>
    /// <param name="terraceCount">梯田数量</param>
    /// <param name="terraceSmoothing">梯田平滑度</param>
    private void ApplyRedistribution(float[,] noiseMap, RedistributionType type, float exponent, AnimationCurve curve, float terraceCount, float terraceSmoothing)
    {
        int width = noiseMap.GetLength(0);
        int height = noiseMap.GetLength(1);

        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                float originalValue = noiseMap[x, y];
                float redistributedValue = originalValue;

                switch (type)
                {
                    case RedistributionType.None:
                        redistributedValue = originalValue;
                        break;

                    case RedistributionType.Power:
                        // 幂函数重分布 - 创建平坦谷地和陡峭山峰
                        redistributedValue = Mathf.Pow(originalValue, exponent);
                        break;

                    case RedistributionType.InversePower:
                        // 反向幂函数 - 突出低地
                        redistributedValue = 1f - Mathf.Pow(1f - originalValue, exponent);
                        break;

                    case RedistributionType.Curve:
                        // 自定义曲线重分布
                        redistributedValue = curve.Evaluate(originalValue);
                        break;

                    case RedistributionType.Terraces:
                        // 梯田效果
                        redistributedValue = ApplyTerraceEffect(originalValue, terraceCount, terraceSmoothing);
                        break;
                }

                noiseMap[x, y] = Mathf.Clamp01(redistributedValue);
            }
        }
    }

    /// <summary>
    /// 应用梯田效果
    /// </summary>
    /// <param name="value">原始值</param>
    /// <param name="terraceCount">梯田数量</param>
    /// <param name="smoothing">平滑度</param>
    /// <returns>处理后的值</returns>
    private float ApplyTerraceEffect(float value, float terraceCount, float smoothing)
    {
        if (terraceCount <= 1) return value;

        float terraceValue = value * terraceCount;
        float terraceFloor = Mathf.Floor(terraceValue);
        float terraceFraction = terraceValue - terraceFloor;

        // 应用平滑
        if (smoothing > 0)
        {
            terraceFraction = Mathf.SmoothStep(0, 1, terraceFraction / smoothing);
            terraceFraction = Mathf.Clamp01(terraceFraction);
        }
        else
        {
            terraceFraction = 0; // 完全平坦的梯田
        }

        return (terraceFloor + terraceFraction) / terraceCount;
    }

    /// <summary>
    /// 应用圆滑化处理
    /// 使用多次平滑滤波来创建真正圆滑的地形
    /// </summary>
    /// <param name="noiseMap">噪声地图</param>
    /// <param name="iterations">平滑迭代次数</param>
    /// <param name="strength">平滑强度</param>
    private void ApplySmoothTerrain(float[,] noiseMap, int iterations, float strength)
    {
        if (iterations <= 0 || strength <= 0) return;

        int width = noiseMap.GetLength(0);
        int height = noiseMap.GetLength(1);

        // 创建临时数组避免在迭代中修改原数组
        float[,] tempMap = new float[width, height];

        for (int iter = 0; iter < iterations; iter++)
        {
            // 复制当前状态到临时数组
            System.Array.Copy(noiseMap, tempMap, width * height);

            // 对每个像素应用平滑滤波
            for (int y = 1; y < height - 1; y++)
            {
                for (int x = 1; x < width - 1; x++)
                {
                    // 使用3x3高斯核进行平滑
                    float smoothedValue = ApplyGaussianSmooth(tempMap, x, y, width, height);

                    // 混合原始值和平滑值
                    noiseMap[x, y] = Mathf.Lerp(tempMap[x, y], smoothedValue, strength);
                }
            }
        }
    }

    /// <summary>
    /// 应用高斯平滑滤波
    /// 使用3x3高斯核创建真正的圆滑效果
    /// </summary>
    /// <param name="heightMap">高度图</param>
    /// <param name="centerX">中心X坐标</param>
    /// <param name="centerY">中心Y坐标</param>
    /// <param name="width">地图宽度</param>
    /// <param name="height">地图高度</param>
    /// <returns>平滑后的值</returns>
    private float ApplyGaussianSmooth(float[,] heightMap, int centerX, int centerY, int width, int height)
    {
        // 3x3高斯核权重（标准化）
        float[,] gaussianKernel = new float[,]
        {
            { 0.0625f, 0.125f, 0.0625f },  // 1/16, 2/16, 1/16
            { 0.125f,  0.25f,  0.125f  },  // 2/16, 4/16, 2/16
            { 0.0625f, 0.125f, 0.0625f }   // 1/16, 2/16, 1/16
        };

        float result = 0f;

        for (int dy = -1; dy <= 1; dy++)
        {
            for (int dx = -1; dx <= 1; dx++)
            {
                int sampleX = centerX + dx;
                int sampleY = centerY + dy;

                // 边界处理：使用边界值
                sampleX = Mathf.Clamp(sampleX, 0, width - 1);
                sampleY = Mathf.Clamp(sampleY, 0, height - 1);

                float weight = gaussianKernel[dy + 1, dx + 1];
                result += heightMap[sampleX, sampleY] * weight;
            }
        }

        return result;
    }

    /// <summary>
    /// 显示地图
    /// </summary>
    private void DisplayMap()
    {
        if (textureRenderer == null || settings == null) return;

        Texture2D texture;

        if (settings.useTerrainColoring && settings.colorMode != TerrainColorMode.Grayscale)
        {
            texture = TextureFromHeightMapWithTerrain(noiseMap, settings.colorMode, settings.textureFilterMode);
        }
        else
        {
            texture = TextureFromHeightMap(noiseMap, settings.textureFilterMode);
        }

        textureRenderer.sharedMaterial.mainTexture = texture;
    }

    /// <summary>
    /// 从高度图创建纹理
    /// </summary>
    /// <param name="heightMap">高度图</param>
    /// <param name="filterMode">纹理过滤模式</param>
    /// <returns>生成的纹理</returns>
    public static Texture2D TextureFromHeightMap(float[,] heightMap, FilterMode filterMode = FilterMode.Point)
    {
        int width = heightMap.GetLength(0);
        int height = heightMap.GetLength(1);

        Color[] colorMap = new Color[width * height];
        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                colorMap[y * width + x] = Color.Lerp(Color.black, Color.white, heightMap[x, y]);
            }
        }

        return TextureFromColorMap(colorMap, width, height, filterMode);
    }

    /// <summary>
    /// 从高度图创建带地形着色的纹理
    /// </summary>
    /// <param name="heightMap">高度图</param>
    /// <param name="colorMode">着色模式</param>
    /// <param name="filterMode">纹理过滤模式</param>
    /// <returns>生成的纹理</returns>
    public static Texture2D TextureFromHeightMapWithTerrain(float[,] heightMap, TerrainColorMode colorMode, FilterMode filterMode = FilterMode.Point)
    {
        int width = heightMap.GetLength(0);
        int height = heightMap.GetLength(1);

        Color[] colorMap = new Color[width * height];
        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                float heightValue = heightMap[x, y];
                Color terrainColor = GetTerrainColor(heightValue, colorMode);
                colorMap[y * width + x] = terrainColor;
            }
        }

        return TextureFromColorMap(colorMap, width, height, filterMode);
    }

    /// <summary>
    /// 根据高度值和着色模式获取地形颜色
    /// </summary>
    /// <param name="height">高度值 (0-1)</param>
    /// <param name="colorMode">着色模式</param>
    /// <returns>地形颜色</returns>
    public static Color GetTerrainColor(float height, TerrainColorMode colorMode)
    {
        switch (colorMode)
        {
            case TerrainColorMode.Grayscale:
                return Color.Lerp(Color.black, Color.white, height);

            case TerrainColorMode.Biomes:
                return GetBiomeColor(height);

            case TerrainColorMode.Elevation:
                return GetElevationColor(height);

            default:
                return Color.white;
        }
    }

    /// <summary>
    /// 获取生物群系颜色
    /// 参考Red Blob Games的生物群系配色方案
    /// </summary>
    /// <param name="height">高度值</param>
    /// <returns>生物群系颜色</returns>
    public static Color GetBiomeColor(float height)
    {
        // 基于Red Blob Games的生物群系阈值
        if (height < 0.1f) return new Color(0.2f, 0.4f, 0.8f, 1f);      // 海洋 - 深蓝
        if (height < 0.15f) return new Color(0.9f, 0.8f, 0.6f, 1f);     // 海滩 - 沙色
        if (height < 0.3f) return new Color(0.3f, 0.6f, 0.2f, 1f);      // 草地 - 绿色
        if (height < 0.5f) return new Color(0.2f, 0.4f, 0.1f, 1f);      // 森林 - 深绿
        if (height < 0.7f) return new Color(0.4f, 0.3f, 0.2f, 1f);      // 丘陵 - 棕色
        if (height < 0.9f) return new Color(0.5f, 0.4f, 0.3f, 1f);      // 山地 - 灰棕
        return new Color(0.9f, 0.9f, 1.0f, 1f);                         // 雪山 - 白色
    }

    /// <summary>
    /// 获取高度着色
    /// 使用蓝到红的渐变表示高度
    /// </summary>
    /// <param name="height">高度值</param>
    /// <returns>高度颜色</returns>
    public static Color GetElevationColor(float height)
    {
        // 蓝色(低) -> 绿色(中) -> 红色(高)
        if (height < 0.5f)
        {
            return Color.Lerp(Color.blue, Color.green, height * 2f);
        }
        else
        {
            return Color.Lerp(Color.green, Color.red, (height - 0.5f) * 2f);
        }
    }

    /// <summary>
    /// 从颜色数组创建纹理
    /// </summary>
    /// <param name="colorMap">颜色数组</param>
    /// <param name="width">宽度</param>
    /// <param name="height">高度</param>
    /// <param name="filterMode">纹理过滤模式</param>
    /// <returns>生成的纹理</returns>
    public static Texture2D TextureFromColorMap(Color[] colorMap, int width, int height, FilterMode filterMode = FilterMode.Point)
    {
        Texture2D texture = new Texture2D(width, height);
        texture.filterMode = filterMode;  // 使用传入的过滤模式
        texture.wrapMode = TextureWrapMode.Clamp;
        texture.SetPixels(colorMap);
        texture.Apply();
        return texture;
    }

    /// <summary>
    /// 计算地图统计信息
    /// </summary>
    private void CalculateMapStatistics()
    {
        if (noiseMap == null) return;

        int width = noiseMap.GetLength(0);
        int height = noiseMap.GetLength(1);
        int totalPixels = width * height;

        float minHeight = float.MaxValue;
        float maxHeight = float.MinValue;
        float totalHeight = 0f;
        int waterPixels = 0;

        const float waterLevel = 0.1f; // 水位阈值

        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                float heightValue = noiseMap[x, y];

                if (heightValue < minHeight) minHeight = heightValue;
                if (heightValue > maxHeight) maxHeight = heightValue;

                totalHeight += heightValue;

                if (heightValue < waterLevel) waterPixels++;
            }
        }

        currentStats = new MapStatistics
        {
            minHeight = minHeight,
            maxHeight = maxHeight,
            averageHeight = totalHeight / totalPixels,
            waterPercentage = (float)waterPixels / totalPixels * 100f,
            landPercentage = (float)(totalPixels - waterPixels) / totalPixels * 100f,
            totalPixels = totalPixels
        };

        if (settings.showStatistics)
        {
            Debug.Log($"地图统计信息:\n" +
                     $"高度范围: {minHeight:F3} - {maxHeight:F3}\n" +
                     $"平均高度: {currentStats.averageHeight:F3}\n" +
                     $"水域占比: {currentStats.waterPercentage:F1}%\n" +
                     $"陆地占比: {currentStats.landPercentage:F1}%\n" +
                     $"总像素数: {totalPixels}");
        }
    }

    /// <summary>
    /// 自动保存地图
    /// </summary>
    private void AutoSaveMap()
    {
        if (noiseMap == null || settings == null) return;

        try
        {
            // 确保导出目录存在
            if (!System.IO.Directory.Exists(settings.exportPath))
            {
                System.IO.Directory.CreateDirectory(settings.exportPath);
            }

            // 生成文件名
            string timestamp = System.DateTime.Now.ToString("yyyyMMdd_HHmmss");
            string fileName = $"NoiseMap_{settings.seed}_{timestamp}.png";
            string fullPath = System.IO.Path.Combine(settings.exportPath, fileName);

            // 生成纹理
            Texture2D texture;
            if (settings.useTerrainColoring && settings.colorMode != TerrainColorMode.Grayscale)
            {
                texture = TextureFromHeightMapWithTerrain(noiseMap, settings.colorMode, settings.textureFilterMode);
            }
            else
            {
                texture = TextureFromHeightMap(noiseMap, settings.textureFilterMode);
            }

            // 保存文件
            byte[] bytes = texture.EncodeToPNG();
            System.IO.File.WriteAllBytes(fullPath, bytes);

            Debug.Log($"地图已自动保存到: {fullPath}");

            // 清理纹理
            if (Application.isEditor)
            {
                DestroyImmediate(texture);
            }
            else
            {
                Destroy(texture);
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"自动保存地图失败: {e.Message}");
        }
    }

    /// <summary>
    /// 获取生成的噪声地图数据
    /// </summary>
    /// <returns>噪声地图数组</returns>
    public float[,] GetNoiseMap()
    {
        return noiseMap;
    }

    /// <summary>
    /// 获取当前地图统计信息
    /// </summary>
    /// <returns>地图统计信息</returns>
    public MapStatistics GetMapStatistics()
    {
        return currentStats;
    }

    /// <summary>
    /// 设置噪声地图配置
    /// </summary>
    /// <param name="newSettings">新的配置设置</param>
    public void SetSettings(NoiseMapSettings newSettings)
    {
        settings = newSettings;
        if (settings != null && settings.autoUpdate && Application.isPlaying)
        {
            GenerateMap();
        }
    }

    /// <summary>
    /// 获取当前配置设置
    /// </summary>
    /// <returns>当前的配置设置</returns>
    public NoiseMapSettings GetSettings()
    {
        return settings;
    }

    // 为了保持向后兼容性，提供一些公共属性
    public bool autoUpdate => settings != null ? settings.autoUpdate : true;
    public bool useTerrainColoring => settings != null ? settings.useTerrainColoring : true;
    public TerrainColorMode colorMode => settings != null ? settings.colorMode : TerrainColorMode.Biomes;
    public FilterMode textureFilterMode => settings != null ? settings.textureFilterMode : FilterMode.Bilinear;
}
