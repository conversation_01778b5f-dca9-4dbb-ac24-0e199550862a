var Ie=Object.defineProperty;var ki=Object.getOwnPropertyDescriptor;var Ci=Object.getOwnPropertyNames;var Mi=Object.prototype.hasOwnProperty;var _i=(i,t)=>{for(var e in t)Ie(i,e,{get:t[e],enumerable:!0})},Re=(i,t,e,s)=>{if(t&&typeof t=="object"||typeof t=="function")for(let n of Ci(t))!Mi.call(i,n)&&n!==e&&Ie(i,n,{get:()=>t[n],enumerable:!(s=ki(t,n))||s.enumerable});return i},vt=(i,t,e)=>(Re(i,t,"default"),e&&Re(e,t,"default"));var Ti=i=>Re(Ie({},"__esModule",{value:!0}),i);var ws={};_i(ws,{default:()=>Oe});module.exports=Ti(ws);var c={};vt(c,require("obsidian"));var g=typeof queueMicrotask=="function"?queueMicrotask:(i=>t=>i.then(t))(Promise.resolve());function G(i,t){return new De(i,t)}var De=class{constructor(t,e){this.reap=t,this.sched=e,this._flags=0,this.q=new Set,this._run=()=>{this._flags&=-3,this.flush()},this.flush=()=>{if(this._flags&1)return;let{q:s}=this;if(s.size){this._flags|=1;try{this.reap(s)}finally{this._flags&=-2,!s.size||this._flags&2||this._sched()}}}}isRunning(){return!!(this._flags&1)}isEmpty(){return!this.q.size}add(t){this.q.size||this._flags&3||this._sched(),this.q.add(t)}delete(t){this.q.delete(t)}has(t){return this.q.has(t)}_sched(){this._flags|=2,(this.sched||g)(this._run)}};var Fi=Array.isArray;function ee(i,t,e){return i.set(t,e),e}function k(i){return typeof i=="function"}var He=(()=>{function i(t){return Object.setPrototypeOf(t,new.target.prototype)}return i.prototype=Function.prototype,i})(),j=Reflect.apply,Ne=(()=>{function i(){}return i.prototype=function*(){}.constructor.prototype.prototype,i})();function Et(i){return i.bind(null,"next")}function St(i){return i.bind(null,"throw",void 0)}function te(){}function je(i,t,e){return{op:i,val:t,err:e}}var ve=Object.freeze(je("cancel"));function kt(i){return je("next",i)}function Ct(i){return je("throw",void 0,i)}function Je(i){return i===ve}function xe(i){return i?i.op==="next":!1}function z(i){return i?i.op==="throw":!1}function We(i){return z(i)&&i.val===void 0}function q(i){return i.val=null,i.err}function Mt(i){if(xe(i))return i.val;i.op,ie(te,t=>{throw t},i)}function ie(i,t,e){z(e)?t(q(e)):Je(e)?t(new we("Job canceled")):i(e.val)}function _t(i,t){i.result()||ie(i.return.bind(i),i.throw.bind(i),t)}var we=class extends Error{},C=I();function y(i){let t=C;return C=i,t}var pe=[];function I(i,t){if(pe&&pe.length){let e=pe.pop();return e.job=i,e.cell=t,e}return{job:i,cell:t}}function Be(i){i.job=i.cell=null,pe.push(i)}var $=new WeakMap,Tt=i=>{Promise.reject(i)},qe=I(),wt=new WeakMap,Ft=G(i=>{for(let t of i)i.delete(t),t.doPull()},g);function yt(){return Ee(0,void 0,void 0)}function Pi(i){for(;i.v;)be(i,i.n);i.u=void 0,be(i,i)}function Ai(i,t){++i.v,Ee(t,i.n,i)}function Li(i,t){++i.v,Ee(t,i,i.p)}function Oi(i,t){return++i.v,Di(i,Ee(t,i,i.p))}function Ri(i){return!i||i.v===0}function Pt(i){return i?i.v:0}function Ii(i){if(Pt(i))return be(i,i.p)}var Ve=class{constructor(){this.n=this,this.p=this,this.v=void 0,this.u=void 0}},ye;function Ee(i,t,e){let s=ye;return s?(ye=s.n,s.n=t||s,s.p=e||s):(s=new Ve,t&&(s.n=t),e&&(s.p=e)),s.v=i,s.n.p=s,s.p.n=s,s}function be(i,t){--i.v;var e=t.v,s=t.u;return t.n&&(t.n.p=t.p),t.p&&(t.p.n=t.n),t.u=t.v=t.p=void 0,t.n=ye,ye=t,s&&s(),e}function Di(i,t){let e=t.u||(t.u=()=>{e&&(e===t.u&&be(i,t),e=i=t=void 0)});return e}function K(){var t;let i=C.job||((t=C.cell)==null?void 0:t.getJob());if(i)return i;throw new Error("No job is currently active")}function Hi(i){return t=>{C.job.must(i.release(t))}}function bt(i,t){for(;Pt(t);)try{Ii(t)(i)}catch(e){se.asyncThrow(e)}t&&Pi(t)}var P=new Set,J=class{constructor(){this.end=()=>{let t=this._done||(this._done=ve),e=this._cbs;if(!e&&!We(t))return;let s=P.size,n=y(qe);if(s||P.add(null),e&&e.u&&(e.u=bt(t,e.u)),P.add(this),s){y(n);return}P.delete(null);for(let r of P)r._cbs&&(r._cbs=bt(r._done,r._cbs)),P.delete(r),We(r._done)&&r.throw(q(r._done));y(n)},this._done=void 0,this._cbs=void 0}static create(t,e){let s=new J;return(t||e)&&(s.must((t||(t=K())).release(e||s.end)),wt.set(s,t)),s}do(t){return Ai(this._chain(),t),this}onError(t){return this.do(e=>{z(e)&&t(q(e))})}onValue(t){return this.do(e=>{xe(e)&&t(e.val)})}onCancel(t){return this.do(e=>{Je(e)&&t()})}result(){var t;return this._done||((t=C.cell)==null?void 0:t.recalcWhen(this,Hi))||void 0}get[Symbol.toStringTag](){return"Job"}restart(){if(!this._done&&P.size){let t=P;P=new Set,this.end(),P=t}else this._end(ve);return this._done=void 0,me.delete(this),this}_end(t){if(this._done)throw new Error("Job already ended");return this!==R&&(this._done=t),this.end(),this}throw(t){return this._done?((wt.get(this)||R).asyncThrow(t),this):this._end(Ct(t))}return(t){return this._end(kt(t))}then(t,e){return ge(this).then(t,e)}catch(t){return ge(this).catch(t)}finally(t){return ge(this).finally(t)}*[Symbol.iterator](){return this._done?Mt(this._done):yield t=>{this.do(e=>ie(Et(t),St(t),e))}}start(t,e){if(!t)return D(this);let s,n;if(k(e))s=e.bind(t);else if(k(t))s=t;else{if(t instanceof J)return t;n=t}let r=D(this);try{if(s&&(n=r.run(s,r)),n!=null)if(k(n))r.must(n);else if(n instanceof Ne)r.run(xt,n,r);else if(n instanceof J)n!==r&&n.do(o=>_t(r,o));else if(n instanceof Promise)n.then(o=>{r.result()||r.return(o)},o=>{r.result()||r.throw(o)});else if(k(n.then))n.then(o=>{r.result()||r.return(o)},o=>{r.result()||r.throw(o)});else if(k(n[Symbol.iterator])&&typeof n!="string")r.run(xt,n,r);else throw new TypeError("Invalid value/return for start()");return r}catch(o){throw r.end(),o}}connect(t,e,s){return this.start(n=>void t(e,n,s))}run(t,...e){let s=y(I(this));try{return t(...e)}finally{Be(y(s))}}bind(t){let e=this;return function(){let s=y(I(e));try{return j(t,this,arguments)}finally{Be(y(s))}}}must(t){return k(t)&&Li(this._chain(),t),this}release(t){if(this===R)return te;let e=this._chain();return(!this._done||e.u)&&(e=e.u||(e.u=yt())),Oi(e,t)}asyncThrow(t){try{($.get(this)||this.throw).call(this,t)}catch(e){this===R?$.set(this,Tt):$.delete(this);let s=$.get(this)||this.throw;s.call(this,t),s.call(this,e)}return this}asyncCatch(t){return k(t)?$.set(this,t):t===null&&$.delete(this),this}_chain(){return this===R&&this.end(),this._done&&Ri(this._cbs)&&g(this.end),this._cbs||(this._cbs=yt())}},me=new WeakMap;function ge(i){return me.has(i)||me.set(i,new Promise((t,e)=>{let s=ie.bind(null,t,e);i.result()?s(i.result()):i.do(s)})),me.get(i)}var D=J.create,R=D(),se=R;R.end=()=>{throw new Error("Can't do that with the detached job")};R.asyncCatch(Tt);var b;At();function At(){b==null||b.end();let i=b=D().asyncCatch(t=>R.asyncThrow(t));return i.release(()=>b===i&&(b=null)),b}function xt(i,t){let e=i[Symbol.iterator](),s=!0,n=I(t),r=0,o=n.job.release(()=>{t=void 0,++r,a("return",void 0)});g(()=>{s=!1,a("next",void 0)});function a(l,u){if(!e)return;if(s)return g(a.bind(null,l,u));let h=y(n);try{s=!0;try{for(;;){++r;let{done:p,value:f}=e[l](u);if(p){t&&t.return(f),t=void 0;break}else if(k(f)){let d=!1,m=!1,x=r;if(f((F,E,de)=>{d||(d=!0,l=F,u=F==="next"?E:de,m&&x===r&&a(F,u))}),m=!0,!d)return}else{l="throw",u=new TypeError("Jobs must yield functions (or yield* Yielding<T>s)");continue}}}catch(p){e=t=void 0,n.job.throw(p)}e=void 0,o==null||o(),o=void 0}finally{y(h),s=!1}}}function Ue(i=Ni){let t=K();return e=>!t.result()&&i.isOpen()?(e&&i.onReady(e,t),i.isReady()):!1}var Se="uneventful/is-stream";function Lt(i=C.job){return new ze(i)}var ze=class{constructor(t){this._job=t,this._callbacks=void 0,this._isReady=!0,this._isPulling=!1}isOpen(){var t;return!((t=this._job)!=null&&t.result())}isReady(){return this.isOpen()&&this._isReady}onReady(t,e){if(!this.isOpen())return this;let s=this._callbacks||(this._callbacks=new Map),n=e.release(()=>s.delete(t));return this.isReady()&&this&&!s.size&&Ft.add(this),s.set(t,n),this}pause(){return this._isReady=!1,this}doPull(){if(this._isPulling)return;let{_callbacks:t}=this;if(t!=null&&t.size){this._isPulling=!0;try{for(let[e,s]of t){if(!this.isReady())break;s(),t.delete(e),e()}}finally{this._isPulling=!1}}}resume(){this.isOpen()&&(this._isReady=!0,this.doPull())}},Ni=Lt();var Ge=new WeakMap;function ke(i=g){return Ge.has(i)||Ge.set(i,G(qi,i)),Ge.get(i)}var _,Rt=new WeakMap,Xe=ke(g),$e;function qi(i){if(!$e){$e=this;try{for(_ of i)_.catchUp(),i.delete(_)}finally{$e=_=void 0}}}var Ce=class extends Error{},Me=class extends Error{},S=1,zi=new WeakMap,It=new WeakMap,Qe=new WeakMap,ne=G(i=>{for(let t of i)t.updateDemand()}),Dt=[];function Ki(i){var e;let t=i.latestSource=S;for(;i;i=Dt.pop())for(let s=i.subscribers;s;s=s.nT){let n=s.tgt;n.latestSource>=t||(n.latestSource=t,(e=n.queue)==null||e.add(n),n.subscribers&&Dt.push(n))}}function Ht(){return this.value}function Ui(){throw this.value}var Q=[],_e;function Gi(i,t){let e=_e;e?(_e=e.old,e.src=i,e.nS=void 0,e.pS=t.sources,e.tgt=t,e.nT=e.pT=void 0,e.ts=i.lastChanged,e.old=i.adding):e={src:i,nS:void 0,pS:t.sources,tgt:t,nT:void 0,pT:void 0,ts:i.lastChanged,old:i.adding},t.sources&&(t.sources.nS=e),t.sources=e,i.adding=e,t.flags&1&&i.subscribe(e)}function Nt(i){let{tgt:t,nS:e,pS:s}=i;i.src.adding===i&&(i.src.adding=i.old),jt(i),t.sources===i&&((t.sources=e||s)||t.becomeConstant())}function jt(i){i.src.unsubscribe(i),i.nS&&(i.nS.pS=i.pS),i.pS&&(i.pS.nS=i.nS),i.src=i.tgt=i.nS=i.pS=i.nT=i.pT=void 0,i.old=_e,_e=i}function Wt(i){for(let t=Jt(i);t;t=t.nS)t.src.subscribe(t);qt(i)}function Bt(i){for(let t=Jt(i);t;t=t.nS)t.src.unsubscribe(t);qt(i)}var $i={};function Jt(i){let t=i.sources;if(t)for(;t.pS;)t=t.pS;return t}function qt(i){((i.flags^=1)&64)===64&&(ne.has(i)?ne.delete(i):ne.add(i))}var A=class{constructor(){this.value=void 0,this.validThrough=0,this.lastChanged=0,this.latestSource=S,this.flags=0,this.ctx=void 0,this.adding=void 0,this.sources=void 0,this.subscribers=void 0,this.queue=void 0,this.compute=Ht}stream(t,e,s){let n=$i;return A.mkRule(()=>{let r=this.getValue();if(r!==n){let o=y(qe);try{t(n=r)}finally{y(o)}}},s?ke(Ue(s)):Xe),Se}getValue(){if(arguments.length)return j(this.stream,this,arguments);let t=C.cell;if(t&&this.flags&132){if(this.flags&32)throw new Me("Cached function dependency cycle");let e=this.adding;!e||e.tgt!==t?(Gi(this,t),e=this.adding):e.ts===-1&&(e.ts=this.lastChanged,e.nS&&(e.nS.pS=e.pS,e.pS&&(e.pS.nS=e.nS),e.nS=void 0,e.pS=t.sources,t.sources.nS=e,t.sources=e)),this.catchUp(),this.adding===e&&(e.ts=this.lastChanged)}else this.catchUp();if(this.flags&16)throw this.value;return this.value}shouldWrite(t){let e=C.cell||_;if(e){if(!e.queue)throw new Ce("Side-effects not allowed outside rules");if(this.adding&&this.adding.tgt===e)throw new Me("Can't update direct dependency");if(this.validThrough===S||this.hasBeenRead())throw new Ce("Value already used")}else if(t)(this.validThrough===S||Q.length)&&(++S,Q.length=0);else return!1;return this.lastChanged===S||Ki(this),this.sources&&(this.sources.ts=0),!0}setValue(t,e){this.shouldWrite(t!==this.value||e!==!!(this.flags&16))&&(this.value=t,this.lastChanged=S,this.flags=e?this.flags|16:this.flags&-17),this.compute=e?Ui:Ht}setCalc(t){this.shouldWrite(t!==this.compute)&&(this.flags|=4,this.compute=t,this.ctx||(this.ctx=I(null,this)))}hasBeenRead(){if(Q.length){for(let t of Q)t.catchUp();Q.length=0}return this.validThrough===S}catchUp(){let{validThrough:t}=this;if(t!==S&&(this.validThrough=S,!!(this.flags&4)))if(this.sources){for(let e=this.sources;e;e=e.nS){let s=e.src;if(e.ts!==s.lastChanged)return this.doRecalc();if(!(s.subscribers&&s.latestSource<=t)&&(s.catchUp(),s.lastChanged>t))return this.doRecalc()}for(let e=this.sources;e;e=e.nS)e.src.validThrough!==S&&Q.push(e.src)}else return this.doRecalc()}doRecalc(){var s,n,r;let{ctx:t}=this,e=y(t);this.flags&64&&((s=t.job)==null||s.restart());for(let o=this.sources;o;o=o.nS)o.ts=-1,o.old=o.src.adding,o.src.adding=o,this.sources=o;this.flags|=32;try{try{let o=this.compute();(o!==this.value||!this.lastChanged||this.flags&16)&&(this.value=o,this.lastChanged=S),this.flags&=-17}catch(o){if(this.flags|=16,this.value=o,this.lastChanged=S,_===this)throw o}}finally{this.flags&=-33,this.flags&64&&(ne.delete(this),this.flags&18?(this.flags&=-3,(n=t.job)==null||n.restart()):this.flags&1||(r=t.job)==null||r.restart()),y(e);let o;for(let a=this.sources;a;){let l=a.pS;a.src.adding=a.old,a.old=void 0,a.ts===-1?jt(a):o=a,a=l}(this.sources=o)||this.becomeConstant()}}becomeConstant(){if(!(this.flags&96)&&(this.flags&=-5,!(this.flags&132))){for(;this.adding;)Nt(this.adding);for(let t=this.subscribers;t;){let{nT:e}=t;t.ts!==this.lastChanged||Nt(t),t=e}}}stop(){var t;this.setQ(null),this.flags&32?this.flags|=2:this.updateDemand(),(t=Rt.get(this))==null||t()}setQ(t=Xe){t!=this.queue&&(this.subscribers||t||!this.queue||Bt(this),this.queue?(this.queue.has(this)&&(t==null||t.add(this)),this.queue.delete(this)):t&&(t.add(this),this.subscribers||Wt(this)),this.queue=t)}subscribe(t){!this.subscribers&&!this.queue&&Wt(this),this.subscribers!==t&&!t.pT&&(t.nT=this.subscribers,this.subscribers&&(this.subscribers.pT=t),this.subscribers=t)}unsubscribe(t){t.nT&&(t.nT.pT=t.pT),t.pT&&(t.pT.nT=t.nT),this.subscribers===t&&(this.subscribers=t.nT),t.nT=t.pT=void 0,!this.subscribers&&!this.queue&&Bt(this)}static mkValue(t){let e=new A;return e.flags=128,e.value=t,e.lastChanged=S,e}isObserved(){return!!((this.flags|=64)&1)}getJob(){var t;return this.flags|=64,(t=this.ctx).job||(t.job=D(b,()=>{var e;(e=this.ctx.job)==null||e.end(),this.ctx.job=void 0}))}updateDemand(){var t;ne.delete(this),Qe.has(this)?Qe.get(this)():this.flags&1?this.shouldWrite(!0):(t=this.ctx.job)==null||t.restart()}static mkStream(t,e){let s=this.mkValue(e);s.flags|=64;let n=I(),r=a=>{s.setValue(a,!1)},o;return Qe.set(s,()=>{if(!s.subscribers){r(e),o==null||o.end();return}if(o)return;o=n.job=D(b).do(l=>{z(l)?s.setValue(q(l),!0):s.setValue(e,!1),n.job=o=void 0});let a=y(n);try{t(r,o)}catch(l){o.end(),b.asyncThrow(l)}finally{y(a)}}),s}recalcWhen(t,e){let s=e?It.get(e)||ee(It,e,new WeakMap):zi,n=s.get(t);if(!n){let r=e?e(t):t,o=0,a=A.mkStream(l=>(r(()=>l(++o)),Se),o);s.set(t,n=a.getValue.bind(a))}n()}static mkCached(t){let e=new A;return e.compute=t,e.ctx=I(null,e),e.flags=4,e}static mkRule(t,e){let s=K(),n=A.mkCached(()=>{try{n.getJob();let o=t();o&&(n.ctx.job||n.getJob()).must(o),n.lastChanged=S}catch(o){throw n.stop(),o}}),r=n.stop.bind(n);return s===se||Rt.set(n,s.release(r)),n.setQ(e),r}};var re=class extends He{constructor(t){super(e=>A.mkRule(e,t))}get stop(){if(_)return _.stop.bind(_);throw new Error("No rule active")}if(t,e){let s=A.mkCached(()=>!!t());return this(()=>s.getValue()?e():void 0)}get method(){let t=this;return(e,s,n)=>n?void(n.value=this.method(n.value)):function(...r){return t(()=>j(e,this,r))}}factory(t){return Vt.get(t)||ee(Vt,t,new re(ke(t)))}detached(t){return se.run(this,t)}root(t){return b.run(this,t)}setScheduler(t){if(_)_.setQ(t?ke(t):Xe);else throw new Error("No rule active")}},Vt=new WeakMap,zt=re.prototype.factory(g);var N=require("obsidian");var Ye="use.me",Ze="use.factory",X,oe,et=function(){return Object.defineProperties(i(),{this:{get(){if(X)return X;throw new TypeError("No current context")}},me:{value:Ye},factory:{value:Ze}});function i(n){let r=new Map;r.prev=n;let o=Object.assign(n?l=>{let u=r.get(l);if(!u){for(let d=r.prev;d;d=d.prev)if(u=d.get(l)){u=Object.assign(Object.assign({},u),{s:u.s||1});break}u=u||{s:2,v:e},r.set(l,u)}let h,p,f;for(;;)switch(u.s){case 0:return X===o&&oe&&oe.push(l),u.v;case 1:if(h=u.d,!h||a(()=>h.k.every(d=>o(d)===h.c(d)))){u.s=0;break}u.v=h.f;case 2:u.s=4;try{t(r,l,0,a(p=u.v,l,f=[])),f.length&&(u.d={c:o,f:p,k:f});break}catch(d){u.s=3,u.v=d,u.d=null}case 3:throw u.v;case 4:throw new Error(`Factory ${String(u.v)} didn't resolve ${String(l)}`)}}:l=>et.this(l),{def(l,u){return t(r,l,2,u),o},set(l,u){return t(r,l,1,u),o},fork(l){let u=i(r);return l!=null?u(l):u}});return n?o.use=o:o;function a(l,u,h){let p=X,f=oe;try{return X=o,oe=h,l(u)}finally{X=p,oe=f}}}function t(n,r,o,a){if(n.has(r)){let l=n.get(r);if(!l.s)throw new Error(`Already read: ${String(r)}`);l.s=o,l.v=a,l.d=null}else n.set(r,{s:o,v:a})}function e(n){if(typeof n[Ye]=="function")return n[Ye](n);if(s(n))return typeof n.prototype[Ze]=="function"?n.prototype[Ze]():new n;throw new ReferenceError(`No config for ${String(n)}`)}function s(n){return typeof n=="function"&&n.prototype!==void 0&&(Object.getPrototypeOf(n.prototype)!==Object.prototype||Object.getOwnPropertyNames(n.prototype).length>1||n.toString().startsWith("class"))}}();function H(i,t){let e=Object.keys(t).map(s=>Xi(i,s,t[s]));return e.length===1?e[0]:function(){e.forEach(s=>s())}}function Xi(i,t,e){let s=i[t],n=i.hasOwnProperty(t),r=n?s:function(){return Object.getPrototypeOf(i)[t].apply(this,arguments)},o=e(r);return s&&Object.setPrototypeOf(o,s),Object.setPrototypeOf(a,o),i[t]=a,l;function a(...u){return o===r&&i[t]===a&&l(),o.apply(this,u)}function l(){i[t]===a&&(n?i[t]=r:delete i[t]),o!==r&&(o=r,Object.setPrototypeOf(a,s||Function))}}var Yi=(i=>typeof require!="undefined"?require:typeof Proxy!="undefined"?new Proxy(i,{get:(t,e)=>(typeof require!="undefined"?require:t)[e]}):i)(function(i){if(typeof require!="undefined")return require.apply(this,arguments);throw Error('Dynamic require of "'+i+'" is not supported')});function ae(i,t,e,s,n){return i.on(t,e,s,n),()=>i.off(t,e,s,n)}var T;(i=>{try{Object.assign(i,Yi("obsidian"))}catch(t){}})(T||(T={}));var v,Zi;function es(i){Zi=i,v=i.app}var it=(i=>(i.service=function(t){return i(Ut).addChild(t),i.this},i.plugin=function(t){if(!U)es(t),U=i.fork(),U.set(T.Plugin,t),U.set(t.constructor,t),t.addChild(U.use(Ut)),t.register(()=>g(b.end));else if(t!==U.use(T.Plugin))throw new TypeError("use.plugin() called on multiple plugins");return U},i.def(T.Plugin,()=>{throw new Error("Plugin not created yet")}),i.def(T.App,()=>i(T.Plugin).app),i))(et),U;var $t=class extends N.Component{constructor(){super(...arguments),this.use=it.service(this)}},Ut=class extends N.Component{constructor(){super(...arguments),this.children=new Set([this])}onload(){this.loaded=!0}onunload(){this.loaded=!1,this.children.clear()}addChild(i){return this.children.has(i)||(this.children.add(i),this.loaded?g(()=>super.addChild(i)):super.addChild(i)),i}};function tt(i,t){g(()=>i.removeChild(t))}function ts(i,t){let e=new T.Component;e.onload=()=>{tt(i,e),i=null,t()},i.addChild(e)}function st(i){let t=v.workspace;switch(i==null?void 0:i.getRoot()){case t.rootSplit:case t.floatingSplit:case t.leftSplit:case t.rightSplit:return!0;default:return!1}}function Qt(i,t=window,e="plugin-"+i.use(T.Plugin).manifest.id.toLowerCase().replace(/[^_a-zA-Z0-9-]/,"-")){let s=t.document.querySelector("body > .app-container"),n=s.find(".status-bar")||s.createDiv("status-bar"),r=n.find(".status-bar-item."+e)||n.createDiv(`status-bar-item ${e.replace(/\./g," ")}`);return s=null,i.register(()=>g(()=>{r=Gt(r),t!==window&&(n=Gt(n))})),i.use(T.Plugin).register(()=>r&&r.detach()),r}function Gt(i){return i&&!i.hasChildNodes()?(i.detach(),null):i}var Xt=class extends N.Component{constructor(i,t){super(),this.use=i,this.container=t,this.win=this.container.win}"use.factory"(){return new is(this.constructor)}static onload(i){}static onunload(i){}},is=class extends $t{constructor(i){super(),this.factory=i,this.instances=new Map,this.watching=!1,this.layoutReadyCallbacks=[]}onload(){var i,t;this.registerEvent(v.workspace.on("layout-change",()=>{v.workspace.layoutReady&&this.layoutReadyCallbacks.length&&(this.layoutReadyCallbacks.forEach(g),this.layoutReadyCallbacks=[])})),(t=(i=this.factory).onload)==null||t.call(i,this.use)}onLeafChange(i,t){return this.onLayoutReady(()=>i.call(t,v.workspace.activeLeaf)),v.workspace.on("active-leaf-change",e=>{v.workspace.layoutReady&&i.call(t,e)})}onLayoutReady(i){v.workspace.layoutReady?g(i):this.layoutReadyCallbacks.push(i)}onunload(){var i,t;(t=(i=this.factory).onunload)==null||t.call(i,this.use)}watch(){if(!this._loaded)ts(this,()=>this.watch());else if(!this.watching){let{workspace:i}=v,t=this;this.watching=!0,this.registerEvent(i.on("window-open",e=>{this.onLayoutReady(()=>this.forContainer(e))})),this.register(H(i,{clearLayout(e){return async function(){try{return await e.call(this)}finally{t.onLayoutReady(()=>t.forAll())}}}})),this.onLayoutReady(()=>this.forAll())}return this}forWindow(i=(e=>(e=window.activeWindow)!=null?e:window)(),t=!0){let e=ns(i);if(e)return this.forContainer(e,t)}forContainer(i,t=!0){i=i.getContainer();let e=this.instances.get(i);return!e&&t&&(e=new this.factory(this.use,i),e&&(this.instances.set(i,e),this.addChild(e),i.component.addChild(e),e.register(()=>{tt(this,e),tt(i.component,e),this.instances.delete(i)}))),e}forDom(i,t=!0){return this.forWindow(nt(i),t)}forLeaf(i=v.workspace.activeLeaf,t=!0){if(st(i))return this.forContainer(i.getContainer(),t)}forView(i,t=!0){return this.forLeaf(i.leaf,t)}forAll(i=!0){return ss().map(t=>this.forContainer(t,i)).filter(t=>t)}};function ss(){return[v.workspace.rootSplit].concat(v.workspace.floatingSplit.children)}function nt(i){return i.win||(i.ownerDocument||i).defaultView||window}function ns(i){if(i===window)return v.workspace.rootSplit;let{floatingSplit:t}=v.workspace;if(t){for(let e of t.children)if(i===e.win)return e}}var Yt=class extends $t{onload(){let i=this,t=this.use(T.Plugin);this.register(H(t,{loadCSS(e){return async function(){await e.call(this),i.triggerReparse(),this.register(()=>i.triggerReparse())}}}))}triggerReparse(){v.workspace.layoutReady&&v.workspace.trigger("parse-style-settings")}};var En=zt.method;var Zt={};function le(i,t,e=[],s={}){typeof e=="string"&&(e=[e]),typeof e=="object"&&e.key&&(e=[e]);let n=e.map(function(o){if(typeof o=="object")return o;let a=o.split("+");return{modifiers:a,key:a.pop()||"+"}});Object.assign(s,{id:i,name:t,hotkeys:n});let r=Symbol("cmd:"+i);return Zt[r]=s,r}function ei(i,t=i.constructor.prototype){Object.getOwnPropertySymbols(t).forEach(e=>{let s=Zt[e],n=t[e];s&&i.addCommand(Object.assign({},s,{checkCallback(r){let o=n.call(i);return r||typeof o!="function"?!!o:(o(),!0)}}))})}function rs(i){for(var t=i.split(/([#.])/),e="",s="",n=[],r=0;r<t.length;r++){var o=t[r];o==="#"?s=t[++r]:o==="."?n.push(t[++r]):o.length&&(e=o)}return{tag:e||"div",id:s,className:n.join(" ")}}function oi(i,t){var e=rs(i),s=e.tag,n=e.id,r=e.className,o=t?document.createElementNS(t,s):document.createElement(s);return n&&(o.id=n),r&&(t?o.setAttribute("class",r):o.className=r),o}function W(i,t){var e=w(i),s=w(t);return t===s&&s.__redom_view&&(t=s.__redom_view),s.parentNode&&(ai(t,s,e),e.removeChild(s)),t}function ai(i,t,e){var s=t.__redom_lifecycle;if(ti(s)){t.__redom_lifecycle={};return}var n=e;for(t.__redom_mounted&&Te(t,"onunmount");n;){var r=n.__redom_lifecycle||{};for(var o in s)r[o]&&(r[o]-=s[o]);ti(r)&&(n.__redom_lifecycle=null),n=n.parentNode}}function ti(i){if(i==null)return!0;for(var t in i)if(i[t])return!1;return!0}var os=["onmount","onremount","onunmount"],as=typeof window!="undefined"&&"ShadowRoot"in window;function B(i,t,e,s){var n=w(i),r=w(t);t===r&&r.__redom_view&&(t=r.__redom_view),t!==r&&(r.__redom_view=t);var o=r.__redom_mounted,a=r.parentNode;return o&&a!==n&&ai(t,r,a),e!=null?s?n.replaceChild(r,w(e)):n.insertBefore(r,w(e)):n.appendChild(r),ls(t,r,n,a),t}function Te(i,t){t==="onmount"||t==="onremount"?i.__redom_mounted=!0:t==="onunmount"&&(i.__redom_mounted=!1);var e=i.__redom_lifecycle;if(e){var s=i.__redom_view,n=0;s&&s[t]&&s[t]();for(var r in e)r&&n++;if(n)for(var o=i.firstChild;o;){var a=o.nextSibling;Te(o,t),o=a}}}function ls(i,t,e,s){for(var n=t.__redom_lifecycle||(t.__redom_lifecycle={}),r=e===s,o=!1,a=0,l=os;a<l.length;a+=1){var u=l[a];r||i!==t&&u in i&&(n[u]=(n[u]||0)+1),n[u]&&(o=!0)}if(!o){t.__redom_lifecycle={};return}var h=e,p=!1;for((r||h&&h.__redom_mounted)&&(Te(t,r?"onremount":"onmount"),p=!0);h;){var f=h.parentNode,d=h.__redom_lifecycle||(h.__redom_lifecycle={});for(var m in n)d[m]=(d[m]||0)+n[m];if(p)break;(h.nodeType===Node.DOCUMENT_NODE||as&&h instanceof ShadowRoot||f&&f.__redom_mounted)&&(Te(h,r?"onremount":"onmount"),p=!0),h=f}}function us(i,t,e){var s=w(i);if(typeof t=="object")for(var n in t)ii(s,n,t[n]);else ii(s,t,e)}function ii(i,t,e){e==null?i.style[t]="":i.style[t]=e}var si="http://www.w3.org/1999/xlink";function li(i,t,e,s){var n=w(i),r=typeof t=="object";if(r)for(var o in t)li(n,o,t[o],s);else{var a=n instanceof SVGElement,l=typeof e=="function";if(t==="style"&&typeof e=="object")us(n,e);else if(a&&l)n[t]=e;else if(t==="dataset")ci(n,e);else if(!a&&(t in n||l)&&t!=="list")n[t]=e;else{if(a&&t==="xlink"){ui(n,e);return}s&&t==="class"&&(e=n.className+" "+e),e==null?n.removeAttribute(t):n.setAttribute(t,e)}}}function ui(i,t,e){if(typeof t=="object")for(var s in t)ui(i,s,t[s]);else e!=null?i.setAttributeNS(si,t,e):i.removeAttributeNS(si,t,e)}function ci(i,t,e){if(typeof t=="object")for(var s in t)ci(i,s,t[s]);else e!=null?i.dataset[t]=e:delete i.dataset[t]}function hi(i){return document.createTextNode(i!=null?i:"")}function ot(i,t,e){for(var s=0,n=t;s<n.length;s+=1){var r=n[s];if(!(r!==0&&!r)){var o=typeof r;o==="function"?r(i):o==="string"||o==="number"?i.appendChild(hi(r)):Fe(w(r))?B(i,r):r.length?ot(i,r,e):o==="object"&&li(i,r,null,e)}}}function fi(i){return typeof i=="string"?ue(i):w(i)}function w(i){return i.nodeType&&i||!i.el&&i||w(i.el)}function Fe(i){return i&&i.nodeType}var ni={};function ue(i){for(var t=[],e=arguments.length-1;e-- >0;)t[e]=arguments[e+1];var s,n=typeof i;if(n==="string")s=di(i).cloneNode(!1);else if(Fe(i))s=i.cloneNode(!1);else if(n==="function"){var r=i;s=new(Function.prototype.bind.apply(r,[null].concat(t)))}else throw new Error("At least one argument required");return ot(w(s),t,!0),s}var ce=ue;ue.extend=function(t){for(var e=[],s=arguments.length-1;s-- >0;)e[s]=arguments[s+1];var n=di(t);return ue.bind.apply(ue,[this,n].concat(e))};function di(i){return ni[i]||(ni[i]=oi(i))}function pi(i){for(var t=[],e=arguments.length-1;e-- >0;)t[e]=arguments[e+1];for(var s=w(i),n=mi(i,t,s.firstChild);n;){var r=n.nextSibling;W(i,n),n=r}}function mi(i,t,e){for(var s=e,n=new Array(t.length),r=0;r<t.length;r++)n[r]=t[r]&&w(t[r]);for(var o=0;o<t.length;o++){var a=t[o];if(a){var l=n[o];if(l===s){s=s.nextSibling;continue}if(Fe(l)){var u=s&&s.nextSibling,h=a.__redom_index!=null,p=h&&u===n[o+1];B(i,a,s,p),p&&(s=u);continue}a.length!=null&&(s=mi(i,a,s))}}return s}var gi=function(t,e,s){this.View=t,this.initData=s,this.oldLookup={},this.lookup={},this.oldViews=[],this.views=[],e!=null&&(this.key=typeof e=="function"?e:cs(e))};gi.prototype.update=function(t,e){for(var s=this,n=s.View,r=s.key,o=s.initData,a=r!=null,l=this.lookup,u={},h=new Array(t.length),p=this.views,f=0;f<t.length;f++){var d=t[f],m=void 0;if(a){var x=r(d);m=l[x]||new n(o,d,f,t),u[x]=m,m.__redom_id=x}else m=p[f]||new n(o,d,f,t);m.update&&m.update(d,f,t,e);var F=w(m.el);F.__redom_view=m,h[f]=m}this.oldViews=p,this.views=h,this.oldLookup=l,this.lookup=u};function cs(i){return function(t){return t[i]}}function at(i,t,e,s){return new Y(i,t,e,s)}var Y=function(t,e,s,n){this.View=e,this.initData=n,this.views=[],this.pool=new gi(e,s,n),this.el=fi(t),this.keySet=s!=null};Y.prototype.update=function(t,e){t===void 0&&(t=[]);var s=this,n=s.keySet,r=this.views;this.pool.update(t,e);var o=this.pool,a=o.views,l=o.lookup;if(n)for(var u=0;u<r.length;u++){var h=r[u],p=h.__redom_id;l[p]==null&&(h.__redom_index=null,W(this,h))}for(var f=0;f<a.length;f++){var d=a[f];d.__redom_index=f}pi(this,a),n&&(this.lookup=l),this.views=a};Y.extend=function(t,e,s,n){return Y.bind(Y,t,e,s,n)};at.extend=Y.extend;var hs=function(t,e){this.el=hi(""),this.visible=!1,this.view=null,this._placeholder=this.el,t instanceof Node?this._el=t:t.el instanceof Node?(this._el=t,this.view=t):this._View=t,this._initData=e};hs.prototype.update=function(t,e){var s=this._placeholder,n=this.el.parentNode;if(t){if(!this.visible)if(this._el)B(n,this._el,s),W(n,s),this.el=w(this._el),this.visible=t;else{var r=this._View,o=new r(this._initData);this.el=w(o),this.view=o,B(n,o,s),W(n,s)}this.view&&this.view.update&&this.view.update(e)}else if(this.visible){if(this._el){B(n,s,this._el),W(n,this._el),this.el=s,this.visible=t;return}B(n,s,this.view),W(n,this.view),this.el=s,this.view=null}this.visible=t};var fs=function(t,e,s){this.el=fi(t),this.Views=e,this.initData=s};fs.prototype.update=function(t,e){if(t!==this.route){var s=this.Views,n=s[t];this.route=t,n&&(n instanceof Node||n.el instanceof Node)?this.view=n:this.view=n&&new n(this.initData,e),pi(this.el,[this.view])}this.view&&this.view.update&&this.view.update(e,t)};var vi="http://www.w3.org/2000/svg",ri={};function rt(i){for(var t=[],e=arguments.length-1;e-- >0;)t[e]=arguments[e+1];var s,n=typeof i;if(n==="string")s=wi(i).cloneNode(!1);else if(Fe(i))s=i.cloneNode(!1);else if(n==="function"){var r=i;s=new(Function.prototype.bind.apply(r,[null].concat(t)))}else throw new Error("At least one argument required");return ot(w(s),t,!0),s}rt.extend=function(t){var e=wi(t);return rt.bind(this,e)};rt.ns=vi;function wi(i){return ri[i]||(ri[i]=oi(i,vi))}var lt=class extends c.MenuItem{setTitle(e){return this.title=typeof e=="string"?e:e.textContent,super.setTitle(e)}},L=class extends c.Menu{constructor(e,s=e instanceof c.App?e:e.app){var r;super(s);this.parent=e;this.app=s;this.match="";this.resetSearchOnTimeout=(0,c.debounce)(()=>{this.match=""},1500,!0);this.visible=!1;(r=this.setUseNativeMenu)==null||r.call(this,!1),e instanceof L&&e.setChildMenu(this),this.scope=new c.Scope,this.scope.register([],"ArrowUp",this.onArrowUp.bind(this)),this.scope.register(["Mod"],"k",this.onArrowUp.bind(this)),this.scope.register([],"ArrowDown",this.onArrowDown.bind(this)),this.scope.register(["Mod"],"j",this.onArrowDown.bind(this)),this.scope.register([],"Enter",this.onEnter.bind(this)),this.scope.register([],"Escape",this.onEscape.bind(this)),this.scope.register([],"ArrowLeft",this.onArrowLeft.bind(this)),this.scope.register(["Mod"],"h",this.onArrowLeft.bind(this)),this.scope.register([],"Home",this.onHome.bind(this)),this.scope.register([],"End",this.onEnd.bind(this)),this.scope.register([],"ArrowRight",this.onArrowRight.bind(this)),this.scope.register(["Mod"],"l",this.onArrowRight.bind(this));let n=this;H(this.dom,{contains(o){return function(a){var u;return o.call(this,a)||((u=n.child)==null?void 0:u.dom.contains(a))}}}),this.dom.addClass("qe-popup-menu"),this.onMouseOver&&this.dom.removeEventListener("mouseover",this.onMouseOver)}onEscape(){return this.hide(),!1}onload(){this.scope.register(null,null,this.onKeyDown.bind(this)),super.onload(),this.visible=!0,this.showSelected();let e,s;this.register(ps(this.dom,"mouseover",".menu-item",(n,r)=>{(e!==n.clientX||s!==n.clientY)&&!r.hasClass("is-disabled")&&!this.child&&this.onItemHover(this.items.find(o=>o.dom===r),n,r),e=n.clientX,s=n.clientY}))}onItemHover(e,s,n){this.select(this.items.indexOf(e),!1)}onunload(){this.visible=!1,super.onunload()}addItem(e){let s=new lt(this);return this.items.push(s),e(s),this._loaded&&this.sort&&this.sort(),this}onKeyDown(e){let s=c.Keymap.getModifiers(e);if(e.key.length===1&&!e.isComposing&&(!s||s==="Shift")){let n=this.match+e.key;for(;n&&!this.searchFor(n);)n=n.slice(1);this.match=n,this.resetSearchOnTimeout()}return!1}searchFor(e){let s=e.split("").map(ds);return this.find(new RegExp("^"+s.join(""),"ui"))||this.find(new RegExp("^"+s.join(".*"),"ui"))||this.find(new RegExp(s.join(".*"),"ui"))}find(e){var n,r,o;let s=Math.min(0,this.selected);for(let a=this.items.length;a;++s,a--)if(!((n=this.items[s])!=null&&n.disabled)&&(o=(r=this.items[s])==null?void 0:r.title)!=null&&o.match(e))return this.select(s),!0;return!1}onEnter(e){let s=this.items[this.selected];return s&&(s.handleEvent(e),this.child||this.hide()),!1}select(e,s=!0){this.match="",super.select(e),s&&this.showSelected()}showSelected(){var s;let e=(s=this.items[this.selected])==null?void 0:s.dom;if(e){let n=this.dom.getBoundingClientRect(),r=e.getBoundingClientRect();(r.top<n.top||r.bottom>n.bottom)&&e.scrollIntoView()}}unselect(){var e;(e=this.items[this.selected])==null||e.dom.removeClass("selected")}onEnd(e){return this.unselect(),this.selected=this.items.length,this.onArrowUp(e),this.selected===this.items.length&&(this.selected=-1),!1}onHome(e){return this.unselect(),this.selected=-1,this.onArrowDown(e),!1}onArrowLeft(){return this.rootMenu()!==this&&this.hide(),!1}onArrowRight(){return!1}hide(){return this.setChildMenu(),super.hide()}setChildMenu(e){var s;(s=this.child)==null||s.hide(),this.child=e}rootMenu(){return this.parent instanceof c.App?this:this.parent.rootMenu()}cascade(e,s,n,r=15,o=5){var mt,gt;let{left:a,top:l,bottom:u,width:h}=e.getBoundingClientRect(),p=Math.max(0,a+(e.matchParent(".menu")?Math.min(150,h/3):0)),f=(mt=window.activeWindow)!=null?mt:window,{innerHeight:d,innerWidth:m}=f,x={x:s?s.clientX-r:p,y:u-o};(gt=this.sort)==null||gt.call(this),f.document.body.appendChild(this.dom);let{offsetWidth:F,offsetHeight:E}=this.dom,de=x.y+E<d,Ei=l-o-E>0,Si=x.x+F<=m;return de||(Ei?x.y=l-o:x.y=u>d-(u-l)?l+o:d),Si||(x.x=E<u-o||de?m:p),this.showAtPosition(x),e.toggleClass("selected",!0),this.register(()=>{this.parent instanceof c.App?e.toggleClass("selected",!1):this.parent instanceof L&&this.parent.setChildMenu(),n&&n()}),this}};function ds(i){return i.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function ps(i,t,e,s,n=!1){return i.on(t,e,s,n),()=>i.off(t,e,s,n)}function Pe(i){return i18next.t(`plugins.file-explorer.menu-opt-${i}`)}var O=class extends L{constructor(t,e){super(t);let{workspace:s}=this.app,n=this.app.internalPlugins.plugins["file-explorer"].enabled;this.addSections(["title","open","action","view","info","system","","danger"]),e instanceof c.TFolder&&(this.addItem(r=>r.setTitle(Pe("new-note")).setIcon("create-new").onClick(async o=>{this.rootMenu().hide();let a=await this.app.fileManager.createNewMarkdownFile(e);a&&await this.app.workspace.getLeaf(c.Keymap.isModEvent(o)).openFile(a,{active:!0,state:{mode:"source"},eState:{rename:"all"}})})),this.addItem(r=>r.setTitle(Pe("new-folder")).setIcon("folder").setDisabled(!n).onClick(o=>{var a;n?(this.rootMenu().hide(),(a=this.withExplorer(e))==null||a.createAbstractFile("folder",e)):(new c.Notice("The File Explorer core plugin must be enabled to create new folders"),o.stopPropagation())})),this.addItem(r=>r.setTitle("Set as attachment folder").setIcon("image-file").onClick(()=>{this.app.setAttachmentFolder(e)})),this.addSeparator()),this.addItem(r=>{r.setTitle(Pe("rename")).setIcon("pencil").onClick(()=>{this.app.fileManager.promptForFileRename(e)}),r.setSection("danger")}),this.addItem(r=>r.setTitle(Pe("delete")).setIcon("trash").onClick(()=>{e instanceof c.TFolder?this.app.fileManager.promptForFolderDeletion(e):e instanceof c.TFile&&this.app.fileManager.promptForFileDeletion(e)}).setSection("danger").dom.classList.add("is-warning")),e instanceof c.TFolder&&n&&this.addItem(r=>r.setIcon("folder").setTitle(i18next.t("plugins.file-explorer.action-reveal-file")).onClick(()=>{this.rootMenu().hide(),this.withExplorer(e)})),s.trigger("file-menu",this,e,"file-explorer-context-menu")}onEnter(t){return this.rootMenu().hide(),super.onEnter(t)}withExplorer(t){let e=this.app.internalPlugins.plugins["file-explorer"];if(e.enabled)return e.instance.revealInFolder(t),this.app.workspace.getLeavesOfType("file-explorer")[0].view}};var ct={markdown:"document",image:"image-file",audio:"audio-file",pdf:"pdf-file"},ms={...ct,excalidraw:"excalidraw-icon"};function Ae(i){var t;if(i instanceof c.TFolder)return"folder";if(i instanceof c.TFile){let e=app.viewRegistry.getTypeByExtension(i.extension);if(e)return(t=ms[e])!=null?t:"document"}}function Le(i){return app.vault.getAbstractFileByPath(gs(i))}function gs(i){return`${i.path}/${i.name}.md`}var ut=new Intl.Collator(void 0,{usage:"sort",sensitivity:"base",numeric:!0}).compare;function ht(i,t=app.vault.getConfig("showUnsupportedFiles")){let{children:e}=i,s=Le(i),n=e.slice().sort((a,l)=>ut(a.name,l.name)),r=n.filter(a=>a instanceof c.TFolder),o=n.filter(a=>a instanceof c.TFile&&a!==s&&(t||Ae(a)));return r.sort((a,l)=>ut(a.name,l.name)),o.sort((a,l)=>ut(a.basename,l.basename)),{folderNote:s,folders:r,files:o}}function yi(i,t){let{folderNote:e,folders:s,files:n}=ht(i,!1);return(e?[e]:[]).concat(s,n)}function bi(i,t,e){let s=new Set;for(;i!=null&&i.parent&&!s.has(i);){s.add(i);let n=yi(i.parent,!1),r=n.indexOf(i);if(r===-1)return;for(e?r+=t:r=t<0?0:n.length-1,i=i.parent;r>=0&&r<n.length;){if(i=n[r],i instanceof c.TFile)return i;i instanceof c.TFolder?(n=yi(i,!1),r=t>0?0:n.length-1):r+=t}}}var M=!0,V=class extends L{constructor(e,s,n,r){super(e);this.parent=e;this.folder=s;this.selectedFile=n;this.crumb=r;this.parentFolder=this.parent instanceof V?this.parent.folder:null;this.fileCount=e=>e instanceof c.TFolder?e.children.map(this.fileCount).reduce((s,n)=>s+n,0):Ae(e)?1:0;this.refreshFiles=(0,c.debounce)(()=>this.loadFiles(this.folder,this.currentFile()),100,!0);this.showPopover=(0,c.debounce)(()=>{var e;this.hidePopover(),M&&this.maybeHover((e=this.currentItem())==null?void 0:e.dom,s=>this.app.workspace.trigger("link-hover",this,nt(this.dom).document.body,s.path,""))},50,!0);this.onItemClick=(e,s)=>{if(e.type==="auxclick"&&!c.Keymap.isModEvent(e))return;let n=this.fileForDom(s);if(n&&!this.onClickFile(n,s,e))return e.stopPropagation(),e.preventDefault(),!1};this.onItemMenu=(e,s)=>{let n=this.fileForDom(s);if(n){let r=this.itemForPath(n.path);r>=0&&this.selected!=r&&this.select(r),new O(this,n).cascade(s,e),e.stopPropagation()}};this.loadFiles(s,n),this.scope.register([],"Tab",this.togglePreviewMode.bind(this)),this.scope.register(["Mod"],"Enter",this.onEnter.bind(this)),this.scope.register(["Alt"],"Enter",this.onKeyboardContextMenu.bind(this)),this.scope.register(null,"Enter",this.onEnter.bind(this)),this.scope.register([],"\\",this.onKeyboardContextMenu.bind(this)),this.scope.register([],"ContextMenu",this.onKeyboardContextMenu.bind(this)),this.scope.register([],"F2",this.doRename.bind(this)),this.scope.register(["Shift"],"F2",this.doMove.bind(this)),this.scope.register([],"PageUp",this.doScroll.bind(this,-1,!1)),this.scope.register([],"PageDown",this.doScroll.bind(this,1,!1)),this.scope.register(["Mod"],"Home",this.doScroll.bind(this,0,!0)),this.scope.register(["Mod"],"End",this.doScroll.bind(this,1,!0));let{dom:o}=this,a=".menu-item[data-file-path]";o.on("click",a,this.onItemClick,!0),o.on("auxclick",a,this.onItemClick,!0),o.on("contextmenu",a,this.onItemMenu),o.on("mousedown",a,u=>{u.stopPropagation()},!0),o.on("dragstart",a,(u,h)=>{ft(this.app,h.dataset.filePath,u)}),this.register(()=>{M&&this.parent instanceof V&&this.parent.showPopover()});let l=this;H(this.dom,{contains(u){return function(h){var f;return u.call(this,h)||((f=l._popover)==null?void 0:f.hoverEl.contains(h))}}})}onArrowLeft(){var e;return super.onArrowLeft(),this.rootMenu()===this&&this.openBreadcrumb((e=this.crumb)==null?void 0:e.prev()),!1}onKeyboardContextMenu(e){var r;if(e.code==="ContextMenu"){let o=function(a){if(a.code==="ContextMenu")return a.preventDefault(),a.view.removeEventListener("keyup",o,{capture:!0}),!1};e.view.addEventListener("keyup",o,{capture:!0})}let s=(r=this.items[this.selected])==null?void 0:r.dom,n=s&&this.fileForDom(s);return n&&new O(this,n).cascade(s),!1}doScroll(e,s,n){var a,l;let r=(a=this.hoverPopover)==null?void 0:a.hoverEl,o=r==null?void 0:r.find((l=this.hoverPopover)!=null&&l.rootSplit?'[data-mode="preview"] .markdown-preview-view, [data-mode="source"] .cm-scroller':".markdown-preview-view");if(o){o.style.scrollBehavior=s?"auto":"smooth";let u=o.scrollTop,h=(s?0:o.scrollTop)+e*(s?o.scrollHeight:o.clientHeight);o.scrollTop=h,s||(h>=o.scrollHeight?this.onArrowDown(n):h<0&&(u>0?o.scrollTop=0:this.onArrowUp(n)))}else M?e>0?this.onArrowDown(n):this.onArrowUp(n):(M=!0,this.showPopover());return!1}doRename(){let e=this.currentFile();return this.rootMenu().hide(),e&&this.app.fileManager.promptForFileRename(e),!1}doMove(){if(!this.app.internalPlugins.plugins["file-explorer"].enabled)return new c.Notice("File explorer core plugin must be enabled to move files or folders"),!1;this.rootMenu().hide();let s=this.currentFile(),n=H(c.Modal.prototype,{open(r){return function(){return n(),this.files&&Array.isArray(this.files)&&this.files.length&&this.files[0]instanceof c.TFile&&(this.files=[s]),r.call(this)}}});return this.app.commands.executeCommandById("file-explorer:move-file"),n(),!1}currentItem(){return this.items[this.selected]}currentFile(){var e;return this.fileForDom((e=this.currentItem())==null?void 0:e.dom)}fileForDom(e){let{filePath:s}=e==null?void 0:e.dataset;if(s)return this.app.vault.getAbstractFileByPath(s)}itemForPath(e){return this.items.findIndex(s=>s.dom.dataset.filePath===e)}openBreadcrumb(e){if(e&&this.rootMenu()===this)return this.hide(),e.open(),!1}onArrowRight(){var s;let e=this.currentFile();if(e instanceof c.TFolder)e!==this.selectedFile?this.onClickFile(e,this.currentItem().dom):this.openBreadcrumb((s=this.crumb)==null?void 0:s.next());else if(e instanceof c.TFile){let n=this.hoverPopover;n&&n.rootSplit&&this.app.workspace.iterateLeaves(r=>{var o;return r.view instanceof c.FileView&&r.view.file===e&&(n.togglePin(!0),this._popover=null,this.onEscape(),r.view instanceof c.MarkdownView?r.setViewState({type:r.view.getViewType(),state:{file:e.path,mode:"source"}}).then(()=>this.app.workspace.setActiveLeaf(r,!1,!0)):((o=this.dom.ownerDocument.activeElement)==null||o.blur(),this.app.workspace.setActiveLeaf(r,!1,!0))),!0},n.rootSplit)}return!1}loadFiles(e,s){this.items.forEach(a=>a.dom.detach()),this.items=[];let{folderNote:n,folders:r,files:o}=ht(e);n&&this.addFile(n),r.length&&(n&&this.addSeparator(),r.map(this.addFile,this)),o.length&&((r.length||n)&&this.addSeparator(),o.map(this.addFile,this)),this.select(s?this.itemForPath(s.path):0)}addFile(e){let s=Ae(e);this.addItem(n=>{if(n.setTitle(e.name),n.dom.dataset.filePath=e.path,n.dom.setAttr("draggable","true"),n.dom.addClass(e instanceof c.TFolder?"is-qe-folder":"is-qe-file"),s&&n.setIcon(s),e instanceof c.TFile)n.setTitle(e.basename),e.extension!=="md"&&n.dom.createDiv({text:e.extension,cls:["nav-file-tag","qe-extension"]});else if(e!==this.folder.parent){let r=this.fileCount(e);r&&n.dom.createDiv({text:""+r,cls:"nav-file-tag qe-file-count"})}n.onClick(r=>this.onClickFile(e,n.dom,r))})}togglePreviewMode(){return(M=!M)?this.showPopover():this.hidePopover(),!1}onload(){super.onload(),this.register(ae(this.dom.ownerDocument.body,"pointerdown",".hover-popover",(e,s)=>{var n,r,o;((n=this.hoverPopover)==null?void 0:n.hoverEl)===s&&((o=(r=this.hoverPopover).togglePin)==null||o.call(r,!0),this._popover=null)},{capture:!0})),this.registerEvent(this.app.vault.on("create",e=>{this.folder===e.parent&&this.refreshFiles()})),this.registerEvent(this.app.vault.on("rename",(e,s)=>{if(this.folder===e.parent){let n=this.itemForPath(s)>=0?e:this.currentFile();this.loadFiles(this.folder,n)}else this.removeItemForPath(s)})),this.registerEvent(this.app.vault.on("delete",e=>this.removeItemForPath(e.path))),M&&this.selected!=-1&&this.showPopover()}removeItemForPath(e){let s=this.itemForPath(e);if(s<0)return;let n=this.items[s];this.selected>s&&(this.selected-=1),n.dom.detach(),this.items.remove(n)}onEscape(){return super.onEscape(),this.parent instanceof L&&this.parent.onEscape(),!1}hide(){return this.hidePopover(),super.hide()}setChildMenu(e){super.setChildMenu(e),M&&this.canShowPopover()&&this.showPopover()}select(e,s=!0){let n=this.selected;super.select(e,s),n!==this.selected&&(M?this.showPopover():this.hidePopover())}hidePopover(){this.hoverPopover=null}canShowPopover(){return!this.child&&this.visible}onItemHover(e,s,n){super.onItemHover(e,s,n),n.matches(".menu-item[data-file-path]")&&(M||this.maybeHover(n,r=>this.app.workspace.trigger("hover-link",{event:s,source:he,hoverParent:this,targetEl:n,linktext:r.path})))}maybeHover(e,s){if(!this.canShowPopover())return;let n=this.fileForDom(e);n instanceof c.TFolder&&(n=Le(n)),n instanceof c.TFile&&ct[this.app.viewRegistry.getTypeByExtension(n.extension)]&&s(n)}get hoverPopover(){return this._popover}set hoverPopover(e){var r;let s=this._popover;if(e===s)return;s&&e!==s&&(this._popover=null,s.onHover=s.onTarget=!1,(!s.isPinned||M)&&s.hide()),e&&!this.canShowPopover()&&(e.onHover=!1,e.hide(),e=null),this._popover=e;let n=e==null?void 0:e.targetEl;if(n&&n===n.ownerDocument.body&&(n.removeEventListener("mouseover",e.onMouseIn),n.removeEventListener("mouseout",e.onMouseOut)),M&&e&&this.currentItem()){(r=e.togglePin)==null||r.call(e,!1),Promise.resolve().then(()=>{var a,l;return(l=(a=e.abortController)==null?void 0:a.unload)==null?void 0:l.call(a)});let o=()=>{var m;let a=e.hoverEl;if(!a.parentElement)return;let l=this.dom.getBoundingClientRect(),u=this.currentItem().dom.getBoundingClientRect(),h=a.offsetParent||this.dom.ownerDocument.documentElement,p=a.offsetHeight,f=Math.min(l.right+2,h.clientWidth-a.offsetWidth),d=Math.min(Math.max(0,u.top-p/8),h.clientHeight-p);f<l.left+l.width/3&&l.left>a.offsetWidth&&(f=l.left-a.offsetWidth),e.position({x:f,y:d}),a.style.top=d+"px",a.style.left=f+"px",(m=e.togglePin)==null||m.call(e,!0)};"onShowCallback"in e?H(e,{onShowCallback(a){return()=>(e.hoverEl.win.requestAnimationFrame(o),a==null?void 0:a.call(e))}}):this.dom.win.requestAnimationFrame(o)}}onClickFile(e,s,n){var o;this.hidePopover();let r=this.itemForPath(e.path);if(r>=0&&this.selected!=r&&this.select(r),e instanceof c.TFile){if(this.app.viewRegistry.isExtensionRegistered(e.extension))return this.app.workspace.openLinkText(e.path,"",n&&c.Keymap.isModEvent(n)||!1),this.rootMenu().hide(),n==null||n.stopPropagation(),!0;new c.Notice(`.${e.extension} files cannot be opened in Obsidian; Use "Open in Default App" to open them externally`)}else e===this.selectedFile?this.openBreadcrumb((o=this.crumb)==null?void 0:o.next()):new V(this,e,Le(e)).cascade(s,n instanceof MouseEvent?n:void 0)}};var he="quick-explorer:folder-menu";function ft(i,t,e){if(!t||t==="/")return;let s=i.vault.getAbstractFileByPath(t);if(!s)return;let{dragManager:n}=i,r=s instanceof c.TFile?n.dragFile(e,s):n.dragFolder(e,s);n.onDragStart(e,r)}var pt=class{constructor(){this.nameEl=ce("span",{class:"explorable-name"});this.sepEl=ce("span",{class:"explorable-separator"});this.el=ce("span",{draggable:!0,class:"explorable titlebar-button"},this.nameEl,this.sepEl)}update(t,e,s){var a,l;let{file:n,path:r}=t,o=n.name||r;this.sepEl.toggle(e<s.length-1),this.nameEl.textContent=o,this.el.dataset.parentPath=(l=(a=n.parent)==null?void 0:a.path)!=null?l:"/",this.el.dataset.filePath=r}},Z=class extends Xt{constructor(){super(...arguments);this.lastFile=null;this.lastPath=null;this.el=ce("div",{id:"quick-explorer"});this.list=at(this.el,pt);this.isOpen=0;this.app=app}onload(){var s,n;if(this.register(()=>{var r;return(r=this.lastMenu)==null?void 0:r.hide()}),(0,c.requireApiVersion)("0.15.6")){let r=this.win.document.body.find(".titlebar .titlebar-inner .titlebar-text"),o=r==null?void 0:r.cloneNode(!0);o&&(o.addClass("qe-replacement"),o.textContent=(n=(s=app.getAppTitle)==null?void 0:s.call(app))!=null?n:this.win.document.title,r.replaceWith(o),this.register(()=>o.replaceWith(r)))}if((0,c.requireApiVersion)("0.16.0")&&this.win.document.body.addClass("obsidian-themepocalypse"),(0,c.requireApiVersion)("0.16.3")){let r=".view-header .view-header-breadcrumb, .view-header .view-header-title-parent";this.register(ae(this.win.document.body,"click",r,(o,a)=>{var l;if(!o.target.matches(".view-header-breadcrumb-separator, .is-exploring"))return(l=dt(a))==null||l.open(o),o.stopPropagation(),!1},{capture:!0})),this.register(ae(this.win.document.body,"contextmenu",".view-header .view-header-breadcrumb",(o,a)=>{var u,h;if(o.target.matches(".view-header-breadcrumb-separator"))return;let l=(h=(u=dt(a))==null?void 0:u.file)==null?void 0:h.parent;if(l)return new O(this.app,l).cascade(a,o),o.stopImmediatePropagation(),!1},{capture:!0}))}let e=this.win.document.body.find("body:not(.is-hidden-frameless) .titlebar .titlebar-button-container.mod-left")||Qt(this,this.win,"left-region");if(this.register(()=>W(e,this)),B(e,this),this.isCurrent())this.update(this.app.workspace.getActiveFile());else{let r=app.workspace.getMostRecentLeaf(this.container),o=(r==null?void 0:r.view)instanceof c.FileView&&r.view.file;o&&this.update(o)}this.registerEvent(this.app.vault.on("rename",this.onFileChange,this)),this.registerEvent(this.app.vault.on("delete",this.onFileDelete,this)),this.el.on("contextmenu",".explorable",(r,o)=>{let{filePath:a}=o.dataset,l=this.app.vault.getAbstractFileByPath(a);new O(this.app,l).cascade(o,r)}),this.el.on("click",".explorable",(r,o)=>{this.folderMenu(o,r.isTrusted&&r)}),this.el.on("dragstart",".explorable",(r,o)=>{ft(this.app,o.dataset.filePath,r)})}onFileChange(e){e===this.lastFile&&this.update(e)}onFileDelete(e){e===this.lastFile&&this.update()}visibleCrumb(e){let s=vs(this,e);if(!e.isShown()){let n=app.workspace.getActiveViewOfType(c.View).containerEl.find(".view-header .view-header-title-parent");if(n!=null&&n.isShown()){let{file:r}=s;s=dt(n),s=s.peers.find(o=>o.file===r)||s}}return s}folderMenu(e=this.el.firstElementChild,s){var n;return this.lastMenu=(n=this.visibleCrumb(e))==null?void 0:n.open(s)}browseVault(){return this.folderMenu()}browseCurrent(){return this.folderMenu(this.el.lastElementChild)}browseFile(e){if(e===this.lastFile)return this.browseCurrent();let s,n=this.el.firstElementChild,r=[],o=e.path.split("/").filter(a=>a);for(;n&&o.length;){if(r.push(o[0]),n.dataset.filePath!==r.join("/")){s=this.folderMenu(n),r.pop();break}o.shift(),n=n.nextElementSibling}for(;s&&o.length;){r.push(o.shift());let a=s.itemForPath(r.join("/"));if(a==-1)break;s.select(a),(o.length||e instanceof c.TFolder)&&(s.onArrowRight(),s=s.child)}return s}isCurrent(){return this===this.use(Z).forLeaf(app.workspace.activeLeaf)}update(e){if(this.isOpen||(e!=null||(e=this.app.vault.getAbstractFileByPath("/")),e==this.lastFile&&e.path==this.lastPath))return;this.lastFile=e,this.lastPath=e.path;let s=xi(e);this.list.update(s)}},fe=class{constructor(t,e,s,n,r){this.peers=t;this.el=e;this.file=s;this.onOpen=n;this.onClose=r;t.push(this)}next(){let t=this.peers.indexOf(this);if(t>-1)return this.peers[t+1]}prev(){let t=this.peers.indexOf(this);if(t>0)return this.peers[t-1]}open(t){var s;let e=this.file;if(e){(s=this.onOpen)==null||s.call(this,this);let n=this.file.parent||e;return new V(app,n,e,this).cascade(this.el,t&&t.isTrusted&&t,()=>this.onClose(this))}}};function dt(i){var p,f,d,m,x,F;let t=[],e=i.matchParent(".workspace-leaf"),s,n;app.workspace.iterateAllLeaves(E=>E.containerEl===e&&(s=E)&&!0);let r=app.vault.getAbstractFileByPath("/"),o=(f=(p=s==null?void 0:s.view)==null?void 0:p.file)!=null?f:r,a=xi(o),l=i.matchParent(".view-header-title-parent");n=new fe(t,l,(m=(d=a.shift())==null?void 0:d.file)!=null?m:r,u,h);for(let E of l.findAll(".view-header-breadcrumb"))new fe(t,E,(F=(x=a.shift())==null?void 0:x.file)!=null?F:r,u,h),E===i&&(n=t[t.length-1]);return n;function u(E){E.el.toggleClass("is-exploring",!0)}function h(E){E.el.toggleClass("is-exploring",!1)}}function vs(i,t){let e=[],s=t.matchParent("#quick-explorer"),n;for(let a of s.findAll(".explorable"))new fe(e,a,app.vault.getAbstractFileByPath(a.dataset.filePath),r,o),a===t&&(n=e[e.length-1]);return n;function r(){i.isOpen++}function o(){i.isOpen--,i.lastMenu=null,!i.isOpen&&i.isCurrent()&&i.update(app.workspace.getActiveFile())}}function xi(i){let t=[];for(;i;)t.unshift({file:i,path:i.path}),i=i.parent;return t.length>1&&t.shift(),t}var Oe=class extends c.Plugin{constructor(){super(...arguments);this.use=it.plugin(this);this.explorers=this.use(Z).watch();this.ss=this.use(Yt)}updateCurrent(e=this.app.workspace.activeLeaf,s=this.app.workspace.getActiveFile()){st(e)&&this.explorers.forLeaf(e).update(s)}onload(){this.app.workspace.registerHoverLinkSource(he,{display:"Quick Explorer",defaultMod:!0}),this.registerEvent(this.app.workspace.on("file-open",()=>this.updateCurrent())),this.registerEvent(this.explorers.onLeafChange(e=>this.updateCurrent(e))),this.addCommand({id:"browse-vault",name:"Browse vault",callback:()=>{var e;(e=this.explorers.forWindow())==null||e.browseVault()}}),this.addCommand({id:"browse-current",name:"Browse current folder",callback:()=>{var e;(e=this.explorers.forWindow())==null||e.browseCurrent()}}),ei(this),this.registerEvent(this.app.workspace.on("file-menu",(e,s,n)=>{let r;if(e instanceof O||e.addItem(o=>{var a;o.setIcon("folder").setTitle("Show in Quick Explorer").onClick(l=>{var u;(u=this.explorers.forDom(r.dom))==null||u.browseFile(s)}),r=o,(a=r.setSection)==null||a.call(r,"system")}),r){let o=i18next.t("plugins.file-explorer.action-reveal-file"),a=e.items.findIndex(l=>{var u;return((u=l.titleEl)==null?void 0:u.textContent)===o});a>-1&&(e.sections||e.dom.insertBefore(r.dom,e.items[a].dom),e.items.remove(r),e.items.splice(a,0,r))}})),Object.defineProperty(c.TFolder.prototype,"basename",{get(){return this.name},configurable:!0})}[le("go-next","Go to next file in folder")](){return this.goFile(1,!0)}[le("go-prev","Go to previous file in folder")](){return this.goFile(-1,!0)}[le("go-first","Go to first file in folder")](){return this.goFile(-1,!1)}[le("go-last","Go to last file in folder")](){return this.goFile(1,!1)}goFile(e,s){return()=>{let n=v.workspace.getActiveFile(),r=n&&bi(n,e,s);r&&r!==n&&v.workspace.getLeaf().openFile(r)}}onunload(){this.app.workspace.unregisterHoverLinkSource(he)}};

/* nosourcemap */