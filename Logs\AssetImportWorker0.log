Using pre-set license
Built from '2022.3/china_unity/release' branch; Version is '2022.3.33f1c1 (ea5182f68133) revision 15356290'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'zh' Physical Memory: 16280 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
D:\Program Files (x86)\Unity\Unity 3D\2022.3.33f1c1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
F:/The Lightless Crown
-logFile
Logs/AssetImportWorker0.log
-srvPort
60736
Successfully changed project path to: F:/The Lightless Crown
F:/The Lightless Crown
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [16660] Host "[IP] *********** [Port] 0 [Flags] 2 [Guid] 1826184575 [EditorId] 1826184575 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-NEVNEH0) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [16660] Host "[IP] *********** [Port] 0 [Flags] 2 [Guid] 1826184575 [EditorId] 1826184575 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-NEVNEH0) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
Refreshing native plugins compatible for Editor in 800.37 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.33f1c1 (ea5182f68133)
[Subsystems] Discovering subsystems at path D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path F:/The Lightless Crown/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 960 (ID=0x1401)
    Vendor:   NVIDIA
    VRAM:     4040 MB
    Driver:   32.0.15.6094
Initialize mono
Mono path[0] = 'D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/Managed'
Mono path[1] = 'D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56872
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: D:/Program Files (x86)/Unity/Unity 3D/2022.3.33f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.132398 seconds.
- Loaded All Assemblies, in  5.274 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.815 seconds
Domain Reload Profiling: 9012ms
	BeginReloadAssembly (1329ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (4ms)
	RebuildCommonClasses (800ms)
	RebuildNativeTypeToScriptingClass (152ms)
	initialDomainReloadingComplete (969ms)
	LoadAllAssembliesAndSetupDomain (1942ms)
		LoadAssemblies (1236ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1920ms)
			TypeCache.Refresh (1903ms)
				TypeCache.ScanAssembly (1728ms)
			ScanForSourceGeneratedMonoScriptInfo (3ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (3821ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3037ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (174ms)
			SetLoadedEditorAssemblies (95ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (37ms)
			ProcessInitializeOnLoadAttributes (2101ms)
			ProcessInitializeOnLoadMethodAttributes (629ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 12.218 seconds
Refreshing native plugins compatible for Editor in 177.71 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 21.050 seconds
Domain Reload Profiling: 33153ms
	BeginReloadAssembly (2205ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (124ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (201ms)
	RebuildCommonClasses (445ms)
	RebuildNativeTypeToScriptingClass (178ms)
	initialDomainReloadingComplete (827ms)
	LoadAllAssembliesAndSetupDomain (8441ms)
		LoadAssemblies (6167ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (3515ms)
			TypeCache.Refresh (3013ms)
				TypeCache.ScanAssembly (2813ms)
			ScanForSourceGeneratedMonoScriptInfo (309ms)
			ResolveRequiredComponents (185ms)
	FinalizeReload (21056ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (17639ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (106ms)
			SetLoadedEditorAssemblies (146ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (1134ms)
			ProcessInitializeOnLoadAttributes (8405ms)
			ProcessInitializeOnLoadMethodAttributes (7605ms)
			AfterProcessingInitializeOnLoad (241ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (58ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 1.60 seconds
Refreshing native plugins compatible for Editor in 159.64 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5649 Unused Serialized files (Serialized files now loaded: 0)
Unloading 81 unused Assets / (351.7 KB). Loaded Objects now: 6117.
Memory consumption went from 219.6 MB to 219.2 MB.
Total: 21.996200 ms (FindLiveObjects: 0.944200 ms CreateObjectMapping: 1.589000 ms MarkObjects: 18.255300 ms  DeleteObjects: 1.205600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 234864.523410 seconds.
  path: Assets/Scripts/Map/Editor
  artifactKey: Guid(2b39fb344561905418b188a70e8277f0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Map/Editor using Guid(2b39fb344561905418b188a70e8277f0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '78424a34ad427b47de27d51fca33141c') in 0.018674 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 2936.748693 seconds.
  path: Assets/Resources/Data/mapData
  artifactKey: Guid(1f6e3b228cdae234bbaa5ab0e868fe1c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/mapData using Guid(1f6e3b228cdae234bbaa5ab0e868fe1c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'b22d40f56e14fe9f1cb8805f8621d706') in 0.046303 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  8.919 seconds
Refreshing native plugins compatible for Editor in 213.19 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 13.626 seconds
Domain Reload Profiling: 22525ms
	BeginReloadAssembly (1442ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (106ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (577ms)
	RebuildCommonClasses (89ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (187ms)
	LoadAllAssembliesAndSetupDomain (7157ms)
		LoadAssemblies (6287ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1278ms)
			TypeCache.Refresh (283ms)
				TypeCache.ScanAssembly (20ms)
			ScanForSourceGeneratedMonoScriptInfo (678ms)
			ResolveRequiredComponents (252ms)
	FinalizeReload (13627ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (4851ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (78ms)
			SetLoadedEditorAssemblies (32ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (658ms)
			ProcessInitializeOnLoadAttributes (2990ms)
			ProcessInitializeOnLoadMethodAttributes (1055ms)
			AfterProcessingInitializeOnLoad (38ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (47ms)
Refreshing native plugins compatible for Editor in 21.97 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5486 Unused Serialized files (Serialized files now loaded: 0)
Unloading 56 unused Assets / (321.6 KB). Loaded Objects now: 6125.
Memory consumption went from 190.5 MB to 190.2 MB.
Total: 70.758200 ms (FindLiveObjects: 3.938900 ms CreateObjectMapping: 7.044300 ms MarkObjects: 59.574300 ms  DeleteObjects: 0.198700 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.237 seconds
Refreshing native plugins compatible for Editor in 25.90 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  5.668 seconds
Domain Reload Profiling: 8879ms
	BeginReloadAssembly (1598ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (15ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (404ms)
	RebuildCommonClasses (88ms)
	RebuildNativeTypeToScriptingClass (35ms)
	initialDomainReloadingComplete (178ms)
	LoadAllAssembliesAndSetupDomain (1311ms)
		LoadAssemblies (1769ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (151ms)
			TypeCache.Refresh (58ms)
				TypeCache.ScanAssembly (3ms)
			ScanForSourceGeneratedMonoScriptInfo (68ms)
			ResolveRequiredComponents (24ms)
	FinalizeReload (5669ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2894ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (25ms)
			SetLoadedEditorAssemblies (7ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (168ms)
			ProcessInitializeOnLoadAttributes (1635ms)
			ProcessInitializeOnLoadMethodAttributes (1026ms)
			AfterProcessingInitializeOnLoad (33ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (36ms)
Refreshing native plugins compatible for Editor in 24.21 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5486 Unused Serialized files (Serialized files now loaded: 0)
Unloading 56 unused Assets / (320.7 KB). Loaded Objects now: 6129.
Memory consumption went from 190.5 MB to 190.2 MB.
Total: 8.814900 ms (FindLiveObjects: 0.735100 ms CreateObjectMapping: 0.402700 ms MarkObjects: 7.534900 ms  DeleteObjects: 0.140500 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 8991.132313 seconds.
  path: Assets/Scripts/Map/Editor/MapGeneratorEditor.cs
  artifactKey: Guid(976a45a44cc9afd49b0e7968007b988f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Map/Editor/MapGeneratorEditor.cs using Guid(976a45a44cc9afd49b0e7968007b988f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '2aa53d8c0ac2eec4f093b319d17ede7b') in 0.006562 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 12.554 seconds
Refreshing native plugins compatible for Editor in 218.07 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 18.661 seconds
Domain Reload Profiling: 30852ms
	BeginReloadAssembly (3042ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (163ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (989ms)
	RebuildCommonClasses (928ms)
	RebuildNativeTypeToScriptingClass (113ms)
	initialDomainReloadingComplete (1326ms)
	LoadAllAssembliesAndSetupDomain (6780ms)
		LoadAssemblies (7829ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (311ms)
			TypeCache.Refresh (106ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (2ms)
			ResolveRequiredComponents (201ms)
	FinalizeReload (18662ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3419ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (15ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (198ms)
			ProcessInitializeOnLoadAttributes (1549ms)
			ProcessInitializeOnLoadMethodAttributes (1612ms)
			AfterProcessingInitializeOnLoad (38ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (56ms)
Refreshing native plugins compatible for Editor in 26.67 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5486 Unused Serialized files (Serialized files now loaded: 0)
Unloading 56 unused Assets / (320.6 KB). Loaded Objects now: 6133.
Memory consumption went from 190.5 MB to 190.2 MB.
Total: 8.204200 ms (FindLiveObjects: 0.676200 ms CreateObjectMapping: 0.558300 ms MarkObjects: 6.801700 ms  DeleteObjects: 0.166400 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.138 seconds
Refreshing native plugins compatible for Editor in 28.44 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 42.606 seconds
Domain Reload Profiling: 44721ms
	BeginReloadAssembly (817ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (117ms)
	RebuildCommonClasses (122ms)
	RebuildNativeTypeToScriptingClass (99ms)
	initialDomainReloadingComplete (175ms)
	LoadAllAssembliesAndSetupDomain (899ms)
		LoadAssemblies (1236ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (41ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (28ms)
	FinalizeReload (42608ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (36565ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (15ms)
			SetLoadedEditorAssemblies (10ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (189ms)
			ProcessInitializeOnLoadAttributes (1845ms)
			ProcessInitializeOnLoadMethodAttributes (34155ms)
			AfterProcessingInitializeOnLoad (349ms)
			EditorAssembliesLoaded (2ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (864ms)
Refreshing native plugins compatible for Editor in 346.80 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5486 Unused Serialized files (Serialized files now loaded: 0)
Unloading 56 unused Assets / (320.7 KB). Loaded Objects now: 6137.
Memory consumption went from 190.5 MB to 190.2 MB.
Total: 68.569000 ms (FindLiveObjects: 10.266700 ms CreateObjectMapping: 1.468300 ms MarkObjects: 56.633200 ms  DeleteObjects: 0.199000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 10.321 seconds
Refreshing native plugins compatible for Editor in 120.25 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 50.777 seconds
Domain Reload Profiling: 60976ms
	BeginReloadAssembly (2951ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (98ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1089ms)
	RebuildCommonClasses (1172ms)
	RebuildNativeTypeToScriptingClass (172ms)
	initialDomainReloadingComplete (393ms)
	LoadAllAssembliesAndSetupDomain (5509ms)
		LoadAssemblies (6379ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (390ms)
			TypeCache.Refresh (68ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (2ms)
			ResolveRequiredComponents (314ms)
	FinalizeReload (50779ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (24101ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (193ms)
			SetLoadedEditorAssemblies (59ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (2867ms)
			ProcessInitializeOnLoadAttributes (10684ms)
			ProcessInitializeOnLoadMethodAttributes (10129ms)
			AfterProcessingInitializeOnLoad (170ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (327ms)
Refreshing native plugins compatible for Editor in 334.94 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5486 Unused Serialized files (Serialized files now loaded: 0)
Unloading 56 unused Assets / (321.6 KB). Loaded Objects now: 6141.
Memory consumption went from 190.5 MB to 190.2 MB.
Total: 225.183600 ms (FindLiveObjects: 118.469500 ms CreateObjectMapping: 13.559000 ms MarkObjects: 84.972000 ms  DeleteObjects: 8.181300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 574.328109 seconds.
  path: Assets/Scripts/Map/Noise.cs
  artifactKey: Guid(b1a317b782dbab0498bf53a5e6b1ad5a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Map/Noise.cs using Guid(b1a317b782dbab0498bf53a5e6b1ad5a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '28c2b5db38267542a5d03b37b38ca4bf') in 0.021226 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.605 seconds
Refreshing native plugins compatible for Editor in 58.38 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  7.095 seconds
Domain Reload Profiling: 9652ms
	BeginReloadAssembly (739ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (22ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (204ms)
	RebuildCommonClasses (151ms)
	RebuildNativeTypeToScriptingClass (40ms)
	initialDomainReloadingComplete (173ms)
	LoadAllAssembliesAndSetupDomain (1452ms)
		LoadAssemblies (1673ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (165ms)
			TypeCache.Refresh (31ms)
				TypeCache.ScanAssembly (6ms)
			ScanForSourceGeneratedMonoScriptInfo (88ms)
			ResolveRequiredComponents (43ms)
	FinalizeReload (7097ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2506ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (27ms)
			SetLoadedEditorAssemblies (14ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (238ms)
			ProcessInitializeOnLoadAttributes (1162ms)
			ProcessInitializeOnLoadMethodAttributes (1036ms)
			AfterProcessingInitializeOnLoad (29ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (41ms)
Refreshing native plugins compatible for Editor in 28.35 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5482 Unused Serialized files (Serialized files now loaded: 0)
Unloading 59 unused Assets / (322.2 KB). Loaded Objects now: 6142.
Memory consumption went from 190.4 MB to 190.1 MB.
Total: 9.048800 ms (FindLiveObjects: 0.884800 ms CreateObjectMapping: 0.634300 ms MarkObjects: 7.332100 ms  DeleteObjects: 0.196000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.138 seconds
Refreshing native plugins compatible for Editor in 113.32 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  6.813 seconds
Domain Reload Profiling: 9924ms
	BeginReloadAssembly (870ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (18ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (284ms)
	RebuildCommonClasses (104ms)
	RebuildNativeTypeToScriptingClass (53ms)
	initialDomainReloadingComplete (161ms)
	LoadAllAssembliesAndSetupDomain (1922ms)
		LoadAssemblies (1847ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (560ms)
			TypeCache.Refresh (35ms)
				TypeCache.ScanAssembly (12ms)
			ScanForSourceGeneratedMonoScriptInfo (143ms)
			ResolveRequiredComponents (371ms)
	FinalizeReload (6814ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2871ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (28ms)
			SetLoadedEditorAssemblies (11ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (205ms)
			ProcessInitializeOnLoadAttributes (1335ms)
			ProcessInitializeOnLoadMethodAttributes (1219ms)
			AfterProcessingInitializeOnLoad (73ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (31ms)
Refreshing native plugins compatible for Editor in 30.59 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5491 Unused Serialized files (Serialized files now loaded: 0)
Unloading 57 unused Assets / (497.2 KB). Loaded Objects now: 6154.
Memory consumption went from 191.7 MB to 191.2 MB.
Total: 54.458700 ms (FindLiveObjects: 2.169200 ms CreateObjectMapping: 1.603200 ms MarkObjects: 48.876600 ms  DeleteObjects: 1.807700 ms)

Prepare: number of updated asset objects reloaded= 2
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 17.424 seconds
Refreshing native plugins compatible for Editor in 101.07 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 13.759 seconds
Domain Reload Profiling: 31155ms
	BeginReloadAssembly (2352ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (33ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (210ms)
	RebuildCommonClasses (11933ms)
	RebuildNativeTypeToScriptingClass (51ms)
	initialDomainReloadingComplete (293ms)
	LoadAllAssembliesAndSetupDomain (2758ms)
		LoadAssemblies (4540ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (171ms)
			TypeCache.Refresh (53ms)
				TypeCache.ScanAssembly (10ms)
			ScanForSourceGeneratedMonoScriptInfo (74ms)
			ResolveRequiredComponents (39ms)
	FinalizeReload (13768ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (7928ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (386ms)
			SetLoadedEditorAssemblies (33ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (1060ms)
			ProcessInitializeOnLoadAttributes (4648ms)
			ProcessInitializeOnLoadMethodAttributes (1769ms)
			AfterProcessingInitializeOnLoad (31ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (169ms)
Refreshing native plugins compatible for Editor in 36.62 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5490 Unused Serialized files (Serialized files now loaded: 0)
Unloading 57 unused Assets / (0.5 MB). Loaded Objects now: 6158.
Memory consumption went from 191.5 MB to 190.9 MB.
Total: 34.814900 ms (FindLiveObjects: 2.119000 ms CreateObjectMapping: 3.324300 ms MarkObjects: 25.714900 ms  DeleteObjects: 3.655500 ms)

Prepare: number of updated asset objects reloaded= 1
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 1285.761481 seconds.
  path: Assets/Scripts/Map/Editor/MapGeneratorEditor.cs
  artifactKey: Guid(976a45a44cc9afd49b0e7968007b988f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Map/Editor/MapGeneratorEditor.cs using Guid(976a45a44cc9afd49b0e7968007b988f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'a58e463aeb2abd375c28ef928ad0f3c0') in 0.015851 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Scripts/Map/MapGeneratorTest.cs
  artifactKey: Guid(fdada8672f27e65409efc6aeccd04191) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Map/MapGeneratorTest.cs using Guid(fdada8672f27e65409efc6aeccd04191) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'cb895103e0490ea379a36a8c56cedb5e') in 0.004704 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.001582 seconds.
  path: Assets/Scripts/Map/Shaders/TerrainVisualization.shader
  artifactKey: Guid(a7398d9889a05d74b9a9d61971f8fe2e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Map/Shaders/TerrainVisualization.shader using Guid(a7398d9889a05d74b9a9d61971f8fe2e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '86becf3b9c6a948683652f0632c68f6e') in 0.071108 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.005872 seconds.
  path: Assets/Scripts/Map/MapGenerator.cs
  artifactKey: Guid(560e877002418ca488102c786fbeb00a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Map/MapGenerator.cs using Guid(560e877002418ca488102c786fbeb00a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'b39066e6b05feca49925581e006f46a3') in 0.001291 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000222 seconds.
  path: Assets/Scripts/Map/TerrainType.cs
  artifactKey: Guid(7131b2498109db94986797133e925991) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Map/TerrainType.cs using Guid(7131b2498109db94986797133e925991) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '8db2ea4202d802d835dfc5a65efa35d0') in 0.001386 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Scripts/Map/NoiseGenerator.cs
  artifactKey: Guid(3adef845f6245fd449dba9e74596938a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Map/NoiseGenerator.cs using Guid(3adef845f6245fd449dba9e74596938a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '42c1f3b949a56736ffc302ad92319466') in 0.001396 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000493 seconds.
  path: Assets/Scripts/Map/MapData.cs
  artifactKey: Guid(c2e890035133eb64cb754a6994cc4b33) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Map/MapData.cs using Guid(c2e890035133eb64cb754a6994cc4b33) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '048c42c49aac99408f0a001a978a1b1a') in 0.001553 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Scripts/Map/README.md
  artifactKey: Guid(1bbb964548ea70a41a4f5d7b425440f2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Map/README.md using Guid(1bbb964548ea70a41a4f5d7b425440f2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '675a8fd6fdd40bac20c07c46964847b1') in 0.001741 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.758 seconds
Refreshing native plugins compatible for Editor in 25.41 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  5.551 seconds
Domain Reload Profiling: 8283ms
	BeginReloadAssembly (1387ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (96ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (408ms)
	RebuildCommonClasses (100ms)
	RebuildNativeTypeToScriptingClass (29ms)
	initialDomainReloadingComplete (140ms)
	LoadAllAssembliesAndSetupDomain (1075ms)
		LoadAssemblies (1568ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (53ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (31ms)
	FinalizeReload (5552ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2739ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (9ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (199ms)
			ProcessInitializeOnLoadAttributes (1415ms)
			ProcessInitializeOnLoadMethodAttributes (998ms)
			AfterProcessingInitializeOnLoad (98ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (38ms)
Refreshing native plugins compatible for Editor in 23.71 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5490 Unused Serialized files (Serialized files now loaded: 0)
Unloading 57 unused Assets / (0.5 MB). Loaded Objects now: 6163.
Memory consumption went from 191.5 MB to 190.9 MB.
Total: 9.102200 ms (FindLiveObjects: 0.963900 ms CreateObjectMapping: 0.700700 ms MarkObjects: 6.909100 ms  DeleteObjects: 0.527400 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 196.327200 seconds.
  path: Assets/TextMesh Pro/Resources/Fonts & Materials/LiberationSans SDF - Drop Shadow.mat
  artifactKey: Guid(e73a58f6e2794ae7b1b7e50b7fb811b0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Resources/Fonts & Materials/LiberationSans SDF - Drop Shadow.mat using Guid(e73a58f6e2794ae7b1b7e50b7fb811b0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'ebb3875d8beb626ef7a25aeab8553c4b') in 3.214753 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 22
========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/TextMesh Pro/Resources/Fonts & Materials/LiberationSans SDF - Outline.mat
  artifactKey: Guid(79459efec17a4d00a321bdcc27bbc385) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Resources/Fonts & Materials/LiberationSans SDF - Outline.mat using Guid(79459efec17a4d00a321bdcc27bbc385) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'dba801ea5411005784cc34c95d889baf') in 0.285967 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/TextMesh Pro/Resources/Fonts & Materials/LiberationSans SDF - Fallback.asset
  artifactKey: Guid(2e498d1c8094910479dc3e1b768306a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Resources/Fonts & Materials/LiberationSans SDF - Fallback.asset using Guid(2e498d1c8094910479dc3e1b768306a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'f85f3c567bc56e6b824408e5894ef837') in 0.217488 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 7
========================================================================
Received Import Request.
  Time since last request: 2.543701 seconds.
  path: Assets/Scripts/Map/Shaders
  artifactKey: Guid(e4a4a0d36d4c60e49aafdcffad97d016) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Map/Shaders using Guid(e4a4a0d36d4c60e49aafdcffad97d016) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '5544f5706b3830cf7322bb110f378437') in 0.005041 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  8.503 seconds
Refreshing native plugins compatible for Editor in 194.04 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 39.971 seconds
Domain Reload Profiling: 48295ms
	BeginReloadAssembly (2902ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (255ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (285ms)
	RebuildCommonClasses (191ms)
	RebuildNativeTypeToScriptingClass (36ms)
	initialDomainReloadingComplete (860ms)
	LoadAllAssembliesAndSetupDomain (4326ms)
		LoadAssemblies (5803ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (317ms)
			TypeCache.Refresh (84ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (7ms)
			ResolveRequiredComponents (196ms)
	FinalizeReload (39979ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (18173ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (97ms)
			SetLoadedEditorAssemblies (31ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (1228ms)
			ProcessInitializeOnLoadAttributes (9691ms)
			ProcessInitializeOnLoadMethodAttributes (6937ms)
			AfterProcessingInitializeOnLoad (189ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (202ms)
Refreshing native plugins compatible for Editor in 169.87 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5490 Unused Serialized files (Serialized files now loaded: 0)
Unloading 57 unused Assets / (0.5 MB). Loaded Objects now: 6249.
Memory consumption went from 196.3 MB to 195.8 MB.
Total: 59.774000 ms (FindLiveObjects: 3.264700 ms CreateObjectMapping: 14.506500 ms MarkObjects: 41.083300 ms  DeleteObjects: 0.917400 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 310.802088 seconds.
  path: Assets/Scripts/Map/New Map Data.asset
  artifactKey: Guid(471684fa6a70a1b48bdd190cafaf9f36) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Map/New Map Data.asset using Guid(471684fa6a70a1b48bdd190cafaf9f36) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '254188ddd1c5c2303d2160d3708cbce1') in 0.065028 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 32.001389 seconds.
  path: Assets/Scripts/Map/Map Data.asset
  artifactKey: Guid(471684fa6a70a1b48bdd190cafaf9f36) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Map/Map Data.asset using Guid(471684fa6a70a1b48bdd190cafaf9f36) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '1b581fc2cf184ac0e32023aa1e2b6e38') in 0.013090 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 2.637543 seconds.
  path: Assets/Scripts/Map/MapDisplay.cs
  artifactKey: Guid(17f993dbf7d58104b9ad9d2b151fe1d4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Map/MapDisplay.cs using Guid(17f993dbf7d58104b9ad9d2b151fe1d4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'eaabc69552222ab3da8384ccd1019d94') in 0.005506 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 13.396570 seconds.
  path: Assets/Scripts/Map/MapData.asset
  artifactKey: Guid(471684fa6a70a1b48bdd190cafaf9f36) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Map/MapData.asset using Guid(471684fa6a70a1b48bdd190cafaf9f36) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '3857d11d66af3708dc9aa6400908a785') in 0.007420 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  9.943 seconds
Refreshing native plugins compatible for Editor in 55.12 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  7.377 seconds
Domain Reload Profiling: 17165ms
	BeginReloadAssembly (3178ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (71ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (860ms)
	RebuildCommonClasses (724ms)
	RebuildNativeTypeToScriptingClass (122ms)
	initialDomainReloadingComplete (721ms)
	LoadAllAssembliesAndSetupDomain (5041ms)
		LoadAssemblies (6846ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (59ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (1ms)
			ResolveRequiredComponents (39ms)
	FinalizeReload (7378ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3033ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (31ms)
			SetLoadedEditorAssemblies (11ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (248ms)
			ProcessInitializeOnLoadAttributes (1441ms)
			ProcessInitializeOnLoadMethodAttributes (1268ms)
			AfterProcessingInitializeOnLoad (35ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (44ms)
Refreshing native plugins compatible for Editor in 24.75 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5490 Unused Serialized files (Serialized files now loaded: 0)
Unloading 57 unused Assets / (498.4 KB). Loaded Objects now: 6253.
Memory consumption went from 196.2 MB to 195.7 MB.
Total: 5.580400 ms (FindLiveObjects: 0.611800 ms CreateObjectMapping: 0.400200 ms MarkObjects: 4.236900 ms  DeleteObjects: 0.330100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  9.298 seconds
Refreshing native plugins compatible for Editor in 266.65 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 42.699 seconds
Domain Reload Profiling: 51964ms
	BeginReloadAssembly (2608ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (17ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (619ms)
	RebuildCommonClasses (536ms)
	RebuildNativeTypeToScriptingClass (370ms)
	initialDomainReloadingComplete (649ms)
	LoadAllAssembliesAndSetupDomain (5096ms)
		LoadAssemblies (6491ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (364ms)
			TypeCache.Refresh (82ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (7ms)
			ResolveRequiredComponents (265ms)
	FinalizeReload (42704ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (21443ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (113ms)
			SetLoadedEditorAssemblies (97ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (1390ms)
			ProcessInitializeOnLoadAttributes (9376ms)
			ProcessInitializeOnLoadMethodAttributes (10204ms)
			AfterProcessingInitializeOnLoad (262ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (453ms)
Refreshing native plugins compatible for Editor in 31.18 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5490 Unused Serialized files (Serialized files now loaded: 0)
Unloading 57 unused Assets / (0.5 MB). Loaded Objects now: 6257.
Memory consumption went from 196.2 MB to 195.7 MB.
Total: 7.230300 ms (FindLiveObjects: 0.564900 ms CreateObjectMapping: 0.406700 ms MarkObjects: 5.917300 ms  DeleteObjects: 0.339600 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 264.649289 seconds.
  path: Assets/Scripts/Map/MapData.asset
  artifactKey: Guid(471684fa6a70a1b48bdd190cafaf9f36) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Map/MapData.asset using Guid(471684fa6a70a1b48bdd190cafaf9f36) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '2f263988f8753f0e349139575b35fd9a') in 3.420615 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 15.863650 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Align.png
  artifactKey: Guid(41237ce7afab42fc90c974ef4240b244) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Align.png using Guid(41237ce7afab42fc90c974ef4240b244) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'b32ba2931b5989dd39791d0db8b54af6') in 5.995904 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.001066 seconds.
  path: Assets/Plugins/Demigiant/DemiLib/Core/Editor/Imgs/blackSquareAlpha80.png
  artifactKey: Guid(595b166389c8db546a199430284ebc9b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Demigiant/DemiLib/Core/Editor/Imgs/blackSquareAlpha80.png using Guid(595b166389c8db546a199430284ebc9b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '0897a9628d757893d8579ce33a632aeb') in 0.019745 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000214 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Custom-Pivot-Rotation-Tool-Pro.png
  artifactKey: Guid(6a2ded3d288bf8042832068eaac3eddc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Custom-Pivot-Rotation-Tool-Pro.png using Guid(6a2ded3d288bf8042832068eaac3eddc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '933370b99778d77ea142a847e72956e7') in 0.098844 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.032841 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/DebugPro.png
  artifactKey: Guid(29196bae2cc9485bbf8bcca95bb06061) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/DebugPro.png using Guid(29196bae2cc9485bbf8bcca95bb06061) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '1ea749914fc553e7c099f0530f5d9ebd') in 0.024824 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.002673 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Favorite Windows.png
  artifactKey: Guid(1ebbd9bab95442c3834cc21ce7a889f7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Favorite Windows.png using Guid(1ebbd9bab95442c3834cc21ce7a889f7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'bc6754c2ebad9d28e702087d6f9d6858') in 0.084685 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000227 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Anchor.png
  artifactKey: Guid(bd8b72d7aee147c6869d460c121d1fb0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Anchor.png using Guid(bd8b72d7aee147c6869d460c121d1fb0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'a4e62b54b829a2fa38b06af8b494e158') in 0.033466 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000251 seconds.
  path: Assets/Plugins/Demigiant/DemiLib/Core/Editor/Imgs/blackSquareAlpha50.png
  artifactKey: Guid(aa1d42dc6ff3e894da2208c6929d2165) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Demigiant/DemiLib/Core/Editor/Imgs/blackSquareAlpha50.png using Guid(aa1d42dc6ff3e894da2208c6929d2165) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '85555109b131425b6f8c270fe3336c81') in 0.022181 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000531 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Drag and Drop to Canvas.png
  artifactKey: Guid(4d27e5ca3ae947b596f0b40ddae45aa1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Drag and Drop to Canvas.png using Guid(4d27e5ca3ae947b596f0b40ddae45aa1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'a39e0a75ecb86b2e41679b409ff5d314') in 0.059194 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Best Icons For GameObject.png
  artifactKey: Guid(a2742138dbcc49d8a76204305164e595) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Best Icons For GameObject.png using Guid(a2742138dbcc49d8a76204305164e595) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '435bdf7720a82685ac7552bd11517348') in 0.039619 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000161 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/DuplicateTool.png
  artifactKey: Guid(e60aa38a051446f3a6260ee2f3fc695b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/DuplicateTool.png using Guid(e60aa38a051446f3a6260ee2f3fc695b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '28bd28dbfcf1f0d1d3fbca0b9dadc88d') in 0.013601 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Collapse-White.png
  artifactKey: Guid(3c680cea53df4ac0871770ad3ad2f905) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Collapse-White.png using Guid(3c680cea53df4ac0871770ad3ad2f905) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '640892385989a059c9a2ea29c70310c5') in 0.015407 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Bookmarks.png
  artifactKey: Guid(e7706a4177c2490f8ef7ebcfb0fd0786) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Bookmarks.png using Guid(e7706a4177c2490f8ef7ebcfb0fd0786) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'f4af6a28a82ccfa0d002f1620b2decac') in 0.025612 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000226 seconds.
  path: Assets/Plugins/Demigiant/DOTween/Editor/Imgs/DOTweenIcon.png
  artifactKey: Guid(8da095e39e9b4df488dfd436f81116d6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Demigiant/DOTween/Editor/Imgs/DOTweenIcon.png using Guid(8da095e39e9b4df488dfd436f81116d6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '01c85cccd8532480875065deff7493f6') in 0.016608 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Eye.png
  artifactKey: Guid(e8172da6dc7c405d998730fbeb384eb5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Eye.png using Guid(e8172da6dc7c405d998730fbeb384eb5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'f6cf329c3a50ca5262cd005e88338051') in 0.022248 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000205 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Custom Tool Switcher.png
  artifactKey: Guid(04161816e7d9423e92bc7e266da7cfa3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Custom Tool Switcher.png using Guid(04161816e7d9423e92bc7e266da7cfa3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '0f2f7ebf52b7edfe7b8e1d81fff4d7ba') in 0.023459 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Component To JSON.png
  artifactKey: Guid(e70d890bee91fd34bbcc6754a600d022) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Component To JSON.png using Guid(e70d890bee91fd34bbcc6754a600d022) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '17044f1dbcb39126dfc6d175d8b0de8e') in 0.016944 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Close-White.png
  artifactKey: Guid(ef6c395344914ab58c1b07b187fe0267) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Close-White.png using Guid(ef6c395344914ab58c1b07b187fe0267) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'e9635d75e0896bc578e49b649586f6e6') in 0.014485 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Collapse Children In TreeView.png
  artifactKey: Guid(449289c710c37f542b0549a2af6afe28) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Collapse Children In TreeView.png using Guid(449289c710c37f542b0549a2af6afe28) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '667c6b7d88db493bbeddead65c9e5fa1') in 0.025227 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Custom-Pivot-Rotation-Tool-Active.png
  artifactKey: Guid(3188c472941878840aab85c80add4ae7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Custom-Pivot-Rotation-Tool-Active.png using Guid(3188c472941878840aab85c80add4ae7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '97777faf511269ebbb0c7934cec304bf') in 0.012843 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Create Material From Texture.png
  artifactKey: Guid(1e086925d363def4dbbe1d4e32d77d7a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Create Material From Texture.png using Guid(1e086925d363def4dbbe1d4e32d77d7a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'f5ec246fb29ac47a443c74d344b48d7e') in 0.029809 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000185 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Collection.png
  artifactKey: Guid(1373b89a3d09417c97506fefcedcdca6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Collection.png using Guid(1373b89a3d09417c97506fefcedcdca6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '8082c2d772ff1dd5b15a8f21472684b1') in 0.022657 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Copy.png
  artifactKey: Guid(c48ffe159d8da804a8a00e52172b81db) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Copy.png using Guid(c48ffe159d8da804a8a00e52172b81db) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'db51257910371d6458f547344e9d6b74') in 0.029661 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/CopyPro.png
  artifactKey: Guid(d5c545747941d864394c5058c49566f1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/CopyPro.png using Guid(d5c545747941d864394c5058c49566f1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '9a49d6a1532ce360ba0305c6cc356449') in 0.015489 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Enum Field Search.png
  artifactKey: Guid(66d00ee32d0a4832869564eda5d77fa1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Enum Field Search.png using Guid(66d00ee32d0a4832869564eda5d77fa1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '416075c42cff7313a2c4ac7e4a586632') in 0.030577 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Plugins/Demigiant/DemiLib/Core/Editor/Imgs/circle.png
  artifactKey: Guid(af28470bd0e2e9543a6dfa9a1a4b348d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Demigiant/DemiLib/Core/Editor/Imgs/circle.png using Guid(af28470bd0e2e9543a6dfa9a1a4b348d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '4c58abb40f3e73fbdd1ff81c5b167087') in 0.014982 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Duplicate.png
  artifactKey: Guid(58f03c12a8054c5c888d52a3d6e4c1b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Duplicate.png using Guid(58f03c12a8054c5c888d52a3d6e4c1b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'e4d61e4d2d4a8265418360d959a00214') in 0.014941 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Plugins/Demigiant/DemiLib/Core/Editor/Imgs/blueSquare.png
  artifactKey: Guid(05cfe010378336646ad6c721f66543d1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Demigiant/DemiLib/Core/Editor/Imgs/blueSquare.png using Guid(05cfe010378336646ad6c721f66543d1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '5dc1b8fd43e79a42f2878794882bc59c') in 0.014449 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Create-Object.png
  artifactKey: Guid(48ff7301c6ff4dac84c15f377be73c72) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Create-Object.png using Guid(48ff7301c6ff4dac84c15f377be73c72) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '3ca4e010c34824ac8ec007da64bc50da') in 0.017194 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Autosave.png
  artifactKey: Guid(36a99f98fe7a646488bcbd16681a7b31) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Autosave.png using Guid(36a99f98fe7a646488bcbd16681a7b31) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'bc58a9f860ea026a5240ef6491288ea7') in 0.031915 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000263 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Drop To Floor.png
  artifactKey: Guid(9539fbbf045d4bc7bb6502d6dfa3887b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Drop To Floor.png using Guid(9539fbbf045d4bc7bb6502d6dfa3887b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '24815c8b492688ca27ceaee0585c9c5d') in 0.024804 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Plugins/Demigiant/DemiLib/Core/Editor/Imgs/blackSquareAlpha15.png
  artifactKey: Guid(53d696c01f6ca524383f11fcc34dd13c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Demigiant/DemiLib/Core/Editor/Imgs/blackSquareAlpha15.png using Guid(53d696c01f6ca524383f11fcc34dd13c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '40b0ed1d94972f06b1d9e9bb1009da70') in 0.012633 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/DebugOn.png
  artifactKey: Guid(83e4c2a1d1254fa7a7b58ecf780c0bb9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/DebugOn.png using Guid(83e4c2a1d1254fa7a7b58ecf780c0bb9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'df0196957c3079a0a99c8f666efb474d') in 0.013831 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Change Value of Numeric Fields.png
  artifactKey: Guid(6f5a1998781e47538014b86ee50f2931) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Change Value of Numeric Fields.png using Guid(6f5a1998781e47538014b86ee50f2931) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'cb60fcfc977bbc8ce7bb5254380f8807') in 0.022031 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Empty Inspector.png
  artifactKey: Guid(4b78eb0e477ce0347accaa2a8b3ae1cc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Empty Inspector.png using Guid(4b78eb0e477ce0347accaa2a8b3ae1cc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '437e3fe73fa031debf01731785cf0a1a') in 0.027672 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000179 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Expand-White.png
  artifactKey: Guid(b4446e21148242fd9be0bfe117eacdf8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Expand-White.png using Guid(b4446e21148242fd9be0bfe117eacdf8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'fec6ad8fcb83fcc8424564c7ef28f25e') in 0.015478 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000115 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Collapse-Black.png
  artifactKey: Guid(0c374a4da90c456eaedf1714a2bbbeaa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Collapse-Black.png using Guid(0c374a4da90c456eaedf1714a2bbbeaa) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '534b396d6f7afa008a92fe063a9719d9') in 0.045606 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Welcome/Discord.png
  artifactKey: Guid(02ed6dc80c958454998599a24d6b5e5e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Welcome/Discord.png using Guid(02ed6dc80c958454998599a24d6b5e5e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '9c98b8d24d762ace46020a1a44c497c9') in 0.025885 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000074 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Component Header - Align and Distribute.png
  artifactKey: Guid(cb07582d235ab374e9a746f6edc8c600) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Component Header - Align and Distribute.png using Guid(cb07582d235ab374e9a746f6edc8c600) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '1a86c7f896645f11c4ba5e995ff1ef24') in 0.029266 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Custom-Pivot-Rotation-Tool.png
  artifactKey: Guid(68c6aefb3322cba4eaa200de42640ac9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Custom-Pivot-Rotation-Tool.png using Guid(68c6aefb3322cba4eaa200de42640ac9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '3b9dffeabfcbdc0944dc9f2283335092') in 0.013610 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Close-Black.png
  artifactKey: Guid(585b65d6fa3147bd9c1b5d8d39f98295) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Close-Black.png using Guid(585b65d6fa3147bd9c1b5d8d39f98295) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '8e76704a31e26484a78d741f71f56086') in 0.013655 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.002699 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Debug Mode For Component.png
  artifactKey: Guid(3aff6f2095064b6cb9d8ae409a647ce4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Debug Mode For Component.png using Guid(3aff6f2095064b6cb9d8ae409a647ce4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '5f60ee4c947be64d7a698ebaed1e35f9') in 0.027504 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Plugins/Demigiant/DemiLib/Core/Editor/Imgs/blackSquare.png
  artifactKey: Guid(7ba4b2810f605d945af87e032ca2957b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Demigiant/DemiLib/Core/Editor/Imgs/blackSquare.png using Guid(7ba4b2810f605d945af87e032ca2957b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'fe9bb01aec2f0979dcb5704b4026cca1') in 0.037227 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000442 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Blue-Bullet.png
  artifactKey: Guid(464ec7f5de8c439e933342a8eca85d33) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Blue-Bullet.png using Guid(464ec7f5de8c439e933342a8eca85d33) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '53819062feb19606e02a51129526e4d6') in 0.014253 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000108 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Create Custom Editor For MonoBehaviour.png
  artifactKey: Guid(4768f223ab299c44bb9c9dceb6528f23) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Create Custom Editor For MonoBehaviour.png using Guid(4768f223ab299c44bb9c9dceb6528f23) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '7eb5e8a177672fe42192b0f501b11ef1') in 0.021381 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000179 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Close-Windows.png
  artifactKey: Guid(b6bdedb57825449e9bcc01cd21eaaba8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Close-Windows.png using Guid(b6bdedb57825449e9bcc01cd21eaaba8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '63113eadcd6c3b8be5f8d922633fe439') in 0.014375 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Custom Pivot Rotation Tool.png
  artifactKey: Guid(431e6e6765881f248b227d55da73fc3c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Custom Pivot Rotation Tool.png using Guid(431e6e6765881f248b227d55da73fc3c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'b84ce4d213ad0cfdb8354e127c49383c') in 0.029351 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Plugins/Demigiant/DOTween/Editor/Imgs/DOTweenMiniIcon.png
  artifactKey: Guid(61521df2e071645488ba3d05e49289ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Demigiant/DOTween/Editor/Imgs/DOTweenMiniIcon.png using Guid(61521df2e071645488ba3d05e49289ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '47b4f0698de532e5d9ffd6f906376690') in 0.015575 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Plugins/Demigiant/DemiLib/Core/Editor/Imgs/blackSquareAlpha25.png
  artifactKey: Guid(f7ff421f40d548444864a01cd7f47112) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Demigiant/DemiLib/Core/Editor/Imgs/blackSquareAlpha25.png using Guid(f7ff421f40d548444864a01cd7f47112) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '8e683aec15746e1d7010ee4634c8da56') in 0.022977 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000110 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Duplicate Tool.png
  artifactKey: Guid(befee3852b7b48b5b24342e018fce00b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Duplicate Tool.png using Guid(befee3852b7b48b5b24342e018fce00b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'ccb06dea2761b025f792223c479f644f') in 0.031703 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Drag And Drop To Tab.png
  artifactKey: Guid(0645e4bebf98fe94698104124c8dcaa9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Drag And Drop To Tab.png using Guid(0645e4bebf98fe94698104124c8dcaa9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '7cf637196f0cbab8d402eac765743876') in 0.046992 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000402 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Debug.png
  artifactKey: Guid(8056682ee22740449947d43781a4c199) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Debug.png using Guid(8056682ee22740449947d43781a4c199) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '03057785327e86f1b19bec1e049290f1') in 0.013683 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Arrow-Left.png
  artifactKey: Guid(5a457dd7912c4e069238126eab937316) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Arrow-Left.png using Guid(5a457dd7912c4e069238126eab937316) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '1ba7550e0d370eaeeb917cdb36a9c9a0') in 0.013892 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Curve Editor Improvement.png
  artifactKey: Guid(a6eab456b324ae24aa3089d58f304d0a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Curve Editor Improvement.png using Guid(a6eab456b324ae24aa3089d58f304d0a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '24d5b072ed7cbb855f5ffbd100b744b5') in 0.034781 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Plugins/Demigiant/DemiLib/Core/Editor/Imgs/blackSquareAlpha10.png
  artifactKey: Guid(8c9cfa1dbe00a1d41ae9d14f5ac543ec) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Demigiant/DemiLib/Core/Editor/Imgs/blackSquareAlpha10.png using Guid(8c9cfa1dbe00a1d41ae9d14f5ac543ec) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '36ad6bc2f21828e54d3870f3cf53e872') in 0.015225 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Plugins/Demigiant/DemiLib/Core/Editor/Imgs/circle_dashedBorderEmpty.png
  artifactKey: Guid(522b0133a9838674ca9a12e0c6e5a59c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Demigiant/DemiLib/Core/Editor/Imgs/circle_dashedBorderEmpty.png using Guid(522b0133a9838674ca9a12e0c6e5a59c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'c6c93dc3b4ce681e22258e0aca9d34fa') in 0.014726 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Bounds-Dark.png
  artifactKey: Guid(1051fc10e86c4363a1051eb14a7e8ddd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Bounds-Dark.png using Guid(1051fc10e86c4363a1051eb14a7e8ddd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '380994a46f28c2d5ddf98a1018a8a322') in 0.017279 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Detect Bounds of Box Collider.png
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Detect Bounds of Box Collider.png using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'f4301c54a060d9649636187bc1040a75') in 0.017520 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Distance Tool.png
  artifactKey: Guid(f739e8cab5bf4b25ace575cdc2ac7647) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Distance Tool.png using Guid(f739e8cab5bf4b25ace575cdc2ac7647) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '6f4595a59b72b8218aab62eaa0335ceb') in 0.018389 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Arrow-Down.png
  artifactKey: Guid(5470f739c2b24becb3beb5ed70632432) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Arrow-Down.png using Guid(5470f739c2b24becb3beb5ed70632432) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '1f0ad7579171d34ab7bd43267ba4f7e6') in 0.013756 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Welcome/Docs.png
  artifactKey: Guid(8a4992e5e24b4e728e1761ab3856e741) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Welcome/Docs.png using Guid(8a4992e5e24b4e728e1761ab3856e741) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'a3b8d4a3be6eafa92982a44150803518') in 0.010065 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Drag From Object Fields.png
  artifactKey: Guid(d27baf2b985e44caad0ddbf2bf4149ed) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Drag From Object Fields.png using Guid(d27baf2b985e44caad0ddbf2bf4149ed) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'fa85845f2cf9fd0e51ae712c5b3803e3') in 0.024837 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000405 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Breadcrumbs.png
  artifactKey: Guid(1b22fa5a72204503a651240f2d76f4d9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Breadcrumbs.png using Guid(1b22fa5a72204503a651240f2d76f4d9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'd60df53e9095cce9e089e88c058fd3f6') in 0.030816 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000273 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Camera Align With.png
  artifactKey: Guid(49191cbc8437d464caf1402523869d19) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Camera Align With.png using Guid(49191cbc8437d464caf1402523869d19) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'e6b4f707fdb142d1334617343e36f64c') in 0.024445 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Arrow-Right.png
  artifactKey: Guid(448abe970b8247b8a0e214cd8e3f055d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Arrow-Right.png using Guid(448abe970b8247b8a0e214cd8e3f055d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '668508b1f69b51d1fcfc1d05d31abcbd') in 0.016841 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Arrow-Up.png
  artifactKey: Guid(1d08988df13d4150b470b5522b7bd3f5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Arrow-Up.png using Guid(1d08988df13d4150b470b5522b7bd3f5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '6f9b9b33a17104659e613bad5dd126e5') in 0.023180 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Bounds.png
  artifactKey: Guid(0890ba1869eb44c6bab1b38bbae7aefb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Bounds.png using Guid(0890ba1869eb44c6bab1b38bbae7aefb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '6caf841fb1f54b95832a93f77628835c') in 0.013019 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/DuplicateToolPro.png
  artifactKey: Guid(602486244fd7460498a0557eb0434430) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/DuplicateToolPro.png using Guid(602486244fd7460498a0557eb0434430) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '7e55eb0fc14af96ed2583a38dc162d5b') in 0.015455 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Align-Dark.png
  artifactKey: Guid(640ec354a9714e0f871c711d3e0742fa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Align-Dark.png using Guid(640ec354a9714e0f871c711d3e0742fa) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'a1eef87a134383a7ad450eeed4e45745') in 0.012969 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Editor Icon Browser.png
  artifactKey: Guid(5b393835f5254d8fa167b4b642699fbf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Textures/Getting Started/Editor Icon Browser.png using Guid(5b393835f5254d8fa167b4b642699fbf) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '51af49c79a92f6572b872c67d4db900d') in 0.026481 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/TextMesh Pro/Sprites/EmojiOne.png
  artifactKey: Guid(dffef66376be4fa480fb02b19edbe903) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextMesh Pro/Sprites/EmojiOne.png using Guid(dffef66376be4fa480fb02b19edbe903) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'daadf0ce84b1373cdf4896255e5e66ed') in 0.019240 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Expand-Black.png
  artifactKey: Guid(956db103de084b76b255f3425fdf475d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Infinity Code/Ultimate Editor Enhancer/Icons/Expand-Black.png using Guid(956db103de084b76b255f3425fdf475d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'cfbf43fa881b36583360d710f9c315a2') in 0.012752 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.523 seconds
Refreshing native plugins compatible for Editor in 24.46 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.761 seconds
Domain Reload Profiling: 6260ms
	BeginReloadAssembly (426ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (15ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (94ms)
	RebuildCommonClasses (68ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (96ms)
	LoadAllAssembliesAndSetupDomain (888ms)
		LoadAssemblies (1069ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (39ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (24ms)
	FinalizeReload (4762ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2475ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (14ms)
			SetLoadedEditorAssemblies (7ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (172ms)
			ProcessInitializeOnLoadAttributes (1042ms)
			ProcessInitializeOnLoadMethodAttributes (1207ms)
			AfterProcessingInitializeOnLoad (32ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (54ms)
Refreshing native plugins compatible for Editor in 20.30 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5490 Unused Serialized files (Serialized files now loaded: 0)
Unloading 57 unused Assets / (0.5 MB). Loaded Objects now: 6270.
Memory consumption went from 196.3 MB to 195.8 MB.
Total: 8.063400 ms (FindLiveObjects: 1.225100 ms CreateObjectMapping: 0.483900 ms MarkObjects: 5.983700 ms  DeleteObjects: 0.369000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.351 seconds
Refreshing native plugins compatible for Editor in 24.47 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.593 seconds
Domain Reload Profiling: 7904ms
	BeginReloadAssembly (1442ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (885ms)
	RebuildCommonClasses (167ms)
	RebuildNativeTypeToScriptingClass (33ms)
	initialDomainReloadingComplete (294ms)
	LoadAllAssembliesAndSetupDomain (1374ms)
		LoadAssemblies (1625ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (104ms)
			TypeCache.Refresh (30ms)
				TypeCache.ScanAssembly (5ms)
			ScanForSourceGeneratedMonoScriptInfo (46ms)
			ResolveRequiredComponents (25ms)
	FinalizeReload (4594ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2055ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (151ms)
			ProcessInitializeOnLoadAttributes (1026ms)
			ProcessInitializeOnLoadMethodAttributes (826ms)
			AfterProcessingInitializeOnLoad (33ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (39ms)
Refreshing native plugins compatible for Editor in 27.63 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5490 Unused Serialized files (Serialized files now loaded: 0)
Unloading 57 unused Assets / (0.5 MB). Loaded Objects now: 6274.
Memory consumption went from 196.3 MB to 195.8 MB.
Total: 6.072800 ms (FindLiveObjects: 0.553100 ms CreateObjectMapping: 0.319500 ms MarkObjects: 4.861700 ms  DeleteObjects: 0.337300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 502.073061 seconds.
  path: Assets/Scripts/Map/Shaders/New Map Data.asset
  artifactKey: Guid(6eb987821d314c74b963c314d235197a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Map/Shaders/New Map Data.asset using Guid(6eb987821d314c74b963c314d235197a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '10eafced43a08c9ba24c007a35053f3e') in 0.083448 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.245 seconds
Refreshing native plugins compatible for Editor in 36.85 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  9.146 seconds
Domain Reload Profiling: 11329ms
	BeginReloadAssembly (610ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (42ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (130ms)
	RebuildCommonClasses (121ms)
	RebuildNativeTypeToScriptingClass (38ms)
	initialDomainReloadingComplete (136ms)
	LoadAllAssembliesAndSetupDomain (1277ms)
		LoadAssemblies (1500ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (97ms)
			TypeCache.Refresh (36ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (56ms)
	FinalizeReload (9146ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (4216ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (26ms)
			SetLoadedEditorAssemblies (9ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (315ms)
			ProcessInitializeOnLoadAttributes (2254ms)
			ProcessInitializeOnLoadMethodAttributes (1578ms)
			AfterProcessingInitializeOnLoad (33ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (58ms)
Refreshing native plugins compatible for Editor in 47.35 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5490 Unused Serialized files (Serialized files now loaded: 0)
Unloading 57 unused Assets / (0.5 MB). Loaded Objects now: 6278.
Memory consumption went from 196.3 MB to 195.8 MB.
Total: 8.867600 ms (FindLiveObjects: 0.916900 ms CreateObjectMapping: 0.830400 ms MarkObjects: 6.266000 ms  DeleteObjects: 0.853100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 82.320001 seconds.
  path: Assets/Scripts/Map/DefaultMapData.asset
  artifactKey: Guid(471684fa6a70a1b48bdd190cafaf9f36) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Map/DefaultMapData.asset using Guid(471684fa6a70a1b48bdd190cafaf9f36) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '6cae3999aeefd371627c74eed4f1b363') in 0.080453 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000195 seconds.
  path: Assets/Scripts/Map/DefaultMapData.asset
  artifactKey: Guid(471684fa6a70a1b48bdd190cafaf9f36) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Map/DefaultMapData.asset using Guid(471684fa6a70a1b48bdd190cafaf9f36) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '6cae3999aeefd371627c74eed4f1b363') in 0.007062 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 11.316 seconds
Refreshing native plugins compatible for Editor in 118.30 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 10.052 seconds
Domain Reload Profiling: 21260ms
	BeginReloadAssembly (2883ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (143ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (825ms)
	RebuildCommonClasses (941ms)
	RebuildNativeTypeToScriptingClass (348ms)
	initialDomainReloadingComplete (794ms)
	LoadAllAssembliesAndSetupDomain (6240ms)
		LoadAssemblies (7520ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (296ms)
			TypeCache.Refresh (64ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (213ms)
	FinalizeReload (10053ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3066ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (16ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (425ms)
			ProcessInitializeOnLoadAttributes (1643ms)
			ProcessInitializeOnLoadMethodAttributes (933ms)
			AfterProcessingInitializeOnLoad (31ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (48ms)
Refreshing native plugins compatible for Editor in 28.10 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5490 Unused Serialized files (Serialized files now loaded: 0)
Unloading 57 unused Assets / (0.5 MB). Loaded Objects now: 6282.
Memory consumption went from 196.3 MB to 195.8 MB.
Total: 8.981300 ms (FindLiveObjects: 0.876400 ms CreateObjectMapping: 0.454100 ms MarkObjects: 7.283700 ms  DeleteObjects: 0.365200 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 73.041733 seconds.
  path: Assets/Scripts/Map/DefaultMapData.asset
  artifactKey: Guid(471684fa6a70a1b48bdd190cafaf9f36) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Map/DefaultMapData.asset using Guid(471684fa6a70a1b48bdd190cafaf9f36) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'eeae4c01becf6b42ac25bede92f05448') in 0.075870 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.904 seconds
Refreshing native plugins compatible for Editor in 25.08 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  5.852 seconds
Domain Reload Profiling: 8725ms
	BeginReloadAssembly (1220ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (24ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (157ms)
	RebuildCommonClasses (433ms)
	RebuildNativeTypeToScriptingClass (85ms)
	initialDomainReloadingComplete (182ms)
	LoadAllAssembliesAndSetupDomain (952ms)
		LoadAssemblies (1845ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (41ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (1ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (5853ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2614ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (10ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (176ms)
			ProcessInitializeOnLoadAttributes (1263ms)
			ProcessInitializeOnLoadMethodAttributes (1115ms)
			AfterProcessingInitializeOnLoad (29ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (41ms)
Refreshing native plugins compatible for Editor in 24.73 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5490 Unused Serialized files (Serialized files now loaded: 0)
Unloading 57 unused Assets / (0.5 MB). Loaded Objects now: 6286.
Memory consumption went from 196.3 MB to 195.8 MB.
Total: 7.935200 ms (FindLiveObjects: 0.577600 ms CreateObjectMapping: 0.356400 ms MarkObjects: 6.643900 ms  DeleteObjects: 0.356100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 103.383387 seconds.
  path: Assets/Scripts/Map/MapGenerator.cs
  artifactKey: Guid(560e877002418ca488102c786fbeb00a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Map/MapGenerator.cs using Guid(560e877002418ca488102c786fbeb00a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '6a4cb3f73b6fcebcbbb9b8f9e329542d') in 0.055444 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  6.320 seconds
Refreshing native plugins compatible for Editor in 29.34 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.471 seconds
Domain Reload Profiling: 10751ms
	BeginReloadAssembly (4727ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (23ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (3710ms)
	RebuildCommonClasses (107ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (146ms)
	LoadAllAssembliesAndSetupDomain (1275ms)
		LoadAssemblies (1824ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (114ms)
			TypeCache.Refresh (27ms)
				TypeCache.ScanAssembly (9ms)
			ScanForSourceGeneratedMonoScriptInfo (54ms)
			ResolveRequiredComponents (29ms)
	FinalizeReload (4472ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2181ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (14ms)
			SetLoadedEditorAssemblies (7ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (167ms)
			ProcessInitializeOnLoadAttributes (1063ms)
			ProcessInitializeOnLoadMethodAttributes (898ms)
			AfterProcessingInitializeOnLoad (32ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (50ms)
Refreshing native plugins compatible for Editor in 25.19 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5490 Unused Serialized files (Serialized files now loaded: 0)
Unloading 57 unused Assets / (0.5 MB). Loaded Objects now: 6290.
Memory consumption went from 196.3 MB to 195.8 MB.
Total: 9.466500 ms (FindLiveObjects: 0.786700 ms CreateObjectMapping: 0.598000 ms MarkObjects: 5.832600 ms  DeleteObjects: 2.247600 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.124 seconds
Refreshing native plugins compatible for Editor in 33.89 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  9.642 seconds
Domain Reload Profiling: 12720ms
	BeginReloadAssembly (645ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (18ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (148ms)
	RebuildCommonClasses (103ms)
	RebuildNativeTypeToScriptingClass (29ms)
	initialDomainReloadingComplete (328ms)
	LoadAllAssembliesAndSetupDomain (1971ms)
		LoadAssemblies (2052ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (273ms)
			TypeCache.Refresh (64ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (167ms)
			ResolveRequiredComponents (37ms)
	FinalizeReload (9643ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (4253ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (26ms)
			SetLoadedEditorAssemblies (14ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (475ms)
			ProcessInitializeOnLoadAttributes (2104ms)
			ProcessInitializeOnLoadMethodAttributes (1597ms)
			AfterProcessingInitializeOnLoad (37ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (41ms)
Refreshing native plugins compatible for Editor in 26.29 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5490 Unused Serialized files (Serialized files now loaded: 0)
Unloading 57 unused Assets / (0.5 MB). Loaded Objects now: 6294.
Memory consumption went from 196.3 MB to 195.8 MB.
Total: 9.437000 ms (FindLiveObjects: 0.802400 ms CreateObjectMapping: 0.614900 ms MarkObjects: 7.676700 ms  DeleteObjects: 0.341400 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 244.180024 seconds.
  path: Assets/Scripts/Map/Shaders/New Material.mat
  artifactKey: Guid(ee8265491a149634792202f978e5a21a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Map/Shaders/New Material.mat using Guid(ee8265491a149634792202f978e5a21a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '68eccc7f1f57db8a01063a1eb8f17159') in 3.022708 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 22
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.307 seconds
Refreshing native plugins compatible for Editor in 22.40 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.581 seconds
Domain Reload Profiling: 5870ms
	BeginReloadAssembly (326ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (22ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (83ms)
	RebuildCommonClasses (64ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (85ms)
	LoadAllAssembliesAndSetupDomain (794ms)
		LoadAssemblies (919ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (37ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (22ms)
	FinalizeReload (4582ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2315ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (19ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (183ms)
			ProcessInitializeOnLoadAttributes (1129ms)
			ProcessInitializeOnLoadMethodAttributes (932ms)
			AfterProcessingInitializeOnLoad (33ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (37ms)
Refreshing native plugins compatible for Editor in 56.10 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5490 Unused Serialized files (Serialized files now loaded: 0)
Unloading 56 unused Assets / (320.7 KB). Loaded Objects now: 6336.
Memory consumption went from 196.5 MB to 196.2 MB.
Total: 6.540100 ms (FindLiveObjects: 0.715400 ms CreateObjectMapping: 0.355900 ms MarkObjects: 5.330100 ms  DeleteObjects: 0.137700 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.099 seconds
Refreshing native plugins compatible for Editor in 45.35 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  5.430 seconds
Domain Reload Profiling: 8487ms
	BeginReloadAssembly (1050ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (95ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (253ms)
	RebuildCommonClasses (108ms)
	RebuildNativeTypeToScriptingClass (42ms)
	initialDomainReloadingComplete (227ms)
	LoadAllAssembliesAndSetupDomain (1628ms)
		LoadAssemblies (1758ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (300ms)
			TypeCache.Refresh (48ms)
				TypeCache.ScanAssembly (18ms)
			ScanForSourceGeneratedMonoScriptInfo (177ms)
			ResolveRequiredComponents (67ms)
	FinalizeReload (5431ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2746ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (30ms)
			SetLoadedEditorAssemblies (11ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (228ms)
			ProcessInitializeOnLoadAttributes (1475ms)
			ProcessInitializeOnLoadMethodAttributes (971ms)
			AfterProcessingInitializeOnLoad (30ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (52ms)
Refreshing native plugins compatible for Editor in 24.99 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5489 Unused Serialized files (Serialized files now loaded: 0)
Unloading 56 unused Assets / (320.8 KB). Loaded Objects now: 6340.
Memory consumption went from 196.0 MB to 195.7 MB.
Total: 33.158100 ms (FindLiveObjects: 1.381300 ms CreateObjectMapping: 1.681100 ms MarkObjects: 29.953900 ms  DeleteObjects: 0.140100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.190 seconds
Refreshing native plugins compatible for Editor in 39.79 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  7.980 seconds
Domain Reload Profiling: 10130ms
	BeginReloadAssembly (609ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (19ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (170ms)
	RebuildCommonClasses (106ms)
	RebuildNativeTypeToScriptingClass (36ms)
	initialDomainReloadingComplete (167ms)
	LoadAllAssembliesAndSetupDomain (1229ms)
		LoadAssemblies (1478ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (72ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (46ms)
	FinalizeReload (7982ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3786ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (26ms)
			SetLoadedEditorAssemblies (14ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (282ms)
			ProcessInitializeOnLoadAttributes (1789ms)
			ProcessInitializeOnLoadMethodAttributes (1617ms)
			AfterProcessingInitializeOnLoad (58ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (79ms)
Refreshing native plugins compatible for Editor in 37.23 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5489 Unused Serialized files (Serialized files now loaded: 0)
Unloading 56 unused Assets / (321.7 KB). Loaded Objects now: 6344.
Memory consumption went from 196.0 MB to 195.7 MB.
Total: 10.393500 ms (FindLiveObjects: 0.845200 ms CreateObjectMapping: 0.615300 ms MarkObjects: 8.491400 ms  DeleteObjects: 0.440500 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.207 seconds
Refreshing native plugins compatible for Editor in 24.43 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  5.999 seconds
Domain Reload Profiling: 8185ms
	BeginReloadAssembly (663ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (20ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (180ms)
	RebuildCommonClasses (76ms)
	RebuildNativeTypeToScriptingClass (33ms)
	initialDomainReloadingComplete (84ms)
	LoadAllAssembliesAndSetupDomain (1330ms)
		LoadAssemblies (1581ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (86ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (45ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (5999ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2681ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (15ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (227ms)
			ProcessInitializeOnLoadAttributes (1330ms)
			ProcessInitializeOnLoadMethodAttributes (1071ms)
			AfterProcessingInitializeOnLoad (30ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (90ms)
Refreshing native plugins compatible for Editor in 56.11 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5489 Unused Serialized files (Serialized files now loaded: 0)
Unloading 56 unused Assets / (320.8 KB). Loaded Objects now: 6348.
Memory consumption went from 196.0 MB to 195.7 MB.
Total: 23.289600 ms (FindLiveObjects: 0.784000 ms CreateObjectMapping: 1.022200 ms MarkObjects: 21.144600 ms  DeleteObjects: 0.337300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  7.186 seconds
Refreshing native plugins compatible for Editor in 195.46 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 44.477 seconds
Domain Reload Profiling: 51602ms
	BeginReloadAssembly (2226ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (108ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (479ms)
	RebuildCommonClasses (199ms)
	RebuildNativeTypeToScriptingClass (52ms)
	initialDomainReloadingComplete (244ms)
	LoadAllAssembliesAndSetupDomain (4404ms)
		LoadAssemblies (5000ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (387ms)
			TypeCache.Refresh (106ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (3ms)
			ResolveRequiredComponents (259ms)
	FinalizeReload (44478ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (20439ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (127ms)
			SetLoadedEditorAssemblies (89ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (1385ms)
			ProcessInitializeOnLoadAttributes (9884ms)
			ProcessInitializeOnLoadMethodAttributes (8851ms)
			AfterProcessingInitializeOnLoad (102ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (1ms)
		AwakeInstancesAfterBackupRestoration (228ms)
Refreshing native plugins compatible for Editor in 20.88 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5489 Unused Serialized files (Serialized files now loaded: 0)
Unloading 56 unused Assets / (321.8 KB). Loaded Objects now: 6352.
Memory consumption went from 196.1 MB to 195.7 MB.
Total: 13.922200 ms (FindLiveObjects: 2.464100 ms CreateObjectMapping: 2.553500 ms MarkObjects: 8.615300 ms  DeleteObjects: 0.287200 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 648.304855 seconds.
  path: Assets/Scripts/Map/DefaultMapData.asset
  artifactKey: Guid(471684fa6a70a1b48bdd190cafaf9f36) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Map/DefaultMapData.asset using Guid(471684fa6a70a1b48bdd190cafaf9f36) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '86e1c8414a06ff31378a0e5e7a5b6496') in 9.506636 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 18.952 seconds
Refreshing native plugins compatible for Editor in 25.92 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 30.277 seconds
Domain Reload Profiling: 49206ms
	BeginReloadAssembly (892ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (21ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (133ms)
	RebuildCommonClasses (10727ms)
	RebuildNativeTypeToScriptingClass (4482ms)
	initialDomainReloadingComplete (521ms)
	LoadAllAssembliesAndSetupDomain (2306ms)
		LoadAssemblies (2770ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (149ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (129ms)
	FinalizeReload (30278ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (6747ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (54ms)
			SetLoadedEditorAssemblies (19ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (890ms)
			ProcessInitializeOnLoadAttributes (4206ms)
			ProcessInitializeOnLoadMethodAttributes (1542ms)
			AfterProcessingInitializeOnLoad (37ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (89ms)
Refreshing native plugins compatible for Editor in 22.21 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5489 Unused Serialized files (Serialized files now loaded: 0)
Unloading 56 unused Assets / (320.6 KB). Loaded Objects now: 6356.
Memory consumption went from 196.1 MB to 195.7 MB.
Total: 8.075100 ms (FindLiveObjects: 0.989200 ms CreateObjectMapping: 1.061500 ms MarkObjects: 5.885700 ms  DeleteObjects: 0.137500 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.350 seconds
Refreshing native plugins compatible for Editor in 22.11 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 14.155 seconds
Domain Reload Profiling: 15486ms
	BeginReloadAssembly (316ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (77ms)
	RebuildCommonClasses (59ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (84ms)
	LoadAllAssembliesAndSetupDomain (852ms)
		LoadAssemblies (986ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (36ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (22ms)
	FinalizeReload (14156ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (6476ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (32ms)
			SetLoadedEditorAssemblies (17ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (933ms)
			ProcessInitializeOnLoadAttributes (2418ms)
			ProcessInitializeOnLoadMethodAttributes (3024ms)
			AfterProcessingInitializeOnLoad (52ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (65ms)
Refreshing native plugins compatible for Editor in 21.71 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5489 Unused Serialized files (Serialized files now loaded: 0)
Unloading 56 unused Assets / (321.6 KB). Loaded Objects now: 6360.
Memory consumption went from 196.1 MB to 195.7 MB.
Total: 9.360400 ms (FindLiveObjects: 0.675300 ms CreateObjectMapping: 0.827000 ms MarkObjects: 7.717900 ms  DeleteObjects: 0.139000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.233 seconds
Refreshing native plugins compatible for Editor in 21.77 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.412 seconds
Domain Reload Profiling: 5627ms
	BeginReloadAssembly (326ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (75ms)
	RebuildCommonClasses (62ms)
	RebuildNativeTypeToScriptingClass (21ms)
	initialDomainReloadingComplete (84ms)
	LoadAllAssembliesAndSetupDomain (721ms)
		LoadAssemblies (804ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (90ms)
			TypeCache.Refresh (21ms)
				TypeCache.ScanAssembly (5ms)
			ScanForSourceGeneratedMonoScriptInfo (44ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (4413ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2143ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (7ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (149ms)
			ProcessInitializeOnLoadAttributes (931ms)
			ProcessInitializeOnLoadMethodAttributes (1016ms)
			AfterProcessingInitializeOnLoad (27ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (99ms)
Refreshing native plugins compatible for Editor in 72.66 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5490 Unused Serialized files (Serialized files now loaded: 0)
Unloading 56 unused Assets / (320.7 KB). Loaded Objects now: 6365.
Memory consumption went from 196.1 MB to 195.8 MB.
Total: 17.462100 ms (FindLiveObjects: 1.679200 ms CreateObjectMapping: 1.712000 ms MarkObjects: 13.092300 ms  DeleteObjects: 0.976900 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.459 seconds
Refreshing native plugins compatible for Editor in 26.44 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  5.071 seconds
Domain Reload Profiling: 6509ms
	BeginReloadAssembly (449ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (123ms)
	RebuildCommonClasses (65ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (112ms)
	LoadAllAssembliesAndSetupDomain (788ms)
		LoadAssemblies (981ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (47ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (29ms)
	FinalizeReload (5072ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2441ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (15ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (197ms)
			ProcessInitializeOnLoadAttributes (1192ms)
			ProcessInitializeOnLoadMethodAttributes (998ms)
			AfterProcessingInitializeOnLoad (35ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (38ms)
Refreshing native plugins compatible for Editor in 22.45 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5490 Unused Serialized files (Serialized files now loaded: 0)
Unloading 56 unused Assets / (321.8 KB). Loaded Objects now: 6369.
Memory consumption went from 196.1 MB to 195.8 MB.
Total: 9.082600 ms (FindLiveObjects: 0.802100 ms CreateObjectMapping: 0.806100 ms MarkObjects: 7.270800 ms  DeleteObjects: 0.202500 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  9.977 seconds
Refreshing native plugins compatible for Editor in 187.00 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 40.247 seconds
Domain Reload Profiling: 50035ms
	BeginReloadAssembly (3285ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (130ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (701ms)
	RebuildCommonClasses (595ms)
	RebuildNativeTypeToScriptingClass (131ms)
	initialDomainReloadingComplete (755ms)
	LoadAllAssembliesAndSetupDomain (5019ms)
		LoadAssemblies (6412ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (301ms)
			TypeCache.Refresh (70ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (204ms)
	FinalizeReload (40250ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (19974ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (141ms)
			SetLoadedEditorAssemblies (50ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (1707ms)
			ProcessInitializeOnLoadAttributes (9089ms)
			ProcessInitializeOnLoadMethodAttributes (8686ms)
			AfterProcessingInitializeOnLoad (301ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (455ms)
Refreshing native plugins compatible for Editor in 74.19 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5490 Unused Serialized files (Serialized files now loaded: 0)
Unloading 56 unused Assets / (320.7 KB). Loaded Objects now: 6373.
Memory consumption went from 196.1 MB to 195.8 MB.
Total: 10.939700 ms (FindLiveObjects: 1.397500 ms CreateObjectMapping: 2.087100 ms MarkObjects: 7.307000 ms  DeleteObjects: 0.146800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.645 seconds
Refreshing native plugins compatible for Editor in 21.95 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  7.555 seconds
Domain Reload Profiling: 9647ms
	BeginReloadAssembly (1119ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (117ms)
	RebuildCommonClasses (101ms)
	RebuildNativeTypeToScriptingClass (30ms)
	initialDomainReloadingComplete (112ms)
	LoadAllAssembliesAndSetupDomain (725ms)
		LoadAssemblies (1595ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (37ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (22ms)
	FinalizeReload (7560ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (5221ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (7ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (157ms)
			ProcessInitializeOnLoadAttributes (3505ms)
			ProcessInitializeOnLoadMethodAttributes (1508ms)
			AfterProcessingInitializeOnLoad (32ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (45ms)
Refreshing native plugins compatible for Editor in 25.87 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5490 Unused Serialized files (Serialized files now loaded: 0)
Unloading 56 unused Assets / (321.7 KB). Loaded Objects now: 6377.
Memory consumption went from 196.1 MB to 195.8 MB.
Total: 9.308100 ms (FindLiveObjects: 1.660300 ms CreateObjectMapping: 0.768500 ms MarkObjects: 6.741400 ms  DeleteObjects: 0.137000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  9.804 seconds
Refreshing native plugins compatible for Editor in 65.41 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 18.602 seconds
Domain Reload Profiling: 28201ms
	BeginReloadAssembly (2775ms)
		ExecutionOrderSort (3ms)
		DisableScriptedObjects (129ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (595ms)
	RebuildCommonClasses (693ms)
	RebuildNativeTypeToScriptingClass (249ms)
	initialDomainReloadingComplete (861ms)
	LoadAllAssembliesAndSetupDomain (5021ms)
		LoadAssemblies (6525ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (117ms)
			TypeCache.Refresh (47ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (1ms)
			ResolveRequiredComponents (64ms)
	FinalizeReload (18602ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2791ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (14ms)
			SetLoadedEditorAssemblies (9ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (167ms)
			ProcessInitializeOnLoadAttributes (1328ms)
			ProcessInitializeOnLoadMethodAttributes (1240ms)
			AfterProcessingInitializeOnLoad (34ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (55ms)
Refreshing native plugins compatible for Editor in 28.98 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5490 Unused Serialized files (Serialized files now loaded: 0)
Unloading 56 unused Assets / (320.8 KB). Loaded Objects now: 6381.
Memory consumption went from 196.1 MB to 195.8 MB.
Total: 5.246800 ms (FindLiveObjects: 0.580300 ms CreateObjectMapping: 0.318800 ms MarkObjects: 4.210700 ms  DeleteObjects: 0.135800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.580 seconds
Refreshing native plugins compatible for Editor in 54.48 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 10.984 seconds
Domain Reload Profiling: 14522ms
	BeginReloadAssembly (653ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (210ms)
	RebuildCommonClasses (139ms)
	RebuildNativeTypeToScriptingClass (49ms)
	initialDomainReloadingComplete (186ms)
	LoadAllAssembliesAndSetupDomain (2512ms)
		LoadAssemblies (2091ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (763ms)
			TypeCache.Refresh (62ms)
				TypeCache.ScanAssembly (6ms)
			ScanForSourceGeneratedMonoScriptInfo (190ms)
			ResolveRequiredComponents (493ms)
	FinalizeReload (10984ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (4304ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (22ms)
			SetLoadedEditorAssemblies (15ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (255ms)
			ProcessInitializeOnLoadAttributes (2694ms)
			ProcessInitializeOnLoadMethodAttributes (1284ms)
			AfterProcessingInitializeOnLoad (34ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (53ms)
Refreshing native plugins compatible for Editor in 17.25 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5482 Unused Serialized files (Serialized files now loaded: 0)
Unloading 63 unused Assets / (324.5 KB). Loaded Objects now: 6378.
Memory consumption went from 195.9 MB to 195.6 MB.
Total: 6.469100 ms (FindLiveObjects: 0.590700 ms CreateObjectMapping: 0.722100 ms MarkObjects: 5.012200 ms  DeleteObjects: 0.143000 ms)

Prepare: number of updated asset objects reloaded= 1
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.345 seconds
Refreshing native plugins compatible for Editor in 43.40 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  8.075 seconds
Domain Reload Profiling: 11377ms
	BeginReloadAssembly (1186ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (28ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (164ms)
	RebuildCommonClasses (150ms)
	RebuildNativeTypeToScriptingClass (43ms)
	initialDomainReloadingComplete (202ms)
	LoadAllAssembliesAndSetupDomain (1720ms)
		LoadAssemblies (2357ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (195ms)
			TypeCache.Refresh (58ms)
				TypeCache.ScanAssembly (10ms)
			ScanForSourceGeneratedMonoScriptInfo (97ms)
			ResolveRequiredComponents (35ms)
	FinalizeReload (8077ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3814ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (41ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (324ms)
			ProcessInitializeOnLoadAttributes (2031ms)
			ProcessInitializeOnLoadMethodAttributes (1381ms)
			AfterProcessingInitializeOnLoad (33ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (46ms)
Refreshing native plugins compatible for Editor in 25.09 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5485 Unused Serialized files (Serialized files now loaded: 0)
Unloading 57 unused Assets / (0.7 MB). Loaded Objects now: 6383.
Memory consumption went from 196.6 MB to 195.9 MB.
Total: 12.419600 ms (FindLiveObjects: 0.562100 ms CreateObjectMapping: 0.720300 ms MarkObjects: 10.694400 ms  DeleteObjects: 0.441200 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.635 seconds
Refreshing native plugins compatible for Editor in 21.41 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  5.787 seconds
Domain Reload Profiling: 7402ms
	BeginReloadAssembly (450ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (174ms)
	RebuildCommonClasses (70ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (97ms)
	LoadAllAssembliesAndSetupDomain (975ms)
		LoadAssemblies (1061ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (104ms)
			TypeCache.Refresh (27ms)
				TypeCache.ScanAssembly (6ms)
			ScanForSourceGeneratedMonoScriptInfo (48ms)
			ResolveRequiredComponents (27ms)
	FinalizeReload (5788ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2640ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (15ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (191ms)
			ProcessInitializeOnLoadAttributes (1178ms)
			ProcessInitializeOnLoadMethodAttributes (1220ms)
			AfterProcessingInitializeOnLoad (30ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (56ms)
Refreshing native plugins compatible for Editor in 21.40 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5486 Unused Serialized files (Serialized files now loaded: 0)
Unloading 57 unused Assets / (0.7 MB). Loaded Objects now: 6388.
Memory consumption went from 197.0 MB to 196.3 MB.
Total: 8.935700 ms (FindLiveObjects: 1.010700 ms CreateObjectMapping: 0.401600 ms MarkObjects: 6.868200 ms  DeleteObjects: 0.653600 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 1651.411337 seconds.
  path: Assets/Quad.cs
  artifactKey: Guid(2a61f25122a577f4fb16ecca1f45db79) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Quad.cs using Guid(2a61f25122a577f4fb16ecca1f45db79) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '2790f09ca6e86ab26423578d43369011') in 0.002292 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.845 seconds
Refreshing native plugins compatible for Editor in 34.87 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 11.916 seconds
Domain Reload Profiling: 14730ms
	BeginReloadAssembly (522ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (27ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (102ms)
	RebuildCommonClasses (113ms)
	RebuildNativeTypeToScriptingClass (51ms)
	initialDomainReloadingComplete (158ms)
	LoadAllAssembliesAndSetupDomain (1968ms)
		LoadAssemblies (2105ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (151ms)
			TypeCache.Refresh (29ms)
				TypeCache.ScanAssembly (7ms)
			ScanForSourceGeneratedMonoScriptInfo (71ms)
			ResolveRequiredComponents (46ms)
	FinalizeReload (11917ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (5668ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (14ms)
			SetLoadedEditorAssemblies (10ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (491ms)
			ProcessInitializeOnLoadAttributes (2970ms)
			ProcessInitializeOnLoadMethodAttributes (2126ms)
			AfterProcessingInitializeOnLoad (58ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (103ms)
Refreshing native plugins compatible for Editor in 27.44 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5485 Unused Serialized files (Serialized files now loaded: 0)
Unloading 58 unused Assets / (0.7 MB). Loaded Objects now: 6391.
Memory consumption went from 197.0 MB to 196.3 MB.
Total: 17.109000 ms (FindLiveObjects: 1.576000 ms CreateObjectMapping: 1.005800 ms MarkObjects: 12.910900 ms  DeleteObjects: 1.614500 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 43.720597 seconds.
  path: Assets/Scripts/Map/快速开始指南.md
  artifactKey: Guid(eab6739440ec8134eb73b121547d96af) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Map/快速开始指南.md using Guid(eab6739440ec8134eb73b121547d96af) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '1208ddee6452db239a4de9b087f114bb') in 0.136338 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 6.404142 seconds.
  path: Assets/Scripts/Map/噪声地图生成器使用说明.md
  artifactKey: Guid(1508616b32e227d47bc8ad06e3dfbc6d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Map/噪声地图生成器使用说明.md using Guid(1508616b32e227d47bc8ad06e3dfbc6d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '5c09ed1660664fa524bf0e40a081bad2') in 0.016589 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  5.316 seconds
Refreshing native plugins compatible for Editor in 230.56 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 27.196 seconds
Domain Reload Profiling: 32313ms
	BeginReloadAssembly (767ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (34ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (168ms)
	RebuildCommonClasses (679ms)
	RebuildNativeTypeToScriptingClass (308ms)
	initialDomainReloadingComplete (915ms)
	LoadAllAssembliesAndSetupDomain (2445ms)
		LoadAssemblies (2619ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (248ms)
			TypeCache.Refresh (24ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (7ms)
			ResolveRequiredComponents (205ms)
	FinalizeReload (27198ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (13569ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (155ms)
			SetLoadedEditorAssemblies (80ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (1686ms)
			ProcessInitializeOnLoadAttributes (9251ms)
			ProcessInitializeOnLoadMethodAttributes (2276ms)
			AfterProcessingInitializeOnLoad (121ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (87ms)
Refreshing native plugins compatible for Editor in 25.58 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5485 Unused Serialized files (Serialized files now loaded: 0)
Unloading 57 unused Assets / (0.7 MB). Loaded Objects now: 6395.
Memory consumption went from 197.0 MB to 196.3 MB.
Total: 15.051200 ms (FindLiveObjects: 1.011400 ms CreateObjectMapping: 0.816700 ms MarkObjects: 10.275300 ms  DeleteObjects: 2.946100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 853.805568 seconds.
  path: Assets/Scripts/Map/TerrainHeightShader.shader
  artifactKey: Guid(88410065fa93bd848a42000d30969f01) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Map/TerrainHeightShader.shader using Guid(88410065fa93bd848a42000d30969f01) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: '0824184fdff8ec4258e2d680524864a9') in 0.252185 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.915 seconds
Refreshing native plugins compatible for Editor in 23.68 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  6.313 seconds
Domain Reload Profiling: 8209ms
	BeginReloadAssembly (792ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (37ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (245ms)
	RebuildCommonClasses (61ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (93ms)
	LoadAllAssembliesAndSetupDomain (927ms)
		LoadAssemblies (1163ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (97ms)
			TypeCache.Refresh (30ms)
				TypeCache.ScanAssembly (6ms)
			ScanForSourceGeneratedMonoScriptInfo (42ms)
			ResolveRequiredComponents (22ms)
	FinalizeReload (6314ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3865ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (168ms)
			ProcessInitializeOnLoadAttributes (2023ms)
			ProcessInitializeOnLoadMethodAttributes (1603ms)
			AfterProcessingInitializeOnLoad (55ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (2ms)
		AwakeInstancesAfterBackupRestoration (63ms)
Refreshing native plugins compatible for Editor in 17.49 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5486 Unused Serialized files (Serialized files now loaded: 0)
Unloading 57 unused Assets / (0.7 MB). Loaded Objects now: 6400.
Memory consumption went from 197.0 MB to 196.3 MB.
Total: 13.691200 ms (FindLiveObjects: 0.773800 ms CreateObjectMapping: 0.581100 ms MarkObjects: 11.831800 ms  DeleteObjects: 0.502700 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 450.207076 seconds.
  path: Assets/Scripts/Map/NoiseMapGenerator.cs
  artifactKey: Guid(700eb1140f6f0ba41b18e9c191457b29) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Map/NoiseMapGenerator.cs using Guid(700eb1140f6f0ba41b18e9c191457b29) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 4 workers.
 -> (artifact id: 'dea362f5c00d809d4b7fceabaefb47c8') in 0.061955 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.474 seconds
Refreshing native plugins compatible for Editor in 20.52 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.300 seconds
Domain Reload Profiling: 5753ms
	BeginReloadAssembly (483ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (111ms)
	RebuildCommonClasses (113ms)
	RebuildNativeTypeToScriptingClass (32ms)
	initialDomainReloadingComplete (92ms)
	LoadAllAssembliesAndSetupDomain (732ms)
		LoadAssemblies (928ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (89ms)
			TypeCache.Refresh (21ms)
				TypeCache.ScanAssembly (5ms)
			ScanForSourceGeneratedMonoScriptInfo (43ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (4301ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1910ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (148ms)
			ProcessInitializeOnLoadAttributes (914ms)
			ProcessInitializeOnLoadMethodAttributes (802ms)
			AfterProcessingInitializeOnLoad (28ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (38ms)
Refreshing native plugins compatible for Editor in 21.56 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5485 Unused Serialized files (Serialized files now loaded: 0)
Unloading 58 unused Assets / (0.7 MB). Loaded Objects now: 6403.
Memory consumption went from 197.0 MB to 196.3 MB.
Total: 9.607400 ms (FindLiveObjects: 0.552400 ms CreateObjectMapping: 0.882100 ms MarkObjects: 7.742800 ms  DeleteObjects: 0.429000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  6.640 seconds
Refreshing native plugins compatible for Editor in 29.59 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  8.256 seconds
Domain Reload Profiling: 14755ms
	BeginReloadAssembly (721ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (23ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (198ms)
	RebuildCommonClasses (220ms)
	RebuildNativeTypeToScriptingClass (73ms)
	initialDomainReloadingComplete (376ms)
	LoadAllAssembliesAndSetupDomain (5108ms)
		LoadAssemblies (5139ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (339ms)
			TypeCache.Refresh (34ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (301ms)
	FinalizeReload (8257ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (4059ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (9ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (343ms)
			ProcessInitializeOnLoadAttributes (2265ms)
			ProcessInitializeOnLoadMethodAttributes (1401ms)
			AfterProcessingInitializeOnLoad (32ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (46ms)
Refreshing native plugins compatible for Editor in 22.89 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5485 Unused Serialized files (Serialized files now loaded: 0)
Unloading 57 unused Assets / (0.7 MB). Loaded Objects now: 6407.
Memory consumption went from 197.0 MB to 196.3 MB.
Total: 7.757600 ms (FindLiveObjects: 0.905700 ms CreateObjectMapping: 0.376100 ms MarkObjects: 5.861500 ms  DeleteObjects: 0.612300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  6.290 seconds
Refreshing native plugins compatible for Editor in 26.24 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  6.282 seconds
Domain Reload Profiling: 12550ms
	BeginReloadAssembly (5250ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (20ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (211ms)
	RebuildCommonClasses (69ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (88ms)
	LoadAllAssembliesAndSetupDomain (837ms)
		LoadAssemblies (3879ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (99ms)
			TypeCache.Refresh (24ms)
				TypeCache.ScanAssembly (7ms)
			ScanForSourceGeneratedMonoScriptInfo (49ms)
			ResolveRequiredComponents (22ms)
	FinalizeReload (6283ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2842ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (26ms)
			SetLoadedEditorAssemblies (9ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (292ms)
			ProcessInitializeOnLoadAttributes (1481ms)
			ProcessInitializeOnLoadMethodAttributes (1000ms)
			AfterProcessingInitializeOnLoad (33ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (1ms)
		AwakeInstancesAfterBackupRestoration (53ms)
Refreshing native plugins compatible for Editor in 24.49 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5485 Unused Serialized files (Serialized files now loaded: 0)
Unloading 57 unused Assets / (0.7 MB). Loaded Objects now: 6411.
Memory consumption went from 197.0 MB to 196.3 MB.
Total: 9.831000 ms (FindLiveObjects: 2.475400 ms CreateObjectMapping: 0.827300 ms MarkObjects: 5.861500 ms  DeleteObjects: 0.665300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.402 seconds
Refreshing native plugins compatible for Editor in 68.80 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 14.166 seconds
Domain Reload Profiling: 17533ms
	BeginReloadAssembly (978ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (28ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (166ms)
	RebuildCommonClasses (349ms)
	RebuildNativeTypeToScriptingClass (63ms)
	initialDomainReloadingComplete (204ms)
	LoadAllAssembliesAndSetupDomain (1770ms)
		LoadAssemblies (2137ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (91ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (64ms)
	FinalizeReload (14169ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (7100ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (51ms)
			SetLoadedEditorAssemblies (13ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (365ms)
			ProcessInitializeOnLoadAttributes (2620ms)
			ProcessInitializeOnLoadMethodAttributes (2763ms)
			AfterProcessingInitializeOnLoad (1256ms)
			EditorAssembliesLoaded (32ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (797ms)
Refreshing native plugins compatible for Editor in 210.71 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5485 Unused Serialized files (Serialized files now loaded: 0)
Unloading 57 unused Assets / (0.7 MB). Loaded Objects now: 6415.
Memory consumption went from 197.0 MB to 196.3 MB.
Total: 29.296600 ms (FindLiveObjects: 5.491100 ms CreateObjectMapping: 2.943100 ms MarkObjects: 17.975400 ms  DeleteObjects: 2.885600 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.893 seconds
Refreshing native plugins compatible for Editor in 38.34 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  6.380 seconds
Domain Reload Profiling: 9220ms
	BeginReloadAssembly (1468ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (26ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (731ms)
	RebuildCommonClasses (177ms)
	RebuildNativeTypeToScriptingClass (50ms)
	initialDomainReloadingComplete (157ms)
	LoadAllAssembliesAndSetupDomain (986ms)
		LoadAssemblies (1472ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (105ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (4ms)
			ScanForSourceGeneratedMonoScriptInfo (50ms)
			ResolveRequiredComponents (32ms)
	FinalizeReload (6381ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2810ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (8ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (203ms)
			ProcessInitializeOnLoadAttributes (1325ms)
			ProcessInitializeOnLoadMethodAttributes (1223ms)
			AfterProcessingInitializeOnLoad (33ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (61ms)
Refreshing native plugins compatible for Editor in 23.99 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5485 Unused Serialized files (Serialized files now loaded: 0)
Unloading 57 unused Assets / (0.7 MB). Loaded Objects now: 6419.
Memory consumption went from 197.0 MB to 196.3 MB.
Total: 20.292900 ms (FindLiveObjects: 0.733400 ms CreateObjectMapping: 0.333300 ms MarkObjects: 18.545800 ms  DeleteObjects: 0.678700 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.974 seconds
Refreshing native plugins compatible for Editor in 149.76 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 13.692 seconds
Domain Reload Profiling: 17630ms
	BeginReloadAssembly (1141ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (29ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (294ms)
	RebuildCommonClasses (185ms)
	RebuildNativeTypeToScriptingClass (31ms)
	initialDomainReloadingComplete (263ms)
	LoadAllAssembliesAndSetupDomain (2318ms)
		LoadAssemblies (2743ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (230ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (1ms)
			ResolveRequiredComponents (204ms)
	FinalizeReload (13692ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (6634ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (30ms)
			SetLoadedEditorAssemblies (12ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (315ms)
			ProcessInitializeOnLoadAttributes (2741ms)
			ProcessInitializeOnLoadMethodAttributes (3242ms)
			AfterProcessingInitializeOnLoad (292ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (3ms)
		AwakeInstancesAfterBackupRestoration (379ms)
Refreshing native plugins compatible for Editor in 185.28 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5485 Unused Serialized files (Serialized files now loaded: 0)
Unloading 57 unused Assets / (0.7 MB). Loaded Objects now: 6423.
Memory consumption went from 197.0 MB to 196.3 MB.
Total: 73.430900 ms (FindLiveObjects: 22.817600 ms CreateObjectMapping: 30.467500 ms MarkObjects: 19.663300 ms  DeleteObjects: 0.481300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 18.703 seconds
Refreshing native plugins compatible for Editor in 104.57 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 16.318 seconds
Domain Reload Profiling: 35006ms
	BeginReloadAssembly (647ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (26ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (166ms)
	RebuildCommonClasses (274ms)
	RebuildNativeTypeToScriptingClass (516ms)
	initialDomainReloadingComplete (394ms)
	LoadAllAssembliesAndSetupDomain (16846ms)
		LoadAssemblies (16820ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (364ms)
			TypeCache.Refresh (85ms)
				TypeCache.ScanAssembly (18ms)
			ScanForSourceGeneratedMonoScriptInfo (172ms)
			ResolveRequiredComponents (94ms)
	FinalizeReload (16330ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (10049ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (35ms)
			SetLoadedEditorAssemblies (12ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (390ms)
			ProcessInitializeOnLoadAttributes (2478ms)
			ProcessInitializeOnLoadMethodAttributes (6889ms)
			AfterProcessingInitializeOnLoad (245ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (1ms)
		AwakeInstancesAfterBackupRestoration (345ms)
Refreshing native plugins compatible for Editor in 40.74 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5485 Unused Serialized files (Serialized files now loaded: 0)
Unloading 57 unused Assets / (0.7 MB). Loaded Objects now: 6427.
Memory consumption went from 197.0 MB to 196.3 MB.
Total: 46.400900 ms (FindLiveObjects: 1.237500 ms CreateObjectMapping: 9.554500 ms MarkObjects: 22.396000 ms  DeleteObjects: 13.210700 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.282 seconds
Refreshing native plugins compatible for Editor in 29.11 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.787 seconds
Domain Reload Profiling: 6046ms
	BeginReloadAssembly (340ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (88ms)
	RebuildCommonClasses (70ms)
	RebuildNativeTypeToScriptingClass (29ms)
	initialDomainReloadingComplete (119ms)
	LoadAllAssembliesAndSetupDomain (700ms)
		LoadAssemblies (834ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (35ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (4788ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2341ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (14ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (181ms)
			ProcessInitializeOnLoadAttributes (1131ms)
			ProcessInitializeOnLoadMethodAttributes (988ms)
			AfterProcessingInitializeOnLoad (23ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (50ms)
Refreshing native plugins compatible for Editor in 39.51 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5485 Unused Serialized files (Serialized files now loaded: 0)
Unloading 57 unused Assets / (0.7 MB). Loaded Objects now: 6431.
Memory consumption went from 197.0 MB to 196.4 MB.
Total: 7.421600 ms (FindLiveObjects: 0.942000 ms CreateObjectMapping: 0.404900 ms MarkObjects: 5.640500 ms  DeleteObjects: 0.432800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  6.359 seconds
Refreshing native plugins compatible for Editor in 90.69 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  9.291 seconds
Domain Reload Profiling: 15595ms
	BeginReloadAssembly (1217ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (38ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (161ms)
	RebuildCommonClasses (204ms)
	RebuildNativeTypeToScriptingClass (39ms)
	initialDomainReloadingComplete (243ms)
	LoadAllAssembliesAndSetupDomain (4600ms)
		LoadAssemblies (5232ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (201ms)
			TypeCache.Refresh (60ms)
				TypeCache.ScanAssembly (14ms)
			ScanForSourceGeneratedMonoScriptInfo (110ms)
			ResolveRequiredComponents (25ms)
	FinalizeReload (9292ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3100ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (7ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (172ms)
			ProcessInitializeOnLoadAttributes (1026ms)
			ProcessInitializeOnLoadMethodAttributes (1844ms)
			AfterProcessingInitializeOnLoad (33ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (40ms)
Refreshing native plugins compatible for Editor in 46.59 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5485 Unused Serialized files (Serialized files now loaded: 0)
Unloading 57 unused Assets / (0.7 MB). Loaded Objects now: 6435.
Memory consumption went from 197.1 MB to 196.4 MB.
Total: 19.133500 ms (FindLiveObjects: 3.062400 ms CreateObjectMapping: 1.875000 ms MarkObjects: 13.459200 ms  DeleteObjects: 0.735300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.037 seconds
Refreshing native plugins compatible for Editor in 30.33 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 12.956 seconds
Domain Reload Profiling: 15969ms
	BeginReloadAssembly (739ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (21ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (188ms)
	RebuildCommonClasses (211ms)
	RebuildNativeTypeToScriptingClass (31ms)
	initialDomainReloadingComplete (241ms)
	LoadAllAssembliesAndSetupDomain (1776ms)
		LoadAssemblies (2047ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (101ms)
			TypeCache.Refresh (25ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (1ms)
			ResolveRequiredComponents (69ms)
	FinalizeReload (12972ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (6202ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (25ms)
			SetLoadedEditorAssemblies (19ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (425ms)
			ProcessInitializeOnLoadAttributes (2552ms)
			ProcessInitializeOnLoadMethodAttributes (3111ms)
			AfterProcessingInitializeOnLoad (71ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (1ms)
		AwakeInstancesAfterBackupRestoration (124ms)
Refreshing native plugins compatible for Editor in 62.93 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5485 Unused Serialized files (Serialized files now loaded: 0)
Unloading 57 unused Assets / (0.7 MB). Loaded Objects now: 6439.
Memory consumption went from 197.0 MB to 196.4 MB.
Total: 18.321500 ms (FindLiveObjects: 2.566500 ms CreateObjectMapping: 3.104700 ms MarkObjects: 12.204700 ms  DeleteObjects: 0.444600 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
