using UnityEngine;

/// <summary>
/// 噪声地图生成器的参数设置
/// 使用ScriptableObject来存储所有配置参数，实现数据与逻辑分离
/// </summary>
[CreateAssetMenu(fileName = "NoiseMapSettings", menuName = "Map Generation/Noise Map Settings", order = 1)]
public class NoiseMapSettings : ScriptableObject
{
    [Header("地图基础设置")]
    [SerializeField] public int mapWidth = 256;
    [SerializeField] public int mapHeight = 256;
    [SerializeField] public int seed = 12345;

    [Header("噪声参数")]
    [SerializeField] public float noiseScale = 50f;
    [SerializeField] public int octaves = 4;
    [SerializeField] public float persistence = 0.5f;
    [SerializeField] public float lacunarity = 2f;
    [SerializeField] public Vector2 offset = Vector2.zero;

    [Header("多倍频程设置")]
    [SerializeField] public bool useCustomAmplitudes = false;
    [SerializeField] public float[] customAmplitudes = new float[] { 1f, 0.5f, 0.25f, 0.125f, 0.0625f, 0.03125f };
    [SerializeField] public bool normalizeAmplitudes = true;

    [Header("高度重分布")]
    [SerializeField] public bool useRedistribution = true;
    [SerializeField] public RedistributionType redistributionType = RedistributionType.Power;
    [SerializeField] public float redistributionExponent = 1.2f;
    [SerializeField] public AnimationCurve redistributionCurve = AnimationCurve.Linear(0, 0, 1, 1);
    [SerializeField] public float terraceCount = 5f;
    [SerializeField] public float terraceSmoothing = 0.1f;

    [Header("预览设置")]
    [SerializeField] public bool autoUpdate = true;
    [SerializeField] public bool useTerrainColoring = true;
    [SerializeField] public TerrainColorMode colorMode = TerrainColorMode.Biomes;
    [SerializeField] public bool showStatistics = true;
    [SerializeField] public FilterMode textureFilterMode = FilterMode.Bilinear;

    [Header("圆滑化设置")]
    [SerializeField] public bool useSmoothTerrain = true;
    [SerializeField] public int smoothingIterations = 2;
    [SerializeField] public float smoothingStrength = 0.5f;

    [Header("导出设置")]
    [SerializeField] public bool autoSaveOnGenerate = false;
    [SerializeField] public string exportPath = "Assets/GeneratedMaps/";

    /// <summary>
    /// 在编辑器中验证参数
    /// </summary>
    void OnValidate()
    {
        if (mapWidth < 1) mapWidth = 1;
        if (mapHeight < 1) mapHeight = 1;
        if (lacunarity < 1) lacunarity = 1;
        if (octaves < 0) octaves = 0;
        if (noiseScale <= 0) noiseScale = 0.0001f;
        if (smoothingIterations < 0) smoothingIterations = 0;
        if (smoothingStrength < 0) smoothingStrength = 0;
        if (terraceCount < 1) terraceCount = 1;
        if (terraceSmoothing < 0) terraceSmoothing = 0;
    }

    /// <summary>
    /// 创建默认设置
    /// </summary>
    /// <returns>包含默认参数的NoiseMapSettings实例</returns>
    public static NoiseMapSettings CreateDefault()
    {
        NoiseMapSettings settings = CreateInstance<NoiseMapSettings>();
        settings.name = "Default Noise Map Settings";
        
        // 设置默认值（已在字段声明中设置）
        return settings;
    }

    /// <summary>
    /// 复制当前设置
    /// </summary>
    /// <returns>当前设置的副本</returns>
    public NoiseMapSettings Clone()
    {
        NoiseMapSettings clone = CreateInstance<NoiseMapSettings>();
        
        // 复制所有字段
        clone.mapWidth = this.mapWidth;
        clone.mapHeight = this.mapHeight;
        clone.seed = this.seed;
        
        clone.noiseScale = this.noiseScale;
        clone.octaves = this.octaves;
        clone.persistence = this.persistence;
        clone.lacunarity = this.lacunarity;
        clone.offset = this.offset;
        
        clone.useCustomAmplitudes = this.useCustomAmplitudes;
        clone.customAmplitudes = (float[])this.customAmplitudes.Clone();
        clone.normalizeAmplitudes = this.normalizeAmplitudes;
        
        clone.useRedistribution = this.useRedistribution;
        clone.redistributionType = this.redistributionType;
        clone.redistributionExponent = this.redistributionExponent;
        clone.redistributionCurve = new AnimationCurve(this.redistributionCurve.keys);
        clone.terraceCount = this.terraceCount;
        clone.terraceSmoothing = this.terraceSmoothing;
        
        clone.autoUpdate = this.autoUpdate;
        clone.useTerrainColoring = this.useTerrainColoring;
        clone.colorMode = this.colorMode;
        clone.showStatistics = this.showStatistics;
        clone.textureFilterMode = this.textureFilterMode;
        
        clone.useSmoothTerrain = this.useSmoothTerrain;
        clone.smoothingIterations = this.smoothingIterations;
        clone.smoothingStrength = this.smoothingStrength;
        
        clone.autoSaveOnGenerate = this.autoSaveOnGenerate;
        clone.exportPath = this.exportPath;
        
        return clone;
    }
}
