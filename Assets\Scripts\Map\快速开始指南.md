# 噪声地图生成器 - 快速开始指南

## 🚀 5分钟快速设置

### 步骤1：创建基础对象
1. 在Hierarchy中右键 → Create Empty，命名为"NoiseMapGenerator"
2. 选中该对象，在Inspector中点击"Add Component"
3. 搜索并添加"NoiseMapGenerator"组件

### 步骤2：创建显示对象
1. 在Hierarchy中右键 → 3D Object → Quad，命名为"MapDisplay"
2. 将Quad的Transform设置为：
   - Position: (0, 0, 0)
   - Rotation: (90, 0, 0) // 让Quad平躺显示
   - Scale: (10, 10, 1)

### 步骤3：连接组件
1. 选中"NoiseMapGenerator"对象
2. 将"MapDisplay"对象拖拽到NoiseMapGenerator组件的"纹理渲染器"字段

### 步骤4：生成第一张地图
1. 在NoiseMapGenerator组件中点击"生成地图"按钮
2. 你应该能看到一张黑白的噪声地图出现在Quad上

### 步骤5：尝试不同效果
1. **启用地形着色**：勾选"使用地形着色"
2. **选择着色模式**：尝试"Biomes"模式查看彩色地形
3. **调整参数**：
   - 修改"随机种子"查看不同地图
   - 调整"噪声缩放"改变地形大小
   - 修改"重分布指数"改变地形形状

## 🎨 推荐的第一次体验参数

```
地图宽度: 256
地图高度: 256
随机种子: 12345
噪声缩放: 50
倍频程数量: 4
持续性: 0.5
间隙性: 2
重分布类型: Power
重分布指数: 1.5
使用地形着色: ✓
着色模式: Biomes
```

## 🔧 常见问题解决

### 问题1：看不到地图
**解决方案**：
- 确保Camera能看到Quad对象
- 检查Quad的材质是否正确接收纹理
- 确认"纹理渲染器"字段已正确设置

### 问题2：地图全黑或全白
**解决方案**：
- 调整"噪声缩放"参数（推荐20-100之间）
- 检查"重分布指数"是否过大或过小
- 尝试不同的随机种子

### 问题3：地图没有细节
**解决方案**：
- 增加"倍频程数量"（推荐4-6个）
- 调整"持续性"参数（推荐0.3-0.7）
- 启用"使用自定义振幅"并使用预设

## 🎯 进阶技巧

### 创建岛屿效果
1. 重分布类型选择"Power"
2. 重分布指数设为2.0或更高
3. 使用Biomes着色模式

### 创建山脉效果
1. 重分布类型选择"InversePower"
2. 重分布指数设为1.8
3. 增加倍频程数量到6

### 创建梯田效果
1. 重分布类型选择"Terraces"
2. 梯田数量设为5-8
3. 梯田平滑度设为0.1

## 📊 性能建议

- **开发阶段**：使用256x256或512x512分辨率
- **最终产品**：根据需要可以使用1024x1024或更高
- **实时预览**：开发时开启"自动更新"，发布时关闭
- **倍频程**：超过6个通常收益递减

## 🎮 游戏集成建议

### 运行时生成
```csharp
// 在脚本中动态生成地图
NoiseMapGenerator generator = GetComponent<NoiseMapGenerator>();
generator.GenerateMap();
float[,] heightMap = generator.GetNoiseMap();
```

### 获取统计信息
```csharp
// 获取地图统计
var stats = generator.GetMapStatistics();
Debug.Log($"水域占比: {stats.waterPercentage:F1}%");
```

### 导出地图数据
```csharp
// 导出为纹理
Texture2D texture = NoiseMapGenerator.TextureFromHeightMap(heightMap);
// 或者带地形着色
Texture2D coloredTexture = NoiseMapGenerator.TextureFromHeightMapWithTerrain(
    heightMap, TerrainColorMode.Biomes);
```

## 🔗 相关资源

- **完整文档**：查看"噪声地图生成器使用说明.md"
- **Red Blob Games原文**：https://www.redblobgames.com/maps/terrain-from-noise/
- **Unity Perlin Noise文档**：Unity官方文档

---

**提示**：如果遇到任何问题，请先查看完整的使用说明文档，那里有更详细的参数解释和故障排除指南。
