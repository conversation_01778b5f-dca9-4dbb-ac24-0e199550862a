@charset "UTF-8";.titlebar-button-container.mod-left{opacity:1}body:not(.qe-title-center):not(.qe-title-hide) .titlebar-text.qe-replacement{text-align:right;justify-content:flex-end;align-items:flex-end}body.qe-title-hide .titlebar-text.qe-replacement{display:none}body.obsidian-themepocalypse:not(.qe-hide-breadcrumbs) #quick-explorer{display:inherit}body.obsidian-themepocalypse:not(.qe-hide-breadcrumbs) #quick-explorer .explorable{display:inherit}body.qe-hide-breadcrumbs #quick-explorer{display:none}body.is-frameless.is-hidden-frameless:not(.qe-hide-breadcrumbs) .status-bar,body:not(.is-frameless):not(.qe-hide-breadcrumbs) .status-bar{position:static}body.is-frameless.is-hidden-frameless:not(.qe-hide-breadcrumbs) .status-bar .status-bar-item.left-region,body:not(.is-frameless):not(.qe-hide-breadcrumbs) .status-bar .status-bar-item.left-region{order:-9999;flex-grow:1}#quick-explorer{display:inline-block;padding-left:10px;padding-right:10px;z-index:var(--layer-cover);background-color:var(--background-translucent)!important}#quick-explorer .explorable{font-size:var(--font-ui-small, 12px);opacity:.75;cursor:pointer;padding:0;display:unset}.status-bar #quick-explorer .explorable{font-size:var(--status-bar-font-size, var(--font-ui-small, 12px));opacity:1}#quick-explorer .explorable.selected,#quick-explorer .explorable:hover{background-color:var(--interactive-accent-hover);color:var(--text-on-accent);opacity:1}#quick-explorer .explorable-separator:before{content:"\a0/\a0"}.menu.qe-popup-menu~.popover.hover-popover{z-index:var(--layer-menu)}.qe-popup-menu .menu-item:not(.is-disabled):not(.is-label){display:flex;align-items:center}.qe-popup-menu .menu-item:not(.is-disabled):not(.is-label).selected{background-color:var(--interactive-accent-hover)}.qe-popup-menu .menu-item:not(.is-disabled):not(.is-label):hover:not(.selected){background-color:var(--background-primary);color:var(--text-normal)}.qe-popup-menu .menu-item:not(.is-disabled):not(.is-label):hover:not(.selected) .menu-item-icon{color:var(--text-muted)}.qe-popup-menu .menu-item:not(.is-disabled):not(.is-label) .menu-item-title{flex-grow:1}.qe-popup-menu .menu-item:not(.is-disabled):not(.is-label) .nav-file-tag{margin-left:1.5em;opacity:.5;background-color:transparent}.qe-popup-menu .menu-item:not(.is-disabled):not(.is-label) .nav-file-tag.qe-file-count{font-size:75%}.qe-popup-menu .menu-item:not(.is-disabled):not(.is-label):hover,.qe-popup-menu .menu-item:not(.is-disabled):not(.is-label).selected{color:var(--text-on-accent)}.qe-popup-menu .menu-item:not(.is-disabled):not(.is-label):hover .menu-item-icon,.qe-popup-menu .menu-item:not(.is-disabled):not(.is-label).selected .menu-item-icon{color:var(--text-on-accent)}.qe-popup-menu .menu-item:not(.is-disabled):not(.is-label):hover .nav-file-tag,.qe-popup-menu .menu-item:not(.is-disabled):not(.is-label).selected .nav-file-tag{opacity:1}.view-header-title-parent:before{content:"/";padding:2px 1px;color:var(--text-faint)}.view-header-breadcrumb.is-exploring,.view-header-title-parent.is-exploring:before{background-color:var(--background-modifier-hover);color:var(--text-normal)}
/*! /* @settings
name: Quick Explorer
id: quick-explorer
settings:
    -   id: qe-obsidian-title
        title: Obsidian Title (Vault+Version)
        description: What should happen to the Obsidian title text?
        type: class-select
        default: qe-title-right
        options:
            - label: Show it on the right
              value: qe-title-right
            - label: Show it in the center
              value: qe-title-center
            - label: Hide it entirely
              value: qe-title-hide

    -   id: qe-hide-breadcrumbs
        title: Hide Quick Explorer
        description: Hide quick explorer (and use tab titlebar breadcrumbs on 0.16)
        type: class-toggle
*/
