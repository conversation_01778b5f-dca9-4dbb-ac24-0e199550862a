/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var Cv=Object.create;var br=Object.defineProperty;var Av=Object.getOwnPropertyDescriptor;var xv=Object.getOwnPropertyNames;var Ov=Object.getPrototypeOf,Rv=Object.prototype.hasOwnProperty;var _v=(e,t)=>()=>(e&&(t=e(e=0)),t);var D=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),Id=(e,t)=>{for(var n in t)br(e,n,{get:t[n],enumerable:!0})},Zd=(e,t,n,a)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of xv(t))!Rv.call(e,i)&&i!==n&&br(e,i,{get:()=>t[i],enumerable:!(a=Av(t,i))||a.enumerable});return e};var Ce=(e,t,n)=>(n=e!=null?Cv(Ov(e)):{},Zd(t||!e||!e.__esModule?br(n,"default",{value:e,enumerable:!0}):n,e)),nu=e=>Zd(br({},"__esModule",{value:!0}),e);var Kd=D((z3,Dv)=>{Dv.exports=`<svg xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="xMidYMid" viewBox="0 0 256 283">
  <path fill="#EA4335" d="M119.5531 134.9164 1.06 259.0605c2.6963 9.5565 9.6584 17.3301 18.861 21.0595 9.2025 3.7294 19.6122 2.9957 28.2008-1.9875l133.327-75.9346-61.8956-67.2815Z"/>
  <path fill="#FBBC04" d="M239.3708 113.8136 181.7135 80.791l-64.8975 56.951 65.1624 64.2794 57.2158-32.6695C249.5257 163.9425 256 153.2447 256 141.5828c0-11.662-6.4743-22.3598-16.8058-27.7692h.1766Z"/>
  <path fill="#4285F4" d="M1.0599 23.4868A30.565 30.565 0 0 0 0 31.61v219.3273a32.3326 32.3326 0 0 0 1.0599 8.1232L123.6148 138.095 1.0599 23.4868Z"/>
  <path fill="#34A853" d="m120.4361 141.2737 61.2774-60.4828L48.5632 4.5032A32.8468 32.8468 0 0 0 32.0518 0C17.6444-.0285 4.9784 9.5342 1.0599 23.3985l119.3762 117.8752Z"/>
</svg>
`});var Wd=D((q3,Bv)=>{Bv.exports=`<svg xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="xMidYMid" viewBox="0 0 256 256">
  <defs>
    <linearGradient id="a" x1="50%" x2="50%" y1="0%" y2="100%">
      <stop offset="0%" stop-color="#17C9FB"/>
      <stop offset="100%" stop-color="#1A74E8"/>
    </linearGradient>
  </defs>
  <path fill="url(#a)" d="M56.0638 0h143.8724C230.8994 0 256 25.1006 256 56.0638v143.8724C256 230.8994 230.8994 256 199.9362 256H56.0638C25.1006 256 0 230.8994 0 199.9362V56.0638C0 25.1006 25.1006 0 56.0638 0Z"/>
  <path fill="#FFF" d="m82.0417 185.8103.024.0076-8.7526 15.16c-3.1948 5.5338-10.2713 7.4299-15.805 4.2348-5.5336-3.1945-7.4297-10.271-4.2349-15.8048l6.4479-11.1679.6187-1.0717c1.1051-1.5887 3.8322-4.3295 9.2872-3.8145 0 0 12.8368 1.3932 13.7656 8.065.0002 0 .1269 2.1957-1.351 4.3915Zm124.1434-38.721H178.891c-1.8586-.1244-2.6706-.7882-2.989-1.174l-.0203-.0353-29.2177-50.6061-.0377.0253-1.7525-2.5125c-2.8715-4.3914-7.4316 6.8406-7.4316 6.8406-5.4446 12.5155.7724 26.7452 2.9404 31.0459l40.5814 70.289c3.1945 5.5338 10.271 7.4299 15.8048 4.2348 5.5335-3.1945 7.4296-10.271 4.2348-15.8048l-10.1479-17.5762c-.1963-.4257-.5386-1.5822 1.5424-1.5868h13.7871c6.3899 0 11.57-5.18 11.57-11.57 0-6.3898-5.1801-11.57-11.57-11.57Zm-53.014 15.729s1.4568 7.4107-4.1804 7.4107h-5.6371v.0002H48.0919c-6.39 0-11.57-5.18-11.57-11.57 0-6.3898 5.18-11.57 11.57-11.57h25.94c4.188-.2426 5.1808-2.6601 5.1808-2.6601l.0228.0116 33.8604-58.6481-.0101-.002c.6174-1.133.1033-2.2035.0139-2.373l-11.1823-19.3682c-3.195-5.5338-1.299-12.61 4.2348-15.8048 5.5338-3.195 12.6097-1.299 15.8048 4.2348l5.1862 8.983 5.177-8.967c3.1948-5.5338 10.271-7.4299 15.8048-4.2348 5.5338 3.195 7.4299 10.2712 4.2348 15.8048l-47.1176 81.61c-.206.4967-.2691 1.2766 1.2637 1.4139H134.67l.006.2744s16.2781.2533 18.495 15.4546Z"/>
</svg>
`});var dh=D(Ae=>{"use strict";var qv=class{constructor(e,t="Microsoft Server Speech Text to Speech Voice (en-US, EmmaMultilingualNeural)",n={}){this.ws=null,this.WSS_URL="wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1",this.TRUSTED_CLIENT_TOKEN="6A5AA1D4EAFF4E9FB37E23D68491D6F4",this.text=e,this.voice=t,this.rate=n.rate||"+0%",this.volume=n.volume||"+0%",this.pitch=n.pitch||"+0Hz"}async synthesize(){if(await this.connect(),!this.ws||this.ws.readyState!==WebSocket.OPEN)throw new Error("WebSocket is not connected.");return this.ws.send(this.createSpeechConfig()),this.ws.send(this.createSSML()),new Promise((e,t)=>{let n=[],a=[];this.ws&&(this.ws.onmessage=i=>{if(typeof i.data=="string"){let{headers:s,body:r}=this.parseMessage(i.data);if(s.Path==="audio.metadata")try{let l=JSON.parse(r);if(l.Metadata&&Array.isArray(l.Metadata)){let o=l.Metadata.filter(u=>u.Type==="WordBoundary"&&u.Data).map(u=>({offset:u.Data.Offset,duration:u.Data.Duration,text:u.Data.text.Text}));a=a.concat(o)}}catch(l){}else s.Path==="turn.end"&&this.ws&&this.ws.close()}else i.data instanceof Blob&&i.data.arrayBuffer().then(s=>{let l=new DataView(s).getUint16(0);if(s.byteLength>l+2){let o=new Uint8Array(s,l+2);n.push(o)}})},this.ws.onclose=()=>{let i=new Blob(n,{type:"audio/mpeg"});e({audio:i,subtitle:a})},this.ws.onerror=i=>{t(i)})})}connect(){let e=this.generateConnectionId(),t=this.generateSecMsGec(),n=`${this.WSS_URL}?TrustedClientToken=${this.TRUSTED_CLIENT_TOKEN}&ConnectionId=${e}&Sec-MS-GEC=${t}&Sec-MS-GEC-Version=1-130.0.2849.68`;return this.ws=new WebSocket(n),new Promise((a,i)=>{if(!this.ws)return i(new Error("WebSocket not initialized"));this.ws.onopen=()=>{a()},this.ws.onerror=s=>{i(s)}})}parseMessage(e){let t=e.split(`\r
\r
`),n=t[0].split(`\r
`),a={};return n.forEach(i=>{let[s,r]=i.split(":",2);s&&r&&(a[s.trim()]=r.trim())}),{headers:a,body:t[1]||""}}createSpeechConfig(){let e={context:{synthesis:{audio:{metadataoptions:{sentenceBoundaryEnabled:!1,wordBoundaryEnabled:!0},outputFormat:"audio-24khz-48kbitrate-mono-mp3"}}}};return`X-Timestamp:${this.getTimestamp()}\r
Content-Type:application/json; charset=utf-8\r
Path:speech.config\r
\r
${JSON.stringify(e)}`}createSSML(){let e=`<speak version='1.0' xmlns='http://www.w3.org/2001/10/synthesis' xml:lang='en-US'>
      <voice name='${this.voice}'>
        <prosody pitch='${this.pitch}' rate='${this.rate}' volume='${this.volume}'>
          ${this.escapeXml(this.text)}
        </prosody>
      </voice>
    </speak>`;return`X-RequestId:${this.generateConnectionId()}\r
Content-Type:application/ssml+xml\r
X-Timestamp:${this.getTimestamp()}Z\r
Path:ssml\r
\r
${e}`}generateConnectionId(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,e=>{let t=Math.random()*16|0;return(e==="x"?t:t&3|8).toString(16)})}getTimestamp(){return new Date().toISOString().replace(/[:-]|\.\d{3}/g,"")}escapeXml(e){return e.replace(/[<>&'"]/g,t=>{switch(t){case"<":return"&lt;";case">":return"&gt;";case"&":return"&amp;";case"'":return"&apos;";case'"':return"&quot;";default:return t}})}async generateSecMsGec(){let n=Date.now()/1e3;n+=11644473600,n-=n%300,n*=1e7;let a=`${n.toFixed(0)}${this.TRUSTED_CLIENT_TOKEN}`,s=new TextEncoder().encode(a),r=await crypto.subtle.digest("SHA-256",s);return Array.from(new Uint8Array(r)).map(o=>o.toString(16).padStart(2,"0")).join("").toUpperCase()}};function ru(){let e=new Uint8Array(16);crypto.getRandomValues(e),e[6]=e[6]&15|64,e[8]=e[8]&63|128;let t=Array.from(e,a=>a.toString(16).padStart(2,"0")).join("");return`${t.slice(0,8)}-${t.slice(8,12)}-${t.slice(12,16)}-${t.slice(16,20)}-${t.slice(20,32)}`.replace(/-/g,"")}function Hv(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}function Qv(e){return e.replace(/&quot;/g,'"').replace(/&apos;/g,"'").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&")}function jv(e){return e.replace(/[\u0000-\u0008\u000B\u000C\u000E-\u001F]/g," ")}function lu(){return new Date().toUTCString().replace("GMT","GMT+0000 (Coordinated Universal Time)")}function nh(e,t,n,a,i){return`<speak version='1.0' xmlns='http://www.w3.org/2001/10/synthesis' xml:lang='en-US'><voice name='${e}'><prosody pitch='${a}' rate='${t}' volume='${n}'>${i}</prosody></voice></speak>`}function ah(e,t,n){return`X-RequestId:${e}\r
Content-Type:application/ssml+xml\r
X-Timestamp:${t}Z\r
Path:ssml\r
\r
${n}`}function Vv(e,t,n,a){return 65536-(ah(ru(),lu(),nh(e,t,n,a,"")).length+50)}var ia=class extends Error{constructor(e){super(e),this.name="EdgeTTSException"}},wr=class extends ia{constructor(e){super(e),this.name="SkewAdjustmentError"}},ou=class extends ia{constructor(e){super(e),this.name="UnknownResponse"}},Bt=class extends ia{constructor(e){super(e),this.name="UnexpectedResponse"}},ih=class extends ia{constructor(e){super(e),this.name="NoAudioReceived"}},Mr=class extends ia{constructor(e){super(e),this.name="WebSocketError"}},Cr=class extends ia{constructor(e){super(e),this.name="ValueError"}},Fv=class Gi{constructor({voice:t,rate:n="+0%",volume:a="+0%",pitch:i="+0Hz"}){this.voice=t,this.rate=n,this.volume=a,this.pitch=i,this.validate()}validate(){let t=/^([a-z]{2,})-([A-Z]{2,})-(.+Neural)$/.exec(this.voice);if(t){let[,n]=t,[,,a,i]=t;if(i.includes("-")){let s=i.split("-");a+=`-${s[0]}`,i=s[1]}this.voice=`Microsoft Server Speech Text to Speech Voice (${n}-${a}, ${i})`}Gi.validateStringParam("voice",this.voice,/^Microsoft Server Speech Text to Speech Voice \(.+,.+\)$/),Gi.validateStringParam("rate",this.rate,/^[+-]\d+%$/),Gi.validateStringParam("volume",this.volume,/^[+-]\d+%$/),Gi.validateStringParam("pitch",this.pitch,/^[+-]\d+Hz$/)}static validateStringParam(t,n,a){if(typeof n!="string")throw new TypeError(`${t} must be a string`);if(!a.test(n))throw new Cr(`Invalid ${t} '${n}'.`)}},sh="speech.platform.bing.com/consumer/speech/synthesize/readaloud",uu="6A5AA1D4EAFF4E9FB37E23D68491D6F4",Gv=`wss://${sh}/edge/v1?TrustedClientToken=${uu}`,$v=`https://${sh}/voices/list?trustedclienttoken=${uu}`,Yv="en-US-EmmaMultilingualNeural",rh="130.0.2849.68",Ar=rh.split(".")[0],lh=`1-${rh}`,Xv={"User-Agent":`Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/${Ar}.0.0.0 Safari/537.36 Edg/${Ar}.0.0.0`,"Accept-Encoding":"gzip, deflate, br","Accept-Language":"en-US,en;q=0.9"},Iv={...Xv,Authority:"speech.platform.bing.com","Sec-CH-UA":`" Not;A Brand";v="99", "Microsoft Edge";v="${Ar}", "Chromium";v="${Ar}"`,"Sec-CH-UA-Mobile":"?0",Accept:"*/*","Sec-Fetch-Site":"none","Sec-Fetch-Mode":"cors","Sec-Fetch-Dest":"empty"},Zv=11644473600,Kv=1e9,oh=class aa{static adjClockSkewSeconds(t){aa.clockSkewSeconds+=t}static getUnixTimestamp(){return Date.now()/1e3+aa.clockSkewSeconds}static parseRfc2616Date(t){try{return new Date(t).getTime()/1e3}catch(n){return null}}static handleClientResponseError(t){if(!t.headers)throw new wr("No headers in response.");let n=t.headers.date||t.headers.Date;if(!n)throw new wr("No server date in headers.");let a=aa.parseRfc2616Date(n);if(a===null)throw new wr(`Failed to parse server date: ${n}`);let i=aa.getUnixTimestamp();aa.adjClockSkewSeconds(a-i)}static async generateSecMsGec(){let t=aa.getUnixTimestamp();t+=Zv,t-=t%300,t*=Kv/100;let n=`${t.toFixed(0)}${uu}`,i=new TextEncoder().encode(n),s=await crypto.subtle.digest("SHA-256",i);return Array.from(new Uint8Array(s)).map(l=>l.toString(16).padStart(2,"0")).join("").toUpperCase()}};oh.clockSkewSeconds=0;var Or=oh,Tr=class{static from(e,t){if(typeof e=="string")return new TextEncoder().encode(e);if(e instanceof ArrayBuffer)return new Uint8Array(e);if(e instanceof Uint8Array)return e;throw new Error("Unsupported input type for BrowserBuffer.from")}static concat(e){let t=e.reduce((i,s)=>i+s.length,0),n=new Uint8Array(t),a=0;for(let i of e)n.set(i,a),a+=i.length;return n}};function Wv(e){let t=new TextDecoder().decode(e),n=t.indexOf(`\r
\r
`),a={};if(n!==-1){let r=t.substring(0,n).split(`\r
`);for(let l of r){let[o,u]=l.split(":",2);o&&u&&(a[o]=u.trim())}}let i=new TextEncoder().encode(t.substring(0,n+4)).length;return[a,e.slice(i)]}function Jd(e){if(e.length<2)throw new Error("Message too short to contain header length");let t=e[0]<<8|e[1],n={};if(t>0&&t+2<=e.length){let a=e.slice(2,t+2),s=new TextDecoder().decode(a).split(`\r
`);for(let r of s){let[l,o]=r.split(":",2);l&&o&&(n[l]=o.trim())}}return[n,e.slice(t+2)]}function Jv(e,t){return function*(){let n=new TextEncoder().encode(e);if(t<=0)throw new Error("byteLength must be greater than 0");for(;n.length>t;){let i=t,s=n.slice(0,t),r=new TextDecoder().decode(s),l=r.lastIndexOf(`
`),o=r.lastIndexOf(" ");l>0?i=new TextEncoder().encode(r.substring(0,l)).length:o>0&&(i=new TextEncoder().encode(r.substring(0,o)).length);let u=n.slice(0,i),c=new TextDecoder().decode(u).trim();c&&(yield new TextEncoder().encode(c)),n=n.slice(i)}let a=new TextDecoder().decode(n).trim();a&&(yield new TextEncoder().encode(a))}()}var uh=class{constructor(e,t={}){if(this.state={partialText:Tr.from(""),offsetCompensation:0,lastDurationOffset:0,streamWasCalled:!1},this.ttsConfig=new Fv({voice:t.voice||Yv,rate:t.rate,volume:t.volume,pitch:t.pitch}),typeof e!="string")throw new TypeError("text must be a string");this.texts=Jv(Hv(jv(e)),Vv(this.ttsConfig.voice,this.ttsConfig.rate,this.ttsConfig.volume,this.ttsConfig.pitch)),this.connectionTimeout=t.connectionTimeout}parseMetadata(e){let t=JSON.parse(new TextDecoder().decode(e));for(let n of t.Metadata){let a=n.Type;if(a==="WordBoundary"){let i=n.Data.Offset+this.state.offsetCompensation,s=n.Data.Duration;return{type:a,offset:i,duration:s,text:Qv(n.Data.text.Text)}}if(a!=="SessionEnd")throw new ou(`Unknown metadata type: ${a}`)}throw new Bt("No WordBoundary metadata found")}async*_stream(){let e=`${Gv}&Sec-MS-GEC=${await Or.generateSecMsGec()}&Sec-MS-GEC-Version=${lh}&ConnectionId=${ru()}`,t=new WebSocket(e),n=[],a=null,i;this.connectionTimeout&&(i=window.setTimeout(()=>{t.close(),n.push(new Mr("Connection timeout")),a&&a()},this.connectionTimeout)),t.onmessage=r=>{i&&(window.clearTimeout(i),i=void 0);let l=r.data;if(typeof l=="string"){let[o,u]=Wv(Tr.from(l)),c=o.Path;if(c==="audio.metadata")try{let f=this.parseMetadata(u);this.state.lastDurationOffset=f.offset+f.duration,n.push(f)}catch(f){n.push(f)}else c==="turn.end"?(this.state.offsetCompensation=this.state.lastDurationOffset,t.close()):c!=="response"&&c!=="turn.start"&&n.push(new ou(`Unknown path received: ${c}`))}else if(l instanceof ArrayBuffer){let o=Tr.from(l);if(o.length<2)n.push(new Bt("We received a binary message, but it is missing the header length."));else{let[u,c]=Jd(o);u.Path!=="audio"?n.push(new Bt("Received binary message, but the path is not audio.")):u["Content-Type"]!=="audio/mpeg"?c.length>0&&n.push(new Bt("Received binary message, but with an unexpected Content-Type.")):c.length===0?n.push(new Bt("Received binary message, but it is missing the audio data.")):n.push({type:"audio",data:c})}}else l instanceof Blob&&l.arrayBuffer().then(o=>{let u=Tr.from(o);if(u.length<2)n.push(new Bt("We received a binary message, but it is missing the header length."));else{let[c,f]=Jd(u);c.Path!=="audio"?n.push(new Bt("Received binary message, but the path is not audio.")):c["Content-Type"]!=="audio/mpeg"?f.length>0&&n.push(new Bt("Received binary message, but with an unexpected Content-Type.")):f.length===0?n.push(new Bt("Received binary message, but it is missing the audio data.")):n.push({type:"audio",data:f})}a&&a()});a&&a()},t.onerror=r=>{i&&(window.clearTimeout(i),i=void 0),n.push(new Mr("WebSocket error occurred")),a&&a()},t.onclose=()=>{i&&(window.clearTimeout(i),i=void 0),n.push("close"),a&&a()},await new Promise((r,l)=>{t.onopen=()=>{i&&(window.clearTimeout(i),i=void 0),r()},this.connectionTimeout&&setTimeout(()=>{t.readyState===WebSocket.CONNECTING&&(t.close(),l(new Mr("Connection timeout")))},this.connectionTimeout)}),t.send(`X-Timestamp:${lu()}\r
Content-Type:application/json; charset=utf-8\r
Path:speech.config\r
\r
{"context":{"synthesis":{"audio":{"metadataoptions":{"sentenceBoundaryEnabled":"false","wordBoundaryEnabled":"true"},"outputFormat":"audio-24khz-48kbitrate-mono-mp3"}}}}\r
`),t.send(ah(ru(),lu(),nh(this.ttsConfig.voice,this.ttsConfig.rate,this.ttsConfig.volume,this.ttsConfig.pitch,new TextDecoder().decode(this.state.partialText))));let s=!1;for(;;)if(n.length>0){let r=n.shift();if(r==="close"){if(!s)throw new ih("No audio was received.");break}else{if(r instanceof Error)throw r;r.type==="audio"&&(s=!0),yield r}}else await new Promise(r=>{a=r,setTimeout(r,50)})}async*stream(){if(this.state.streamWasCalled)throw new Error("stream can only be called once.");this.state.streamWasCalled=!0;for(let e of this.texts){this.state.partialText=e;for await(let t of this._stream())yield t}}};function eS(e){if(e.length===0)return new Uint8Array(0);if(e.length===1)return e[0];let t=e.reduce((i,s)=>i+s.length,0),n=new Uint8Array(t),a=0;for(let i of e)i.length>0&&(n.set(i,a),a+=i.length);return n}var tS=class{constructor(e,t="Microsoft Server Speech Text to Speech Voice (en-US, EmmaMultilingualNeural)",n={}){this.text=e,this.voice=t,this.rate=n.rate||"+0%",this.volume=n.volume||"+0%",this.pitch=n.pitch||"+0Hz"}async synthesize(){let e=new uh(this.text,{voice:this.voice,rate:this.rate,volume:this.volume,pitch:this.pitch}),t=[],n=[];for await(let s of e.stream())s.type==="audio"&&s.data?t.push(s.data):s.type==="WordBoundary"&&s.offset!==void 0&&s.duration!==void 0&&s.text!==void 0&&n.push({offset:s.offset,duration:s.duration,text:s.text});let a=eS(t);return{audio:new Blob([a],{type:"audio/mpeg"}),subtitle:n}}};function xr(e,t){let n=Math.floor(e/1e7),a=Math.floor(n/3600),i=Math.floor(n%3600/60),s=n%60,r=Math.floor(e%1e7/1e4),l=t==="vtt"?".":",";return`${Er(a)}:${Er(i)}:${Er(s)}${l}${Er(r,3)}`}function Er(e,t=2){return e.toString().padStart(t,"0")}function nS(e){let t=`WEBVTT

`;return e.forEach((n,a)=>{let i=xr(n.offset,"vtt"),s=xr(n.offset+n.duration,"vtt");t+=`${a+1}
`,t+=`${i} --> ${s}
`,t+=`${n.text}

`}),t}function aS(e){let t="";return e.forEach((n,a)=>{let i=xr(n.offset,"srt"),s=xr(n.offset+n.duration,"srt");t+=`${a+1}
`,t+=`${i} --> ${s}
`,t+=`${n.text}

`}),t}var $i=class extends Error{constructor(e,t){super(e),this.name="BrowserFetchError",this.response=t}};async function eh(){let e=`${$v}&Sec-MS-GEC=${await Or.generateSecMsGec()}&Sec-MS-GEC-Version=${lh}`;try{let t=await fetch(e,{headers:Iv});if(!t.ok){let a={};throw t.headers.forEach((i,s)=>{a[s]=i}),new $i(`HTTP ${t.status}`,{status:t.status,headers:a})}let n=await t.json();for(let a of n)a.VoiceTag.ContentCategories=a.VoiceTag.ContentCategories.map(i=>i.trim()),a.VoiceTag.VoicePersonalities=a.VoiceTag.VoicePersonalities.map(i=>i.trim());return n}catch(t){throw t instanceof $i?t:new $i(t instanceof Error?t.message:"Unknown fetch error")}}async function ch(){var e;try{return await eh()}catch(t){if(t instanceof $i&&((e=t.response)==null?void 0:e.status)===403)return Or.handleClientResponseError(t.response),await eh();throw t}}var iS=class fh{constructor(){this.voices=[],this.calledCreate=!1}static async create(t){let n=new fh,a=t!=null?t:await ch();return n.voices=a.map(i=>({...i,Language:i.Locale.split("-")[0]})),n.calledCreate=!0,n}find(t){if(!this.calledCreate)throw new Error("BrowserVoicesManager.find() called before BrowserVoicesManager.create()");return this.voices.filter(n=>Object.entries(t).every(([a,i])=>n[a]===i))}};function th(e){let t=Math.floor(e/3600),n=Math.floor(e%3600/60),a=Math.floor(e%60),i=Math.round((e-Math.floor(e))*1e3),s=(r,l=2)=>r.toString().padStart(l,"0");return`${s(t)}:${s(n)}:${s(a)},${s(i,3)}`}var sS=class{constructor(){this.cues=[]}feed(e){if(e.type!=="WordBoundary"||e.offset===void 0||e.duration===void 0||e.text===void 0)throw new Cr("Invalid message type, expected 'WordBoundary' with offset, duration and text");let t=e.offset/1e7,n=(e.offset+e.duration)/1e7;this.cues.push({index:this.cues.length+1,start:t,end:n,content:e.text})}mergeCues(e){if(e<=0)throw new Cr("Invalid number of words to merge, expected > 0");if(this.cues.length===0)return;let t=[],n=this.cues[0];for(let a of this.cues.slice(1))n.content.split(" ").length<e?n={...n,end:a.end,content:`${n.content} ${a.content}`}:(t.push(n),n=a);t.push(n),this.cues=t.map((a,i)=>({...a,index:i+1}))}getSrt(){return this.cues.map(e=>`${e.index}\r
${th(e.start)} --> ${th(e.end)}\r
${e.content}\r
`).join(`\r
`)}toString(){return this.getSrt()}};Ae.Communicate=uh;Ae.DRM=Or;Ae.EdgeTTS=tS;Ae.EdgeTTSBrowser=qv;Ae.EdgeTTSException=ia;Ae.FetchError=$i;Ae.NoAudioReceived=ih;Ae.SkewAdjustmentError=wr;Ae.SubMaker=sS;Ae.UnexpectedResponse=Bt;Ae.UnknownResponse=ou;Ae.ValueError=Cr;Ae.VoicesManager=iS;Ae.WebSocketError=Mr;Ae.createSRT=aS;Ae.createVTT=nS;Ae.listVoices=ch});var hh=D(cu=>{"use strict";Object.defineProperty(cu,"__esModule",{value:!0});cu.default="ffffffff-ffff-ffff-ffff-ffffffffffff"});var mh=D(fu=>{"use strict";Object.defineProperty(fu,"__esModule",{value:!0});fu.default="00000000-0000-0000-0000-000000000000"});var gh=D(du=>{"use strict";Object.defineProperty(du,"__esModule",{value:!0});du.default=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i});var Yi=D(hu=>{"use strict";Object.defineProperty(hu,"__esModule",{value:!0});var rS=gh();function lS(e){return typeof e=="string"&&rS.default.test(e)}hu.default=lS});var Xi=D(mu=>{"use strict";Object.defineProperty(mu,"__esModule",{value:!0});var oS=Yi();function uS(e){if(!(0,oS.default)(e))throw TypeError("Invalid UUID");let t;return Uint8Array.of((t=parseInt(e.slice(0,8),16))>>>24,t>>>16&255,t>>>8&255,t&255,(t=parseInt(e.slice(9,13),16))>>>8,t&255,(t=parseInt(e.slice(14,18),16))>>>8,t&255,(t=parseInt(e.slice(19,23),16))>>>8,t&255,(t=parseInt(e.slice(24,36),16))/1099511627776&255,t/4294967296&255,t>>>24&255,t>>>16&255,t>>>8&255,t&255)}mu.default=uS});var Kt=D(Ii=>{"use strict";Object.defineProperty(Ii,"__esModule",{value:!0});Ii.unsafeStringify=void 0;var cS=Yi(),ke=[];for(let e=0;e<256;++e)ke.push((e+256).toString(16).slice(1));function ph(e,t=0){return(ke[e[t+0]]+ke[e[t+1]]+ke[e[t+2]]+ke[e[t+3]]+"-"+ke[e[t+4]]+ke[e[t+5]]+"-"+ke[e[t+6]]+ke[e[t+7]]+"-"+ke[e[t+8]]+ke[e[t+9]]+"-"+ke[e[t+10]]+ke[e[t+11]]+ke[e[t+12]]+ke[e[t+13]]+ke[e[t+14]]+ke[e[t+15]]).toLowerCase()}Ii.unsafeStringify=ph;function fS(e,t=0){let n=ph(e,t);if(!(0,cS.default)(n))throw TypeError("Stringified UUID is invalid");return n}Ii.default=fS});var Rr=D(pu=>{"use strict";Object.defineProperty(pu,"__esModule",{value:!0});var gu,dS=new Uint8Array(16);function hS(){if(!gu){if(typeof crypto=="undefined"||!crypto.getRandomValues)throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");gu=crypto.getRandomValues.bind(crypto)}return gu(dS)}pu.default=hS});var yu=D(Ki=>{"use strict";Object.defineProperty(Ki,"__esModule",{value:!0});Ki.updateV1State=void 0;var yh=Rr(),mS=Kt(),Zi={};function gS(e,t,n){var s,r,l,o;let a,i=(s=e==null?void 0:e._v6)!=null?s:!1;if(e){let u=Object.keys(e);u.length===1&&u[0]==="_v6"&&(e=void 0)}if(e)a=bh((o=(l=e.random)!=null?l:(r=e.rng)==null?void 0:r.call(e))!=null?o:(0,yh.default)(),e.msecs,e.nsecs,e.clockseq,e.node,t,n);else{let u=Date.now(),c=(0,yh.default)();vh(Zi,u,c),a=bh(c,Zi.msecs,Zi.nsecs,i?void 0:Zi.clockseq,i?void 0:Zi.node,t,n)}return t!=null?t:(0,mS.unsafeStringify)(a)}function vh(e,t,n){var a,i;return(a=e.msecs)!=null||(e.msecs=-1/0),(i=e.nsecs)!=null||(e.nsecs=0),t===e.msecs?(e.nsecs++,e.nsecs>=1e4&&(e.node=void 0,e.nsecs=0)):t>e.msecs?e.nsecs=0:t<e.msecs&&(e.node=void 0),e.node||(e.node=n.slice(10,16),e.node[0]|=1,e.clockseq=(n[8]<<8|n[9])&16383),e.msecs=t,e}Ki.updateV1State=vh;function bh(e,t,n,a,i,s,r=0){if(e.length<16)throw new Error("Random bytes length must be >= 16");if(!s)s=new Uint8Array(16),r=0;else if(r<0||r+16>s.length)throw new RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);t!=null||(t=Date.now()),n!=null||(n=0),a!=null||(a=(e[8]<<8|e[9])&16383),i!=null||(i=e.slice(10,16)),t+=122192928e5;let l=((t&268435455)*1e4+n)%4294967296;s[r++]=l>>>24&255,s[r++]=l>>>16&255,s[r++]=l>>>8&255,s[r++]=l&255;let o=t/4294967296*1e4&268435455;s[r++]=o>>>8&255,s[r++]=o&255,s[r++]=o>>>24&15|16,s[r++]=o>>>16&255,s[r++]=a>>>8|128,s[r++]=a&255;for(let u=0;u<6;++u)s[r++]=i[u];return s}Ki.default=gS});var vu=D(bu=>{"use strict";Object.defineProperty(bu,"__esModule",{value:!0});var pS=Xi(),yS=Kt();function bS(e){let t=typeof e=="string"?(0,pS.default)(e):e,n=vS(t);return typeof e=="string"?(0,yS.unsafeStringify)(n):n}bu.default=bS;function vS(e){return Uint8Array.of((e[6]&15)<<4|e[7]>>4&15,(e[7]&15)<<4|(e[4]&240)>>4,(e[4]&15)<<4|(e[5]&240)>>4,(e[5]&15)<<4|(e[0]&240)>>4,(e[0]&15)<<4|(e[1]&240)>>4,(e[1]&15)<<4|(e[2]&240)>>4,96|e[2]&15,e[3],e[8],e[9],e[10],e[11],e[12],e[13],e[14],e[15])}});var Th=D(Su=>{"use strict";Object.defineProperty(Su,"__esModule",{value:!0});function SS(e){let t=wS(e),n=ES(t,e.length*8);return TS(n)}function TS(e){let t=new Uint8Array(e.length*4);for(let n=0;n<e.length*4;n++)t[n]=e[n>>2]>>>n%4*8&255;return t}function Sh(e){return(e+64>>>9<<4)+14+1}function ES(e,t){let n=new Uint32Array(Sh(t)).fill(0);n.set(e),n[t>>5]|=128<<t%32,n[n.length-1]=t,e=n;let a=1732584193,i=-271733879,s=-1732584194,r=271733878;for(let l=0;l<e.length;l+=16){let o=a,u=i,c=s,f=r;a=Pe(a,i,s,r,e[l],7,-680876936),r=Pe(r,a,i,s,e[l+1],12,-389564586),s=Pe(s,r,a,i,e[l+2],17,606105819),i=Pe(i,s,r,a,e[l+3],22,-1044525330),a=Pe(a,i,s,r,e[l+4],7,-176418897),r=Pe(r,a,i,s,e[l+5],12,1200080426),s=Pe(s,r,a,i,e[l+6],17,-1473231341),i=Pe(i,s,r,a,e[l+7],22,-45705983),a=Pe(a,i,s,r,e[l+8],7,1770035416),r=Pe(r,a,i,s,e[l+9],12,-1958414417),s=Pe(s,r,a,i,e[l+10],17,-42063),i=Pe(i,s,r,a,e[l+11],22,-1990404162),a=Pe(a,i,s,r,e[l+12],7,1804603682),r=Pe(r,a,i,s,e[l+13],12,-40341101),s=Pe(s,r,a,i,e[l+14],17,-1502002290),i=Pe(i,s,r,a,e[l+15],22,1236535329),a=Le(a,i,s,r,e[l+1],5,-165796510),r=Le(r,a,i,s,e[l+6],9,-1069501632),s=Le(s,r,a,i,e[l+11],14,643717713),i=Le(i,s,r,a,e[l],20,-373897302),a=Le(a,i,s,r,e[l+5],5,-701558691),r=Le(r,a,i,s,e[l+10],9,38016083),s=Le(s,r,a,i,e[l+15],14,-660478335),i=Le(i,s,r,a,e[l+4],20,-405537848),a=Le(a,i,s,r,e[l+9],5,568446438),r=Le(r,a,i,s,e[l+14],9,-1019803690),s=Le(s,r,a,i,e[l+3],14,-187363961),i=Le(i,s,r,a,e[l+8],20,1163531501),a=Le(a,i,s,r,e[l+13],5,-1444681467),r=Le(r,a,i,s,e[l+2],9,-51403784),s=Le(s,r,a,i,e[l+7],14,1735328473),i=Le(i,s,r,a,e[l+12],20,-1926607734),a=ze(a,i,s,r,e[l+5],4,-378558),r=ze(r,a,i,s,e[l+8],11,-2022574463),s=ze(s,r,a,i,e[l+11],16,1839030562),i=ze(i,s,r,a,e[l+14],23,-35309556),a=ze(a,i,s,r,e[l+1],4,-1530992060),r=ze(r,a,i,s,e[l+4],11,1272893353),s=ze(s,r,a,i,e[l+7],16,-155497632),i=ze(i,s,r,a,e[l+10],23,-1094730640),a=ze(a,i,s,r,e[l+13],4,681279174),r=ze(r,a,i,s,e[l],11,-358537222),s=ze(s,r,a,i,e[l+3],16,-722521979),i=ze(i,s,r,a,e[l+6],23,76029189),a=ze(a,i,s,r,e[l+9],4,-640364487),r=ze(r,a,i,s,e[l+12],11,-421815835),s=ze(s,r,a,i,e[l+15],16,530742520),i=ze(i,s,r,a,e[l+2],23,-995338651),a=qe(a,i,s,r,e[l],6,-198630844),r=qe(r,a,i,s,e[l+7],10,1126891415),s=qe(s,r,a,i,e[l+14],15,-1416354905),i=qe(i,s,r,a,e[l+5],21,-57434055),a=qe(a,i,s,r,e[l+12],6,1700485571),r=qe(r,a,i,s,e[l+3],10,-1894986606),s=qe(s,r,a,i,e[l+10],15,-1051523),i=qe(i,s,r,a,e[l+1],21,-2054922799),a=qe(a,i,s,r,e[l+8],6,1873313359),r=qe(r,a,i,s,e[l+15],10,-30611744),s=qe(s,r,a,i,e[l+6],15,-1560198380),i=qe(i,s,r,a,e[l+13],21,1309151649),a=qe(a,i,s,r,e[l+4],6,-145523070),r=qe(r,a,i,s,e[l+11],10,-1120210379),s=qe(s,r,a,i,e[l+2],15,718787259),i=qe(i,s,r,a,e[l+9],21,-343485551),a=wn(a,o),i=wn(i,u),s=wn(s,c),r=wn(r,f)}return Uint32Array.of(a,i,s,r)}function wS(e){if(e.length===0)return new Uint32Array;let t=new Uint32Array(Sh(e.length*8)).fill(0);for(let n=0;n<e.length;n++)t[n>>2]|=(e[n]&255)<<n%4*8;return t}function wn(e,t){let n=(e&65535)+(t&65535);return(e>>16)+(t>>16)+(n>>16)<<16|n&65535}function MS(e,t){return e<<t|e>>>32-t}function _r(e,t,n,a,i,s){return wn(MS(wn(wn(t,e),wn(a,s)),i),n)}function Pe(e,t,n,a,i,s,r){return _r(t&n|~t&a,e,t,i,s,r)}function Le(e,t,n,a,i,s,r){return _r(t&a|n&~a,e,t,i,s,r)}function ze(e,t,n,a,i,s,r){return _r(t^n^a,e,t,i,s,r)}function qe(e,t,n,a,i,s,r){return _r(n^(t|~a),e,t,i,s,r)}Su.default=SS});var Wi=D(Wt=>{"use strict";Object.defineProperty(Wt,"__esModule",{value:!0});Wt.URL=Wt.DNS=Wt.stringToBytes=void 0;var Eh=Xi(),CS=Kt();function wh(e){e=unescape(encodeURIComponent(e));let t=new Uint8Array(e.length);for(let n=0;n<e.length;++n)t[n]=e.charCodeAt(n);return t}Wt.stringToBytes=wh;Wt.DNS="6ba7b810-9dad-11d1-80b4-00c04fd430c8";Wt.URL="6ba7b811-9dad-11d1-80b4-00c04fd430c8";function AS(e,t,n,a,i,s){let r=typeof n=="string"?wh(n):n,l=typeof a=="string"?(0,Eh.default)(a):a;if(typeof a=="string"&&(a=(0,Eh.default)(a)),(a==null?void 0:a.length)!==16)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");let o=new Uint8Array(16+r.length);if(o.set(l),o.set(r,l.length),o=t(o),o[6]=o[6]&15|e,o[8]=o[8]&63|128,i){s=s||0;for(let u=0;u<16;++u)i[s+u]=o[u];return i}return(0,CS.unsafeStringify)(o)}Wt.default=AS});var Ch=D(sa=>{"use strict";Object.defineProperty(sa,"__esModule",{value:!0});sa.URL=sa.DNS=void 0;var xS=Th(),Tu=Wi(),Mh=Wi();Object.defineProperty(sa,"DNS",{enumerable:!0,get:function(){return Mh.DNS}});Object.defineProperty(sa,"URL",{enumerable:!0,get:function(){return Mh.URL}});function Eu(e,t,n,a){return(0,Tu.default)(48,xS.default,e,t,n,a)}Eu.DNS=Tu.DNS;Eu.URL=Tu.URL;sa.default=Eu});var Ah=D(wu=>{"use strict";Object.defineProperty(wu,"__esModule",{value:!0});var OS=typeof crypto!="undefined"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto);wu.default={randomUUID:OS}});var Oh=D(Mu=>{"use strict";Object.defineProperty(Mu,"__esModule",{value:!0});var xh=Ah(),RS=Rr(),_S=Kt();function kS(e,t,n){var i,s,r;if(xh.default.randomUUID&&!t&&!e)return xh.default.randomUUID();e=e||{};let a=(r=(s=e.random)!=null?s:(i=e.rng)==null?void 0:i.call(e))!=null?r:(0,RS.default)();if(a.length<16)throw new Error("Random bytes length must be >= 16");if(a[6]=a[6]&15|64,a[8]=a[8]&63|128,t){if(n=n||0,n<0||n+16>t.length)throw new RangeError(`UUID byte range ${n}:${n+15} is out of buffer bounds`);for(let l=0;l<16;++l)t[n+l]=a[l];return t}return(0,_S.unsafeStringify)(a)}Mu.default=kS});var Rh=D(Au=>{"use strict";Object.defineProperty(Au,"__esModule",{value:!0});function US(e,t,n,a){switch(e){case 0:return t&n^~t&a;case 1:return t^n^a;case 2:return t&n^t&a^n&a;case 3:return t^n^a}}function Cu(e,t){return e<<t|e>>>32-t}function NS(e){let t=[1518500249,1859775393,2400959708,3395469782],n=[1732584193,4023233417,2562383102,271733878,3285377520],a=new Uint8Array(e.length+1);a.set(e),a[e.length]=128,e=a;let i=e.length/4+2,s=Math.ceil(i/16),r=new Array(s);for(let l=0;l<s;++l){let o=new Uint32Array(16);for(let u=0;u<16;++u)o[u]=e[l*64+u*4]<<24|e[l*64+u*4+1]<<16|e[l*64+u*4+2]<<8|e[l*64+u*4+3];r[l]=o}r[s-1][14]=(e.length-1)*8/Math.pow(2,32),r[s-1][14]=Math.floor(r[s-1][14]),r[s-1][15]=(e.length-1)*8&4294967295;for(let l=0;l<s;++l){let o=new Uint32Array(80);for(let y=0;y<16;++y)o[y]=r[l][y];for(let y=16;y<80;++y)o[y]=Cu(o[y-3]^o[y-8]^o[y-14]^o[y-16],1);let u=n[0],c=n[1],f=n[2],d=n[3],m=n[4];for(let y=0;y<80;++y){let E=Math.floor(y/20),M=Cu(u,5)+US(E,c,f,d)+m+t[E]+o[y]>>>0;m=d,d=f,f=Cu(c,30)>>>0,c=u,u=M}n[0]=n[0]+u>>>0,n[1]=n[1]+c>>>0,n[2]=n[2]+f>>>0,n[3]=n[3]+d>>>0,n[4]=n[4]+m>>>0}return Uint8Array.of(n[0]>>24,n[0]>>16,n[0]>>8,n[0],n[1]>>24,n[1]>>16,n[1]>>8,n[1],n[2]>>24,n[2]>>16,n[2]>>8,n[2],n[3]>>24,n[3]>>16,n[3]>>8,n[3],n[4]>>24,n[4]>>16,n[4]>>8,n[4])}Au.default=NS});var kh=D(ra=>{"use strict";Object.defineProperty(ra,"__esModule",{value:!0});ra.URL=ra.DNS=void 0;var DS=Rh(),xu=Wi(),_h=Wi();Object.defineProperty(ra,"DNS",{enumerable:!0,get:function(){return _h.DNS}});Object.defineProperty(ra,"URL",{enumerable:!0,get:function(){return _h.URL}});function Ou(e,t,n,a){return(0,xu.default)(80,DS.default,e,t,n,a)}Ou.DNS=xu.DNS;Ou.URL=xu.URL;ra.default=Ou});var Uh=D(Ru=>{"use strict";Object.defineProperty(Ru,"__esModule",{value:!0});var BS=Kt(),PS=yu(),LS=vu();function zS(e,t,n){e!=null||(e={}),n!=null||(n=0);let a=(0,PS.default)({...e,_v6:!0},new Uint8Array(16));if(a=(0,LS.default)(a),t){for(let i=0;i<16;i++)t[n+i]=a[i];return t}return(0,BS.unsafeStringify)(a)}Ru.default=zS});var Nh=D(_u=>{"use strict";Object.defineProperty(_u,"__esModule",{value:!0});var qS=Xi(),HS=Kt();function QS(e){let t=typeof e=="string"?(0,qS.default)(e):e,n=jS(t);return typeof e=="string"?(0,HS.unsafeStringify)(n):n}_u.default=QS;function jS(e){return Uint8Array.of((e[3]&15)<<4|e[4]>>4&15,(e[4]&15)<<4|(e[5]&240)>>4,(e[5]&15)<<4|e[6]&15,e[7],(e[1]&15)<<4|(e[2]&240)>>4,(e[2]&15)<<4|(e[3]&240)>>4,16|(e[0]&240)>>4,(e[0]&15)<<4|(e[1]&240)>>4,e[8],e[9],e[10],e[11],e[12],e[13],e[14],e[15])}});var Lh=D(Ji=>{"use strict";Object.defineProperty(Ji,"__esModule",{value:!0});Ji.updateV7State=void 0;var Dh=Rr(),VS=Kt(),ku={};function FS(e,t,n){var i,s,r;let a;if(e)a=Bh((r=(s=e.random)!=null?s:(i=e.rng)==null?void 0:i.call(e))!=null?r:(0,Dh.default)(),e.msecs,e.seq,t,n);else{let l=Date.now(),o=(0,Dh.default)();Ph(ku,l,o),a=Bh(o,ku.msecs,ku.seq,t,n)}return t!=null?t:(0,VS.unsafeStringify)(a)}function Ph(e,t,n){var a,i;return(a=e.msecs)!=null||(e.msecs=-1/0),(i=e.seq)!=null||(e.seq=0),t>e.msecs?(e.seq=n[6]<<23|n[7]<<16|n[8]<<8|n[9],e.msecs=t):(e.seq=e.seq+1|0,e.seq===0&&e.msecs++),e}Ji.updateV7State=Ph;function Bh(e,t,n,a,i=0){if(e.length<16)throw new Error("Random bytes length must be >= 16");if(!a)a=new Uint8Array(16),i=0;else if(i<0||i+16>a.length)throw new RangeError(`UUID byte range ${i}:${i+15} is out of buffer bounds`);return t!=null||(t=Date.now()),n!=null||(n=e[6]*127<<24|e[7]<<16|e[8]<<8|e[9]),a[i++]=t/1099511627776&255,a[i++]=t/4294967296&255,a[i++]=t/16777216&255,a[i++]=t/65536&255,a[i++]=t/256&255,a[i++]=t&255,a[i++]=112|n>>>28&15,a[i++]=n>>>20&255,a[i++]=128|n>>>14&63,a[i++]=n>>>6&255,a[i++]=n<<2&255|e[10]&3,a[i++]=e[11],a[i++]=e[12],a[i++]=e[13],a[i++]=e[14],a[i++]=e[15],a}Ji.default=FS});var zh=D(Uu=>{"use strict";Object.defineProperty(Uu,"__esModule",{value:!0});var GS=Yi();function $S(e){if(!(0,GS.default)(e))throw TypeError("Invalid UUID");return parseInt(e.slice(14,15),16)}Uu.default=$S});var Nu=D(J=>{"use strict";Object.defineProperty(J,"__esModule",{value:!0});J.version=J.validate=J.v7=J.v6ToV1=J.v6=J.v5=J.v4=J.v3=J.v1ToV6=J.v1=J.stringify=J.parse=J.NIL=J.MAX=void 0;var YS=hh();Object.defineProperty(J,"MAX",{enumerable:!0,get:function(){return YS.default}});var XS=mh();Object.defineProperty(J,"NIL",{enumerable:!0,get:function(){return XS.default}});var IS=Xi();Object.defineProperty(J,"parse",{enumerable:!0,get:function(){return IS.default}});var ZS=Kt();Object.defineProperty(J,"stringify",{enumerable:!0,get:function(){return ZS.default}});var KS=yu();Object.defineProperty(J,"v1",{enumerable:!0,get:function(){return KS.default}});var WS=vu();Object.defineProperty(J,"v1ToV6",{enumerable:!0,get:function(){return WS.default}});var JS=Ch();Object.defineProperty(J,"v3",{enumerable:!0,get:function(){return JS.default}});var e1=Oh();Object.defineProperty(J,"v4",{enumerable:!0,get:function(){return e1.default}});var t1=kh();Object.defineProperty(J,"v5",{enumerable:!0,get:function(){return t1.default}});var n1=Uh();Object.defineProperty(J,"v6",{enumerable:!0,get:function(){return n1.default}});var a1=Nh();Object.defineProperty(J,"v6ToV1",{enumerable:!0,get:function(){return a1.default}});var i1=Lh();Object.defineProperty(J,"v7",{enumerable:!0,get:function(){return i1.default}});var s1=Yi();Object.defineProperty(J,"validate",{enumerable:!0,get:function(){return s1.default}});var r1=zh();Object.defineProperty(J,"version",{enumerable:!0,get:function(){return r1.default}})});var Du=D((xA,qh)=>{var l1=qh.exports=function e(t,n){var a;if(t!=null)return n=(n||"").replace(/[^&"<>\']/g,""),a=`([&"<>'])`.replace(new RegExp("["+n+"]","g"),""),t.replace(new RegExp(a,"g"),function(i,s){return e.map[s]})},AA=l1.map={">":"&gt;","<":"&lt;","'":"&apos;",'"':"&quot;","&":"&amp;"}});var Bu={};Id(Bu,{default:()=>o1});var qa,o1,Pu=_v(()=>{qa=null;typeof WebSocket!="undefined"?qa=WebSocket:typeof MozWebSocket!="undefined"?qa=MozWebSocket:typeof global!="undefined"?qa=global.WebSocket||global.MozWebSocket:typeof window!="undefined"?qa=window.WebSocket||window.MozWebSocket:typeof self!="undefined"&&(qa=self.WebSocket||self.MozWebSocket);o1=qa});var Lu=D((Mn,Hh)=>{var kr=typeof globalThis!="undefined"&&globalThis||typeof self!="undefined"&&self||typeof global!="undefined"&&global,Ur=function(){function e(){this.fetch=!1,this.DOMException=kr.DOMException}return e.prototype=kr,new e}();(function(e){var t=function(n){var a=typeof e!="undefined"&&e||typeof self!="undefined"&&self||typeof global!="undefined"&&global||{},i={searchParams:"URLSearchParams"in a,iterable:"Symbol"in a&&"iterator"in Symbol,blob:"FileReader"in a&&"Blob"in a&&function(){try{return new Blob,!0}catch(b){return!1}}(),formData:"FormData"in a,arrayBuffer:"ArrayBuffer"in a};function s(b){return b&&DataView.prototype.isPrototypeOf(b)}if(i.arrayBuffer)var r=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]"],l=ArrayBuffer.isView||function(b){return b&&r.indexOf(Object.prototype.toString.call(b))>-1};function o(b){if(typeof b!="string"&&(b=String(b)),/[^a-z0-9\-#$%&'*+.^_`|~!]/i.test(b)||b==="")throw new TypeError('Invalid character in header field name: "'+b+'"');return b.toLowerCase()}function u(b){return typeof b!="string"&&(b=String(b)),b}function c(b){var x={next:function(){var B=b.shift();return{done:B===void 0,value:B}}};return i.iterable&&(x[Symbol.iterator]=function(){return x}),x}function f(b){this.map={},b instanceof f?b.forEach(function(x,B){this.append(B,x)},this):Array.isArray(b)?b.forEach(function(x){if(x.length!=2)throw new TypeError("Headers constructor: expected name/value pair to be length 2, found"+x.length);this.append(x[0],x[1])},this):b&&Object.getOwnPropertyNames(b).forEach(function(x){this.append(x,b[x])},this)}f.prototype.append=function(b,x){b=o(b),x=u(x);var B=this.map[b];this.map[b]=B?B+", "+x:x},f.prototype.delete=function(b){delete this.map[o(b)]},f.prototype.get=function(b){return b=o(b),this.has(b)?this.map[b]:null},f.prototype.has=function(b){return this.map.hasOwnProperty(o(b))},f.prototype.set=function(b,x){this.map[o(b)]=u(x)},f.prototype.forEach=function(b,x){for(var B in this.map)this.map.hasOwnProperty(B)&&b.call(x,this.map[B],B,this)},f.prototype.keys=function(){var b=[];return this.forEach(function(x,B){b.push(B)}),c(b)},f.prototype.values=function(){var b=[];return this.forEach(function(x){b.push(x)}),c(b)},f.prototype.entries=function(){var b=[];return this.forEach(function(x,B){b.push([B,x])}),c(b)},i.iterable&&(f.prototype[Symbol.iterator]=f.prototype.entries);function d(b){if(!b._noBody){if(b.bodyUsed)return Promise.reject(new TypeError("Already read"));b.bodyUsed=!0}}function m(b){return new Promise(function(x,B){b.onload=function(){x(b.result)},b.onerror=function(){B(b.error)}})}function y(b){var x=new FileReader,B=m(x);return x.readAsArrayBuffer(b),B}function E(b){var x=new FileReader,B=m(x),$=/charset=([A-Za-z0-9_-]+)/.exec(b.type),v=$?$[1]:"utf-8";return x.readAsText(b,v),B}function M(b){for(var x=new Uint8Array(b),B=new Array(x.length),$=0;$<x.length;$++)B[$]=String.fromCharCode(x[$]);return B.join("")}function g(b){if(b.slice)return b.slice(0);var x=new Uint8Array(b.byteLength);return x.set(new Uint8Array(b)),x.buffer}function h(){return this.bodyUsed=!1,this._initBody=function(b){this.bodyUsed=this.bodyUsed,this._bodyInit=b,b?typeof b=="string"?this._bodyText=b:i.blob&&Blob.prototype.isPrototypeOf(b)?this._bodyBlob=b:i.formData&&FormData.prototype.isPrototypeOf(b)?this._bodyFormData=b:i.searchParams&&URLSearchParams.prototype.isPrototypeOf(b)?this._bodyText=b.toString():i.arrayBuffer&&i.blob&&s(b)?(this._bodyArrayBuffer=g(b.buffer),this._bodyInit=new Blob([this._bodyArrayBuffer])):i.arrayBuffer&&(ArrayBuffer.prototype.isPrototypeOf(b)||l(b))?this._bodyArrayBuffer=g(b):this._bodyText=b=Object.prototype.toString.call(b):(this._noBody=!0,this._bodyText=""),this.headers.get("content-type")||(typeof b=="string"?this.headers.set("content-type","text/plain;charset=UTF-8"):this._bodyBlob&&this._bodyBlob.type?this.headers.set("content-type",this._bodyBlob.type):i.searchParams&&URLSearchParams.prototype.isPrototypeOf(b)&&this.headers.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"))},i.blob&&(this.blob=function(){var b=d(this);if(b)return b;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(new Blob([this._bodyArrayBuffer]));if(this._bodyFormData)throw new Error("could not read FormData body as blob");return Promise.resolve(new Blob([this._bodyText]))}),this.arrayBuffer=function(){if(this._bodyArrayBuffer){var b=d(this);return b||(ArrayBuffer.isView(this._bodyArrayBuffer)?Promise.resolve(this._bodyArrayBuffer.buffer.slice(this._bodyArrayBuffer.byteOffset,this._bodyArrayBuffer.byteOffset+this._bodyArrayBuffer.byteLength)):Promise.resolve(this._bodyArrayBuffer))}else{if(i.blob)return this.blob().then(y);throw new Error("could not read as ArrayBuffer")}},this.text=function(){var b=d(this);if(b)return b;if(this._bodyBlob)return E(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(M(this._bodyArrayBuffer));if(this._bodyFormData)throw new Error("could not read FormData body as text");return Promise.resolve(this._bodyText)},i.formData&&(this.formData=function(){return this.text().then(_)}),this.json=function(){return this.text().then(JSON.parse)},this}var p=["CONNECT","DELETE","GET","HEAD","OPTIONS","PATCH","POST","PUT","TRACE"];function S(b){var x=b.toUpperCase();return p.indexOf(x)>-1?x:b}function A(b,x){if(!(this instanceof A))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');x=x||{};var B=x.body;if(b instanceof A){if(b.bodyUsed)throw new TypeError("Already read");this.url=b.url,this.credentials=b.credentials,x.headers||(this.headers=new f(b.headers)),this.method=b.method,this.mode=b.mode,this.signal=b.signal,!B&&b._bodyInit!=null&&(B=b._bodyInit,b.bodyUsed=!0)}else this.url=String(b);if(this.credentials=x.credentials||this.credentials||"same-origin",(x.headers||!this.headers)&&(this.headers=new f(x.headers)),this.method=S(x.method||this.method||"GET"),this.mode=x.mode||this.mode||null,this.signal=x.signal||this.signal||function(){if("AbortController"in a){var T=new AbortController;return T.signal}}(),this.referrer=null,(this.method==="GET"||this.method==="HEAD")&&B)throw new TypeError("Body not allowed for GET or HEAD requests");if(this._initBody(B),(this.method==="GET"||this.method==="HEAD")&&(x.cache==="no-store"||x.cache==="no-cache")){var $=/([?&])_=[^&]*/;if($.test(this.url))this.url=this.url.replace($,"$1_="+new Date().getTime());else{var v=/\?/;this.url+=(v.test(this.url)?"&":"?")+"_="+new Date().getTime()}}}A.prototype.clone=function(){return new A(this,{body:this._bodyInit})};function _(b){var x=new FormData;return b.trim().split("&").forEach(function(B){if(B){var $=B.split("="),v=$.shift().replace(/\+/g," "),T=$.join("=").replace(/\+/g," ");x.append(decodeURIComponent(v),decodeURIComponent(T))}}),x}function O(b){var x=new f,B=b.replace(/\r?\n[\t ]+/g," ");return B.split("\r").map(function($){return $.indexOf(`
`)===0?$.substr(1,$.length):$}).forEach(function($){var v=$.split(":"),T=v.shift().trim();if(T){var P=v.join(":").trim();try{x.append(T,P)}catch(ee){console.warn("Response "+ee.message)}}}),x}h.call(A.prototype);function R(b,x){if(!(this instanceof R))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');if(x||(x={}),this.type="default",this.status=x.status===void 0?200:x.status,this.status<200||this.status>599)throw new RangeError("Failed to construct 'Response': The status provided (0) is outside the range [200, 599].");this.ok=this.status>=200&&this.status<300,this.statusText=x.statusText===void 0?"":""+x.statusText,this.headers=new f(x.headers),this.url=x.url||"",this._initBody(b)}h.call(R.prototype),R.prototype.clone=function(){return new R(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new f(this.headers),url:this.url})},R.error=function(){var b=new R(null,{status:200,statusText:""});return b.ok=!1,b.status=0,b.type="error",b};var q=[301,302,303,307,308];R.redirect=function(b,x){if(q.indexOf(x)===-1)throw new RangeError("Invalid status code");return new R(null,{status:x,headers:{location:b}})},n.DOMException=a.DOMException;try{new n.DOMException}catch(b){n.DOMException=function(x,B){this.message=x,this.name=B;var $=Error(x);this.stack=$.stack},n.DOMException.prototype=Object.create(Error.prototype),n.DOMException.prototype.constructor=n.DOMException}function k(b,x){return new Promise(function(B,$){var v=new A(b,x);if(v.signal&&v.signal.aborted)return $(new n.DOMException("Aborted","AbortError"));var T=new XMLHttpRequest;function P(){T.abort()}T.onload=function(){var be={statusText:T.statusText,headers:O(T.getAllResponseHeaders()||"")};v.url.indexOf("file://")===0&&(T.status<200||T.status>599)?be.status=200:be.status=T.status,be.url="responseURL"in T?T.responseURL:be.headers.get("X-Request-URL");var Jn="response"in T?T.response:T.responseText;setTimeout(function(){B(new R(Jn,be))},0)},T.onerror=function(){setTimeout(function(){$(new TypeError("Network request failed"))},0)},T.ontimeout=function(){setTimeout(function(){$(new TypeError("Network request timed out"))},0)},T.onabort=function(){setTimeout(function(){$(new n.DOMException("Aborted","AbortError"))},0)};function ee(be){try{return be===""&&a.location.href?a.location.href:be}catch(Jn){return be}}if(T.open(v.method,ee(v.url),!0),v.credentials==="include"?T.withCredentials=!0:v.credentials==="omit"&&(T.withCredentials=!1),"responseType"in T&&(i.blob?T.responseType="blob":i.arrayBuffer&&(T.responseType="arraybuffer")),x&&typeof x.headers=="object"&&!(x.headers instanceof f||a.Headers&&x.headers instanceof a.Headers)){var Wn=[];Object.getOwnPropertyNames(x.headers).forEach(function(be){Wn.push(o(be)),T.setRequestHeader(be,u(x.headers[be]))}),v.headers.forEach(function(be,Jn){Wn.indexOf(Jn)===-1&&T.setRequestHeader(Jn,be)})}else v.headers.forEach(function(be,Jn){T.setRequestHeader(Jn,be)});v.signal&&(v.signal.addEventListener("abort",P),T.onreadystatechange=function(){T.readyState===4&&v.signal.removeEventListener("abort",P)}),T.send(typeof v._bodyInit=="undefined"?null:v._bodyInit)})}return k.polyfill=!0,a.fetch||(a.fetch=k,a.Headers=f,a.Request=A,a.Response=R),n.Headers=f,n.Request=A,n.Response=R,n.fetch=k,n}({})})(Ur);Ur.fetch.ponyfill=!0;delete Ur.fetch.polyfill;var Ha=kr.fetch?kr:Ur;Mn=Ha.fetch;Mn.default=Ha.fetch;Mn.fetch=Ha.fetch;Mn.Headers=Ha.Headers;Mn.Request=Ha.Request;Mn.Response=Ha.Response;Hh.exports=Mn});var jh=D((OA,Qh)=>{var Qa=1e3,ja=Qa*60,Va=ja*60,la=Va*24,u1=la*7,c1=la*365.25;Qh.exports=function(e,t){t=t||{};var n=typeof e;if(n==="string"&&e.length>0)return f1(e);if(n==="number"&&isFinite(e))return t.long?h1(e):d1(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))};function f1(e){if(e=String(e),!(e.length>100)){var t=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(t){var n=parseFloat(t[1]),a=(t[2]||"ms").toLowerCase();switch(a){case"years":case"year":case"yrs":case"yr":case"y":return n*c1;case"weeks":case"week":case"w":return n*u1;case"days":case"day":case"d":return n*la;case"hours":case"hour":case"hrs":case"hr":case"h":return n*Va;case"minutes":case"minute":case"mins":case"min":case"m":return n*ja;case"seconds":case"second":case"secs":case"sec":case"s":return n*Qa;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return n;default:return}}}}function d1(e){var t=Math.abs(e);return t>=la?Math.round(e/la)+"d":t>=Va?Math.round(e/Va)+"h":t>=ja?Math.round(e/ja)+"m":t>=Qa?Math.round(e/Qa)+"s":e+"ms"}function h1(e){var t=Math.abs(e);return t>=la?Nr(e,t,la,"day"):t>=Va?Nr(e,t,Va,"hour"):t>=ja?Nr(e,t,ja,"minute"):t>=Qa?Nr(e,t,Qa,"second"):e+" ms"}function Nr(e,t,n,a){var i=t>=n*1.5;return Math.round(e/n)+" "+a+(i?"s":"")}});var Fh=D((RA,Vh)=>{function m1(e){n.debug=n,n.default=n,n.coerce=o,n.disable=r,n.enable=i,n.enabled=l,n.humanize=jh(),n.destroy=u,Object.keys(e).forEach(c=>{n[c]=e[c]}),n.names=[],n.skips=[],n.formatters={};function t(c){let f=0;for(let d=0;d<c.length;d++)f=(f<<5)-f+c.charCodeAt(d),f|=0;return n.colors[Math.abs(f)%n.colors.length]}n.selectColor=t;function n(c){let f,d=null,m,y;function E(...M){if(!E.enabled)return;let g=E,h=Number(new Date),p=h-(f||h);g.diff=p,g.prev=f,g.curr=h,f=h,M[0]=n.coerce(M[0]),typeof M[0]!="string"&&M.unshift("%O");let S=0;M[0]=M[0].replace(/%([a-zA-Z%])/g,(_,O)=>{if(_==="%%")return"%";S++;let R=n.formatters[O];if(typeof R=="function"){let q=M[S];_=R.call(g,q),M.splice(S,1),S--}return _}),n.formatArgs.call(g,M),(g.log||n.log).apply(g,M)}return E.namespace=c,E.useColors=n.useColors(),E.color=n.selectColor(c),E.extend=a,E.destroy=n.destroy,Object.defineProperty(E,"enabled",{enumerable:!0,configurable:!1,get:()=>d!==null?d:(m!==n.namespaces&&(m=n.namespaces,y=n.enabled(c)),y),set:M=>{d=M}}),typeof n.init=="function"&&n.init(E),E}function a(c,f){let d=n(this.namespace+(typeof f=="undefined"?":":f)+c);return d.log=this.log,d}function i(c){n.save(c),n.namespaces=c,n.names=[],n.skips=[];let f=(typeof c=="string"?c:"").trim().replace(" ",",").split(",").filter(Boolean);for(let d of f)d[0]==="-"?n.skips.push(d.slice(1)):n.names.push(d)}function s(c,f){let d=0,m=0,y=-1,E=0;for(;d<c.length;)if(m<f.length&&(f[m]===c[d]||f[m]==="*"))f[m]==="*"?(y=m,E=d,m++):(d++,m++);else if(y!==-1)m=y+1,E++,d=E;else return!1;for(;m<f.length&&f[m]==="*";)m++;return m===f.length}function r(){let c=[...n.names,...n.skips.map(f=>"-"+f)].join(",");return n.enable(""),c}function l(c){for(let f of n.skips)if(s(c,f))return!1;for(let f of n.names)if(s(c,f))return!0;return!1}function o(c){return c instanceof Error?c.stack||c.message:c}function u(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return n.enable(n.load()),n}Vh.exports=m1});var zu=D((rt,Dr)=>{rt.formatArgs=p1;rt.save=y1;rt.load=b1;rt.useColors=g1;rt.storage=v1();rt.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})();rt.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function g1(){if(typeof window!="undefined"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs))return!0;if(typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let e;return typeof document!="undefined"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window!="undefined"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator!="undefined"&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}function p1(e){if(e[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+e[0]+(this.useColors?"%c ":" ")+"+"+Dr.exports.humanize(this.diff),!this.useColors)return;let t="color: "+this.color;e.splice(1,0,t,"color: inherit");let n=0,a=0;e[0].replace(/%[a-zA-Z%]/g,i=>{i!=="%%"&&(n++,i==="%c"&&(a=n))}),e.splice(a,0,t)}rt.log=console.debug||console.log||(()=>{});function y1(e){try{e?rt.storage.setItem("debug",e):rt.storage.removeItem("debug")}catch(t){}}function b1(){let e;try{e=rt.storage.getItem("debug")}catch(t){}return!e&&typeof process!="undefined"&&"env"in process&&(e=process.env.DEBUG),e}function v1(){try{return localStorage}catch(e){}}Dr.exports=Fh()(rt);var{formatters:S1}=Dr.exports;S1.j=function(e){try{return JSON.stringify(e)}catch(t){return"[UnexpectedJSONParseError]: "+t.message}}});var Yh=D(Ke=>{"use strict";var T1=Ke&&Ke.__createBinding||(Object.create?function(e,t,n,a){a===void 0&&(a=n);var i=Object.getOwnPropertyDescriptor(t,n);(!i||("get"in i?!t.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,a,i)}:function(e,t,n,a){a===void 0&&(a=n),e[a]=t[n]}),E1=Ke&&Ke.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),Gh=Ke&&Ke.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var n in e)n!=="default"&&Object.prototype.hasOwnProperty.call(e,n)&&T1(t,e,n);return E1(t,e),t};Object.defineProperty(Ke,"__esModule",{value:!0});Ke.req=Ke.json=Ke.toBuffer=void 0;var w1=Gh(require("http")),M1=Gh(require("https"));async function $h(e){let t=0,n=[];for await(let a of e)t+=a.length,n.push(a);return Buffer.concat(n,t)}Ke.toBuffer=$h;async function C1(e){let n=(await $h(e)).toString("utf8");try{return JSON.parse(n)}catch(a){let i=a;throw i.message+=` (input: ${n})`,i}}Ke.json=C1;function A1(e,t={}){let a=((typeof e=="string"?e:e.href).startsWith("https:")?M1:w1).request(e,t),i=new Promise((s,r)=>{a.once("response",s).once("error",r).end()});return a.then=i.then.bind(i),a}Ke.req=A1});var Kh=D(lt=>{"use strict";var Ih=lt&&lt.__createBinding||(Object.create?function(e,t,n,a){a===void 0&&(a=n);var i=Object.getOwnPropertyDescriptor(t,n);(!i||("get"in i?!t.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,a,i)}:function(e,t,n,a){a===void 0&&(a=n),e[a]=t[n]}),x1=lt&&lt.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),Zh=lt&&lt.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var n in e)n!=="default"&&Object.prototype.hasOwnProperty.call(e,n)&&Ih(t,e,n);return x1(t,e),t},O1=lt&&lt.__exportStar||function(e,t){for(var n in e)n!=="default"&&!Object.prototype.hasOwnProperty.call(t,n)&&Ih(t,e,n)};Object.defineProperty(lt,"__esModule",{value:!0});lt.Agent=void 0;var R1=Zh(require("net")),Xh=Zh(require("http")),_1=require("https");O1(Yh(),lt);var Pt=Symbol("AgentBaseInternalState"),qu=class extends Xh.Agent{constructor(t){super(t),this[Pt]={}}isSecureEndpoint(t){if(t){if(typeof t.secureEndpoint=="boolean")return t.secureEndpoint;if(typeof t.protocol=="string")return t.protocol==="https:"}let{stack:n}=new Error;return typeof n!="string"?!1:n.split(`
`).some(a=>a.indexOf("(https.js:")!==-1||a.indexOf("node:https:")!==-1)}incrementSockets(t){if(this.maxSockets===1/0&&this.maxTotalSockets===1/0)return null;this.sockets[t]||(this.sockets[t]=[]);let n=new R1.Socket({writable:!1});return this.sockets[t].push(n),this.totalSocketCount++,n}decrementSockets(t,n){if(!this.sockets[t]||n===null)return;let a=this.sockets[t],i=a.indexOf(n);i!==-1&&(a.splice(i,1),this.totalSocketCount--,a.length===0&&delete this.sockets[t])}getName(t){return(typeof t.secureEndpoint=="boolean"?t.secureEndpoint:this.isSecureEndpoint(t))?_1.Agent.prototype.getName.call(this,t):super.getName(t)}createSocket(t,n,a){let i={...n,secureEndpoint:this.isSecureEndpoint(n)},s=this.getName(i),r=this.incrementSockets(s);Promise.resolve().then(()=>this.connect(t,i)).then(l=>{if(this.decrementSockets(s,r),l instanceof Xh.Agent)try{return l.addRequest(t,i)}catch(o){return a(o)}this[Pt].currentSocket=l,super.createSocket(t,n,a)},l=>{this.decrementSockets(s,r),a(l)})}createConnection(){let t=this[Pt].currentSocket;if(this[Pt].currentSocket=void 0,!t)throw new Error("No socket was returned in the `connect()` function");return t}get defaultPort(){var t;return(t=this[Pt].defaultPort)!=null?t:this.protocol==="https:"?443:80}set defaultPort(t){this[Pt]&&(this[Pt].defaultPort=t)}get protocol(){var t;return(t=this[Pt].protocol)!=null?t:this.isSecureEndpoint()?"https:":"http:"}set protocol(t){this[Pt]&&(this[Pt].protocol=t)}};lt.Agent=qu});var Wh=D(Fa=>{"use strict";var k1=Fa&&Fa.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Fa,"__esModule",{value:!0});Fa.parseProxyResponse=void 0;var U1=k1(zu()),Br=(0,U1.default)("https-proxy-agent:parse-proxy-response");function N1(e){return new Promise((t,n)=>{let a=0,i=[];function s(){let c=e.read();c?u(c):e.once("readable",s)}function r(){e.removeListener("end",l),e.removeListener("error",o),e.removeListener("readable",s)}function l(){r(),Br("onend"),n(new Error("Proxy connection ended before receiving CONNECT response"))}function o(c){r(),Br("onerror %o",c),n(c)}function u(c){i.push(c),a+=c.length;let f=Buffer.concat(i,a),d=f.indexOf(`\r
\r
`);if(d===-1){Br("have not received end of HTTP headers yet..."),s();return}let m=f.slice(0,d).toString("ascii").split(`\r
`),y=m.shift();if(!y)return e.destroy(),n(new Error("No header received from proxy CONNECT response"));let E=y.split(" "),M=+E[1],g=E.slice(2).join(" "),h={};for(let p of m){if(!p)continue;let S=p.indexOf(":");if(S===-1)return e.destroy(),n(new Error(`Invalid header from proxy CONNECT response: "${p}"`));let A=p.slice(0,S).toLowerCase(),_=p.slice(S+1).trimStart(),O=h[A];typeof O=="string"?h[A]=[O,_]:Array.isArray(O)?O.push(_):h[A]=_}Br("got proxy server response: %o %o",y,h),r(),t({connect:{statusCode:M,statusText:g,headers:h},buffered:f})}e.on("error",o),e.on("end",l),s()})}Fa.parseProxyResponse=N1});var zr=D(bt=>{"use strict";var D1=bt&&bt.__createBinding||(Object.create?function(e,t,n,a){a===void 0&&(a=n);var i=Object.getOwnPropertyDescriptor(t,n);(!i||("get"in i?!t.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,a,i)}:function(e,t,n,a){a===void 0&&(a=n),e[a]=t[n]}),B1=bt&&bt.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),nm=bt&&bt.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var n in e)n!=="default"&&Object.prototype.hasOwnProperty.call(e,n)&&D1(t,e,n);return B1(t,e),t},am=bt&&bt.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(bt,"__esModule",{value:!0});bt.HttpsProxyAgent=void 0;var Pr=nm(require("net")),Jh=nm(require("tls")),P1=am(require("assert")),L1=am(zu()),z1=Kh(),q1=require("url"),H1=Wh(),es=(0,L1.default)("https-proxy-agent"),em=e=>e.servername===void 0&&e.host&&!Pr.isIP(e.host)?{...e,servername:e.host}:e,Lr=class extends z1.Agent{constructor(t,n){var s;super(n),this.options={path:void 0},this.proxy=typeof t=="string"?new q1.URL(t):t,this.proxyHeaders=(s=n==null?void 0:n.headers)!=null?s:{},es("Creating new HttpsProxyAgent instance: %o",this.proxy.href);let a=(this.proxy.hostname||this.proxy.host).replace(/^\[|\]$/g,""),i=this.proxy.port?parseInt(this.proxy.port,10):this.proxy.protocol==="https:"?443:80;this.connectOpts={ALPNProtocols:["http/1.1"],...n?tm(n,"headers"):null,host:a,port:i}}async connect(t,n){let{proxy:a}=this;if(!n.host)throw new TypeError('No "host" provided');let i;a.protocol==="https:"?(es("Creating `tls.Socket`: %o",this.connectOpts),i=Jh.connect(em(this.connectOpts))):(es("Creating `net.Socket`: %o",this.connectOpts),i=Pr.connect(this.connectOpts));let s=typeof this.proxyHeaders=="function"?this.proxyHeaders():{...this.proxyHeaders},r=Pr.isIPv6(n.host)?`[${n.host}]`:n.host,l=`CONNECT ${r}:${n.port} HTTP/1.1\r
`;if(a.username||a.password){let d=`${decodeURIComponent(a.username)}:${decodeURIComponent(a.password)}`;s["Proxy-Authorization"]=`Basic ${Buffer.from(d).toString("base64")}`}s.Host=`${r}:${n.port}`,s["Proxy-Connection"]||(s["Proxy-Connection"]=this.keepAlive?"Keep-Alive":"close");for(let d of Object.keys(s))l+=`${d}: ${s[d]}\r
`;let o=(0,H1.parseProxyResponse)(i);i.write(`${l}\r
`);let{connect:u,buffered:c}=await o;if(t.emit("proxyConnect",u),this.emit("proxyConnect",u,t),u.statusCode===200)return t.once("socket",Q1),n.secureEndpoint?(es("Upgrading socket connection to TLS"),Jh.connect({...tm(em(n),"host","path","port"),socket:i})):i;i.destroy();let f=new Pr.Socket({writable:!1});return f.readable=!0,t.once("socket",d=>{es("Replaying proxy buffer for failed request"),(0,P1.default)(d.listenerCount("data")>0),d.push(c),d.push(null)}),f}};Lr.protocols=["http","https"];bt.HttpsProxyAgent=Lr;function Q1(e){e.resume()}function tm(e,...t){let n={},a;for(a in e)t.includes(a)||(n[a]=e[a]);return n}});var ym=D(Ue=>{"use strict";var j1=Nu(),V1=Du(),F1=(Pu(),nu(Bu)),G1=Lu();function Gu(e){return e&&e.__esModule?e:{default:e}}var $1=Gu(V1),Y1=Gu(F1),X1=Gu(G1),I1=(e=>typeof require!="undefined"?require:typeof Proxy!="undefined"?new Proxy(e,{get:(t,n)=>(typeof require!="undefined"?require:t)[n]}):e)(function(e){if(typeof require!="undefined")return require.apply(this,arguments);throw Error('Dynamic require of "'+e+'" is not supported')}),fa=class extends Error{constructor(e){super(e),this.name="EdgeTTSException"}},Hu=class extends fa{constructor(e){super(e),this.name="SkewAdjustmentError"}},Qu=class extends fa{constructor(e){super(e),this.name="UnknownResponse"}},oa=class extends fa{constructor(e){super(e),this.name="UnexpectedResponse"}},rm=class extends fa{constructor(e){super(e),this.name="NoAudioReceived"}},ju=class extends fa{constructor(e){super(e),this.name="WebSocketError"}},Ga=class extends fa{constructor(e){super(e),this.name="ValueError"}};function Z1(e){return e.replace(/[\u0000-\u0008\u000B\u000C\u000E-\u001F]/g," ")}function Vu(){return j1.v4().replace(/-/g,"")}function K1(e,t){let n=e.subarray(0,t),a=n.lastIndexOf(`
`);return a<0&&(a=n.lastIndexOf(" ")),a}function W1(e){let t=e.length;for(;t>0;){if(e.subarray(0,t).toString("utf-8").endsWith("\uFFFD")){t--;continue}return t}return t}function J1(e,t){let n=e.lastIndexOf("&",t-1);for(;n!==-1;){let a=e.indexOf(";",n);if(a!==-1&&a<t)break;t=n,n=e.lastIndexOf("&",t-1)}return t}function*eT(e,t){let n=Buffer.isBuffer(e)?e:Buffer.from(e,"utf-8");if(t<=0)throw new Ga("byteLength must be greater than 0");for(;n.length>t;){let i=K1(n,t);if(i<0&&(i=W1(n.subarray(0,t))),i=J1(n,i),i<=0)throw new Ga("Maximum byte length is too small or invalid text structure near '&' or invalid UTF-8");let r=n.subarray(0,i).toString("utf-8").trim();r&&(yield Buffer.from(r,"utf-8")),n=n.subarray(i)}let a=n.toString("utf-8").trim();a&&(yield Buffer.from(a,"utf-8"))}function lm(e,t){let n=Buffer.isBuffer(t)?t.toString("utf-8"):t;return`<speak version='1.0' xmlns='http://www.w3.org/2001/10/synthesis' xml:lang='en-US'><voice name='${e.voice}'><prosody pitch='${e.pitch}' rate='${e.rate}' volume='${e.volume}'>${n}</prosody></voice></speak>`}function Fu(){return new Date().toUTCString().replace("GMT","GMT+0000 (Coordinated Universal Time)")}function om(e,t,n){return`X-RequestId:${e}\r
Content-Type:application/ssml+xml\r
X-Timestamp:${t}Z\r
Path:ssml\r
\r
${n}`}function tT(e){return 65536-(om(Vu(),Fu(),lm(e,"")).length+50)}function nT(e){return e.replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&gt;/g,">")}var aT=class ts{constructor({voice:t,rate:n="+0%",volume:a="+0%",pitch:i="+0Hz"}){this.voice=t,this.rate=n,this.volume=a,this.pitch=i,this.validate()}validate(){let t=/^([a-z]{2,})-([A-Z]{2,})-(.+Neural)$/.exec(this.voice);if(t){let[,n]=t,[,,a,i]=t;if(i.includes("-")){let s=i.split("-");a+=`-${s[0]}`,i=s[1]}this.voice=`Microsoft Server Speech Text to Speech Voice (${n}-${a}, ${i})`}ts.validateStringParam("voice",this.voice,/^Microsoft Server Speech Text to Speech Voice \(.+,.+\)$/),ts.validateStringParam("rate",this.rate,/^[+-]\d+%$/),ts.validateStringParam("volume",this.volume,/^[+-]\d+%$/),ts.validateStringParam("pitch",this.pitch,/^[+-]\d+Hz$/)}static validateStringParam(t,n,a){if(typeof n!="string")throw new TypeError(`${t} must be a string`);if(!a.test(n))throw new Ga(`Invalid ${t} '${n}'.`)}},um="speech.platform.bing.com/consumer/speech/synthesize/readaloud",$u="6A5AA1D4EAFF4E9FB37E23D68491D6F4",iT=`wss://${um}/edge/v1?TrustedClientToken=${$u}`,sT=`https://${um}/voices/list?trustedclienttoken=${$u}`,rT="en-US-EmmaMultilingualNeural",cm="130.0.2849.68",Hr=cm.split(".")[0],fm=`1-${cm}`,dm={"User-Agent":`Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/${Hr}.0.0.0 Safari/537.36 Edg/${Hr}.0.0.0`,"Accept-Encoding":"gzip, deflate, br","Accept-Language":"en-US,en;q=0.9"},lT={...dm,Pragma:"no-cache","Cache-Control":"no-cache",Origin:"chrome-extension://jdiccldimpdaibmpdkjnbmckianbfold"},oT={...dm,Authority:"speech.platform.bing.com","Sec-CH-UA":`" Not;A Brand";v="99", "Microsoft Edge";v="${Hr}", "Chromium";v="${Hr}"`,"Sec-CH-UA-Mobile":"?0",Accept:"*/*","Sec-Fetch-Site":"none","Sec-Fetch-Mode":"cors","Sec-Fetch-Dest":"empty"};function uT(){var t;let e={name:"unknown",isNode:!1,isDeno:!1,isBun:!1,isBrowser:!1,isWebWorker:!1};return typeof globalThis.Deno!="undefined"?(e.name="deno",e.isDeno=!0,e.version=(t=globalThis.Deno.version)==null?void 0:t.deno,e):typeof globalThis.Bun!="undefined"?(e.name="bun",e.isBun=!0,e.version=globalThis.Bun.version,e):typeof process!="undefined"&&process.versions&&process.versions.node?(e.name="node",e.isNode=!0,e.version=process.versions.node,e):typeof globalThis.importScripts=="function"&&typeof globalThis.WorkerGlobalScope!="undefined"?(e.name="webworker",e.isWebWorker=!0,e):(typeof window!="undefined"&&(e.name="browser",e.isBrowser=!0),e)}function cT(){let e=uT();if(e.isDeno||e.isBrowser||e.isWebWorker)return globalThis.crypto;if(e.isNode||e.isBun)try{return typeof globalThis.crypto!="undefined"?globalThis.crypto:I1("isomorphic-webcrypto")}catch(t){throw new Error("No crypto implementation available. Please install isomorphic-webcrypto.")}throw new Error("Unsupported runtime environment")}var fT=11644473600,dT=1e9,hm=class ua{static adjClockSkewSeconds(t){ua.clockSkewSeconds+=t}static getUnixTimestamp(){return Date.now()/1e3+ua.clockSkewSeconds}static parseRfc2616Date(t){try{return new Date(t).getTime()/1e3}catch(n){return null}}static handleClientResponseError(t){let n=null;if("headers"in t&&typeof t.headers=="object")if("get"in t.headers&&typeof t.headers.get=="function")n=t.headers.get("date");else{let s=t.headers;n=s.date||s.Date}if(!n)throw new Hu("No server date in headers.");let a=ua.parseRfc2616Date(n);if(a===null)throw new Hu(`Failed to parse server date: ${n}`);let i=ua.getUnixTimestamp();ua.adjClockSkewSeconds(a-i)}static async generateSecMsGec(){let t=ua.getUnixTimestamp();t+=fT,t-=t%300,t*=dT/100;let n=`${t.toFixed(0)}${$u}`,a=cT();if(!a||!a.subtle)throw new Error("Crypto API not available");let s=new TextEncoder().encode(n),r=await a.subtle.digest("SHA-256",s);return Array.from(new Uint8Array(r)).map(o=>o.toString(16).padStart(2,"0")).join("").toUpperCase()}};hm.clockSkewSeconds=0;var jr=hm,ca={from:(e,t)=>{if(typeof e=="string")return new TextEncoder().encode(e);if(e instanceof ArrayBuffer)return new Uint8Array(e);if(e instanceof Uint8Array)return e;throw new Error("Unsupported input type for IsomorphicBuffer.from")},concat:e=>{let t=e.reduce((i,s)=>i+s.length,0),n=new Uint8Array(t),a=0;for(let i of e)n.set(i,a),a+=i.length;return n},isBuffer:e=>e instanceof Uint8Array,toString:(e,t)=>new TextDecoder(t||"utf-8").decode(e)};function hT(e){let t=ca.toString(e),n=t.indexOf(`\r
\r
`),a={};if(n!==-1){let r=t.substring(0,n).split(`\r
`);for(let l of r){let[o,u]=l.split(":",2);o&&u&&(a[o]=u.trim())}}let i=new TextEncoder().encode(t.substring(0,n+4)).length;return[a,e.slice(i)]}function mT(e){if(e.length<2)throw new Error("Message too short to contain header length");let t=e[0]<<8|e[1],n={};if(t>0&&t+2<=e.length){let a=e.slice(2,t+2),s=ca.toString(a).split(`\r
`);for(let r of s){let[l,o]=r.split(":",2);l&&o&&(n[l]=o.trim())}}return[n,e.slice(t+2)]}var mm=class{constructor(e,t={}){var i,s,r;if(this.state={partialText:ca.from(""),offsetCompensation:0,lastDurationOffset:0,streamWasCalled:!1},this.ttsConfig=new aT({voice:t.voice||rT,rate:t.rate,volume:t.volume,pitch:t.pitch}),typeof e!="string")throw new TypeError("text must be a string");let n=$1.default(Z1(e)),a=tT(this.ttsConfig);this.texts=function*(){for(let l of eT(n,a))l instanceof Uint8Array?yield l:yield new Uint8Array(l)}(),this.proxy=t.proxy,this.connectionTimeout=t.connectionTimeout,this.isNode=typeof globalThis!="undefined"?((s=(i=globalThis.process)==null?void 0:i.versions)==null?void 0:s.node)!==void 0:typeof process!="undefined"&&((r=process.versions)==null?void 0:r.node)!==void 0}parseMetadata(e){let t=JSON.parse(ca.toString(e));for(let n of t.Metadata){let a=n.Type;if(a==="WordBoundary"){let i=n.Data.Offset+this.state.offsetCompensation,s=n.Data.Duration;return{type:a,offset:i,duration:s,text:nT(n.Data.text.Text)}}if(a!=="SessionEnd")throw new Qu(`Unknown metadata type: ${a}`)}throw new oa("No WordBoundary metadata found")}async createWebSocket(e){let t={headers:lT};if(this.connectionTimeout&&(t.timeout=this.connectionTimeout),this.isNode&&this.proxy)try{let{HttpsProxyAgent:n}=await Promise.resolve().then(()=>Ce(zr()));t.agent=new n(this.proxy)}catch(n){console.warn("Proxy not supported in this environment:",n)}return new Y1.default(e,t)}async*_stream(){let e=`${iT}&Sec-MS-GEC=${await jr.generateSecMsGec()}&Sec-MS-GEC-Version=${fm}&ConnectionId=${Vu()}`,t=await this.createWebSocket(e),n=[],a=null,i=(r,l)=>{let o=r.data||r;if(!(l!=null?l:o instanceof ArrayBuffer||o instanceof Uint8Array)&&typeof o=="string"){let[c,f]=hT(ca.from(o)),d=c.Path;if(d==="audio.metadata")try{let m=this.parseMetadata(f);this.state.lastDurationOffset=m.offset+m.duration,n.push(m)}catch(m){n.push(m)}else d==="turn.end"?(this.state.offsetCompensation=this.state.lastDurationOffset,t.close()):d!=="response"&&d!=="turn.start"&&n.push(new Qu(`Unknown path received: ${d}`))}else{let c;if(o instanceof ArrayBuffer)c=ca.from(o);else if(o instanceof Uint8Array)c=o;else{n.push(new oa("Unknown binary data type"));return}if(c.length<2)n.push(new oa("We received a binary message, but it is missing the header length."));else{let[f,d]=mT(c);f.Path!=="audio"?n.push(new oa("Received binary message, but the path is not audio.")):f["Content-Type"]!=="audio/mpeg"?d.length>0&&n.push(new oa("Received binary message, but with an unexpected Content-Type.")):d.length===0?n.push(new oa("Received binary message, but it is missing the audio data.")):n.push({type:"audio",data:d})}}a&&a()};this.isNode?(t.on("message",i),t.on("error",r=>{n.push(new ju(r.message)),a&&a()}),t.on("close",()=>{n.push("close"),a&&a()})):(t.onmessage=i,t.onerror=r=>{n.push(new ju(r.message||"WebSocket error")),a&&a()},t.onclose=()=>{n.push("close"),a&&a()}),await new Promise((r,l)=>{let o=()=>r(),u=c=>l(c);this.isNode?(t.on("open",o),t.on("error",u)):(t.onopen=o,t.onerror=u)}),t.send(`X-Timestamp:${Fu()}\r
Content-Type:application/json; charset=utf-8\r
Path:speech.config\r
\r
{"context":{"synthesis":{"audio":{"metadataoptions":{"sentenceBoundaryEnabled":"false","wordBoundaryEnabled":"true"},"outputFormat":"audio-24khz-48kbitrate-mono-mp3"}}}}\r
`),t.send(om(Vu(),Fu(),lm(this.ttsConfig,ca.toString(this.state.partialText))));let s=!1;for(;;)if(n.length>0){let r=n.shift();if(r==="close"){if(!s)throw new rm("No audio was received.");break}else{if(r instanceof Error)throw r;r.type==="audio"&&(s=!0),yield r}}else await new Promise(r=>{a=r,setTimeout(r,50)})}async*stream(){if(this.state.streamWasCalled)throw new Error("stream can only be called once.");this.state.streamWasCalled=!0;for(let e of this.texts){this.state.partialText=e;for await(let t of this._stream())yield t}}},ns=class extends Error{constructor(e,t){super(e),this.name="FetchError",this.response=t}};async function im(e){let t=`${sT}&Sec-MS-GEC=${await jr.generateSecMsGec()}&Sec-MS-GEC-Version=${fm}`,n={headers:oT};e&&console.warn("Proxy support in isomorphic environment is limited. Consider using a backend proxy.");try{let a=await X1.default(t,n);if(!a.ok){let s={};throw a.headers.forEach((r,l)=>{s[l]=r}),new ns(`HTTP ${a.status}`,{status:a.status,headers:s})}let i=await a.json();for(let s of i)s.VoiceTag.ContentCategories=s.VoiceTag.ContentCategories.map(r=>r.trim()),s.VoiceTag.VoicePersonalities=s.VoiceTag.VoicePersonalities.map(r=>r.trim());return i}catch(a){throw a instanceof ns?a:new ns(a instanceof Error?a.message:"Unknown fetch error")}}async function gm(e){var t;try{return await im(e)}catch(n){if(n instanceof ns&&((t=n.response)==null?void 0:t.status)===403)return jr.handleClientResponseError(n.response),await im(e);throw n}}var gT=class pm{constructor(){this.voices=[],this.calledCreate=!1}static async create(t,n){let a=new pm,i=t!=null?t:await gm(n);return a.voices=i.map(s=>({...s,Language:s.Locale.split("-")[0]})),a.calledCreate=!0,a}find(t){if(!this.calledCreate)throw new Error("IsomorphicVoicesManager.find() called before IsomorphicVoicesManager.create()");return this.voices.filter(n=>Object.entries(t).every(([a,i])=>n[a]===i))}};function pT(e){if(e.length===0)return new Uint8Array(0);if(e.length===1)return e[0];let t=e.reduce((i,s)=>i+s.length,0),n=new Uint8Array(t),a=0;for(let i of e)i.length>0&&(n.set(i,a),a+=i.length);return n}var yT=class{constructor(e,t="Microsoft Server Speech Text to Speech Voice (en-US, EmmaMultilingualNeural)",n={}){this.text=e,this.voice=t,this.rate=n.rate||"+0%",this.volume=n.volume||"+0%",this.pitch=n.pitch||"+0Hz"}async synthesize(){let e=new mm(this.text,{voice:this.voice,rate:this.rate,volume:this.volume,pitch:this.pitch}),t=[],n=[];for await(let s of e.stream())s.type==="audio"&&s.data?t.push(s.data):s.type==="WordBoundary"&&s.offset!==void 0&&s.duration!==void 0&&s.text!==void 0&&n.push({offset:s.offset,duration:s.duration,text:s.text});let a=pT(t);return{audio:new Blob([a],{type:"audio/mpeg"}),subtitle:n}}};function Qr(e,t){let n=Math.floor(e/1e7),a=Math.floor(n/3600),i=Math.floor(n%3600/60),s=n%60,r=Math.floor(e%1e7/1e4),l=t==="vtt"?".":",";return`${qr(a)}:${qr(i)}:${qr(s)}${l}${qr(r,3)}`}function qr(e,t=2){return e.toString().padStart(t,"0")}function bT(e){let t=`WEBVTT

`;return e.forEach((n,a)=>{let i=Qr(n.offset,"vtt"),s=Qr(n.offset+n.duration,"vtt");t+=`${a+1}
`,t+=`${i} --> ${s}
`,t+=`${n.text}

`}),t}function vT(e){let t="";return e.forEach((n,a)=>{let i=Qr(n.offset,"srt"),s=Qr(n.offset+n.duration,"srt");t+=`${a+1}
`,t+=`${i} --> ${s}
`,t+=`${n.text}

`}),t}function sm(e){let t=Math.floor(e/3600),n=Math.floor(e%3600/60),a=Math.floor(e%60),i=Math.round((e-Math.floor(e))*1e3),s=(r,l=2)=>r.toString().padStart(l,"0");return`${s(t)}:${s(n)}:${s(a)},${s(i,3)}`}var ST=class{constructor(){this.cues=[]}feed(e){if(e.type!=="WordBoundary"||e.offset===void 0||e.duration===void 0||e.text===void 0)throw new Ga("Invalid message type, expected 'WordBoundary' with offset, duration and text");let t=e.offset/1e7,n=(e.offset+e.duration)/1e7;this.cues.push({index:this.cues.length+1,start:t,end:n,content:e.text})}mergeCues(e){if(e<=0)throw new Ga("Invalid number of words to merge, expected > 0");if(this.cues.length===0)return;let t=[],n=this.cues[0];for(let a of this.cues.slice(1))n.content.split(" ").length<e?n={...n,end:a.end,content:`${n.content} ${a.content}`}:(t.push(n),n=a);t.push(n),this.cues=t.map((a,i)=>({...a,index:i+1}))}getSrt(){return this.cues.map(e=>`${e.index}\r
${sm(e.start)} --> ${sm(e.end)}\r
${e.content}\r
`).join(`\r
`)}toString(){return this.getSrt()}};Ue.Communicate=mm;Ue.DRM=jr;Ue.EdgeTTS=yT;Ue.EdgeTTSException=fa;Ue.FetchError=ns;Ue.NoAudioReceived=rm;Ue.SkewAdjustmentError=Hu;Ue.SubMaker=ST;Ue.UnexpectedResponse=oa;Ue.UnknownResponse=Qu;Ue.ValueError=Ga;Ue.VoicesManager=gT;Ue.WebSocketError=ju;Ue.createSRT=vT;Ue.createVTT=bT;Ue.listVoices=gm});var ng=D(($A,tg)=>{"use strict";function km(e,t){return function(){return e.apply(t,arguments)}}var{toString:TT}=Object.prototype,{getPrototypeOf:ac}=Object,{iterator:Ir,toStringTag:Um}=Symbol,Zr=(e=>t=>{let n=TT.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),_t=e=>(e=e.toLowerCase(),t=>Zr(t)===e),Kr=e=>t=>typeof t===e,{isArray:Xa}=Array,is=Kr("undefined");function ET(e){return e!==null&&!is(e)&&e.constructor!==null&&!is(e.constructor)&&We(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}var Nm=_t("ArrayBuffer");function wT(e){let t;return typeof ArrayBuffer!="undefined"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Nm(e.buffer),t}var MT=Kr("string"),We=Kr("function"),Dm=Kr("number"),Wr=e=>e!==null&&typeof e=="object",CT=e=>e===!0||e===!1,Vr=e=>{if(Zr(e)!=="object")return!1;let t=ac(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Um in e)&&!(Ir in e)},AT=_t("Date"),xT=_t("File"),OT=_t("Blob"),RT=_t("FileList"),_T=e=>Wr(e)&&We(e.pipe),kT=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||We(e.append)&&((t=Zr(e))==="formdata"||t==="object"&&We(e.toString)&&e.toString()==="[object FormData]"))},UT=_t("URLSearchParams"),[NT,DT,BT,PT]=["ReadableStream","Request","Response","Headers"].map(_t),LT=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function rs(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e=="undefined")return;let a,i;if(typeof e!="object"&&(e=[e]),Xa(e))for(a=0,i=e.length;a<i;a++)t.call(null,e[a],a,e);else{let s=n?Object.getOwnPropertyNames(e):Object.keys(e),r=s.length,l;for(a=0;a<r;a++)l=s[a],t.call(null,e[l],l,e)}}function Bm(e,t){t=t.toLowerCase();let n=Object.keys(e),a=n.length,i;for(;a-- >0;)if(i=n[a],t===i.toLowerCase())return i;return null}var da=(()=>typeof globalThis!="undefined"?globalThis:typeof self!="undefined"?self:typeof window!="undefined"?window:global)(),Pm=e=>!is(e)&&e!==da;function Zu(){let{caseless:e}=Pm(this)&&this||{},t={},n=(a,i)=>{let s=e&&Bm(t,i)||i;Vr(t[s])&&Vr(a)?t[s]=Zu(t[s],a):Vr(a)?t[s]=Zu({},a):Xa(a)?t[s]=a.slice():t[s]=a};for(let a=0,i=arguments.length;a<i;a++)arguments[a]&&rs(arguments[a],n);return t}var zT=(e,t,n,{allOwnKeys:a}={})=>(rs(t,(i,s)=>{n&&We(i)?e[s]=km(i,n):e[s]=i},{allOwnKeys:a}),e),qT=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),HT=(e,t,n,a)=>{e.prototype=Object.create(t.prototype,a),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},QT=(e,t,n,a)=>{let i,s,r,l={};if(t=t||{},e==null)return t;do{for(i=Object.getOwnPropertyNames(e),s=i.length;s-- >0;)r=i[s],(!a||a(r,e,t))&&!l[r]&&(t[r]=e[r],l[r]=!0);e=n!==!1&&ac(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},jT=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;let a=e.indexOf(t,n);return a!==-1&&a===n},VT=e=>{if(!e)return null;if(Xa(e))return e;let t=e.length;if(!Dm(t))return null;let n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},FT=(e=>t=>e&&t instanceof e)(typeof Uint8Array!="undefined"&&ac(Uint8Array)),GT=(e,t)=>{let a=(e&&e[Ir]).call(e),i;for(;(i=a.next())&&!i.done;){let s=i.value;t.call(e,s[0],s[1])}},$T=(e,t)=>{let n,a=[];for(;(n=e.exec(t))!==null;)a.push(n);return a},YT=_t("HTMLFormElement"),XT=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,a,i){return a.toUpperCase()+i}),bm=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),IT=_t("RegExp"),Lm=(e,t)=>{let n=Object.getOwnPropertyDescriptors(e),a={};rs(n,(i,s)=>{let r;(r=t(i,s,e))!==!1&&(a[s]=r||i)}),Object.defineProperties(e,a)},ZT=e=>{Lm(e,(t,n)=>{if(We(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;let a=e[n];if(We(a)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},KT=(e,t)=>{let n={},a=i=>{i.forEach(s=>{n[s]=!0})};return Xa(e)?a(e):a(String(e).split(t)),n},WT=()=>{},JT=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function eE(e){return!!(e&&We(e.append)&&e[Um]==="FormData"&&e[Ir])}var tE=e=>{let t=new Array(10),n=(a,i)=>{if(Wr(a)){if(t.indexOf(a)>=0)return;if(!("toJSON"in a)){t[i]=a;let s=Xa(a)?[]:{};return rs(a,(r,l)=>{let o=n(r,i+1);!is(o)&&(s[l]=o)}),t[i]=void 0,s}}return a};return n(e,0)},nE=_t("AsyncFunction"),aE=e=>e&&(Wr(e)||We(e))&&We(e.then)&&We(e.catch),zm=((e,t)=>e?setImmediate:t?((n,a)=>(da.addEventListener("message",({source:i,data:s})=>{i===da&&s===n&&a.length&&a.shift()()},!1),i=>{a.push(i),da.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",We(da.postMessage)),iE=typeof queueMicrotask!="undefined"?queueMicrotask.bind(da):typeof process!="undefined"&&process.nextTick||zm,sE=e=>e!=null&&We(e[Ir]),w={isArray:Xa,isArrayBuffer:Nm,isBuffer:ET,isFormData:kT,isArrayBufferView:wT,isString:MT,isNumber:Dm,isBoolean:CT,isObject:Wr,isPlainObject:Vr,isReadableStream:NT,isRequest:DT,isResponse:BT,isHeaders:PT,isUndefined:is,isDate:AT,isFile:xT,isBlob:OT,isRegExp:IT,isFunction:We,isStream:_T,isURLSearchParams:UT,isTypedArray:FT,isFileList:RT,forEach:rs,merge:Zu,extend:zT,trim:LT,stripBOM:qT,inherits:HT,toFlatObject:QT,kindOf:Zr,kindOfTest:_t,endsWith:jT,toArray:VT,forEachEntry:GT,matchAll:$T,isHTMLForm:YT,hasOwnProperty:bm,hasOwnProp:bm,reduceDescriptors:Lm,freezeMethods:ZT,toObjectSet:KT,toCamelCase:XT,noop:WT,toFiniteNumber:JT,findKey:Bm,global:da,isContextDefined:Pm,isSpecCompliantForm:eE,toJSONObject:tE,isAsyncFn:nE,isThenable:aE,setImmediate:zm,asap:iE,isIterable:sE};function z(e,t,n,a,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),a&&(this.request=a),i&&(this.response=i,this.status=i.status?i.status:null)}w.inherits(z,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:w.toJSONObject(this.config),code:this.code,status:this.status}}});var qm=z.prototype,Hm={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Hm[e]={value:e}});Object.defineProperties(z,Hm);Object.defineProperty(qm,"isAxiosError",{value:!0});z.from=(e,t,n,a,i,s)=>{let r=Object.create(qm);return w.toFlatObject(e,r,function(o){return o!==Error.prototype},l=>l!=="isAxiosError"),z.call(r,e.message,t,n,a,i),r.cause=e,r.name=e.name,s&&Object.assign(r,s),r};var rE=null;function Ku(e){return w.isPlainObject(e)||w.isArray(e)}function Qm(e){return w.endsWith(e,"[]")?e.slice(0,-2):e}function vm(e,t,n){return e?e.concat(t).map(function(i,s){return i=Qm(i),!n&&s?"["+i+"]":i}).join(n?".":""):t}function lE(e){return w.isArray(e)&&!e.some(Ku)}var oE=w.toFlatObject(w,{},null,function(t){return/^is[A-Z]/.test(t)});function Jr(e,t,n){if(!w.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=w.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(E,M){return!w.isUndefined(M[E])});let a=n.metaTokens,i=n.visitor||c,s=n.dots,r=n.indexes,o=(n.Blob||typeof Blob!="undefined"&&Blob)&&w.isSpecCompliantForm(t);if(!w.isFunction(i))throw new TypeError("visitor must be a function");function u(y){if(y===null)return"";if(w.isDate(y))return y.toISOString();if(!o&&w.isBlob(y))throw new z("Blob is not supported. Use a Buffer instead.");return w.isArrayBuffer(y)||w.isTypedArray(y)?o&&typeof Blob=="function"?new Blob([y]):Buffer.from(y):y}function c(y,E,M){let g=y;if(y&&!M&&typeof y=="object"){if(w.endsWith(E,"{}"))E=a?E:E.slice(0,-2),y=JSON.stringify(y);else if(w.isArray(y)&&lE(y)||(w.isFileList(y)||w.endsWith(E,"[]"))&&(g=w.toArray(y)))return E=Qm(E),g.forEach(function(p,S){!(w.isUndefined(p)||p===null)&&t.append(r===!0?vm([E],S,s):r===null?E:E+"[]",u(p))}),!1}return Ku(y)?!0:(t.append(vm(M,E,s),u(y)),!1)}let f=[],d=Object.assign(oE,{defaultVisitor:c,convertValue:u,isVisitable:Ku});function m(y,E){if(!w.isUndefined(y)){if(f.indexOf(y)!==-1)throw Error("Circular reference detected in "+E.join("."));f.push(y),w.forEach(y,function(g,h){(!(w.isUndefined(g)||g===null)&&i.call(t,g,w.isString(h)?h.trim():h,E,d))===!0&&m(g,E?E.concat(h):[h])}),f.pop()}}if(!w.isObject(e))throw new TypeError("data must be an object");return m(e),t}function Sm(e){let t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(a){return t[a]})}function ic(e,t){this._pairs=[],e&&Jr(e,this,t)}var jm=ic.prototype;jm.append=function(t,n){this._pairs.push([t,n])};jm.toString=function(t){let n=t?function(a){return t.call(this,a,Sm)}:Sm;return this._pairs.map(function(i){return n(i[0])+"="+n(i[1])},"").join("&")};function uE(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Vm(e,t,n){if(!t)return e;let a=n&&n.encode||uE;w.isFunction(n)&&(n={serialize:n});let i=n&&n.serialize,s;if(i?s=i(t,n):s=w.isURLSearchParams(t)?t.toString():new ic(t,n).toString(a),s){let r=e.indexOf("#");r!==-1&&(e=e.slice(0,r)),e+=(e.indexOf("?")===-1?"?":"&")+s}return e}var Wu=class{constructor(){this.handlers=[]}use(t,n,a){return this.handlers.push({fulfilled:t,rejected:n,synchronous:a?a.synchronous:!1,runWhen:a?a.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){w.forEach(this.handlers,function(a){a!==null&&t(a)})}},Tm=Wu,Fm={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},cE=typeof URLSearchParams!="undefined"?URLSearchParams:ic,fE=typeof FormData!="undefined"?FormData:null,dE=typeof Blob!="undefined"?Blob:null,hE={isBrowser:!0,classes:{URLSearchParams:cE,FormData:fE,Blob:dE},protocols:["http","https","file","blob","url","data"]},sc=typeof window!="undefined"&&typeof document!="undefined",Ju=typeof navigator=="object"&&navigator||void 0,mE=sc&&(!Ju||["ReactNative","NativeScript","NS"].indexOf(Ju.product)<0),gE=(()=>typeof WorkerGlobalScope!="undefined"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function")(),pE=sc&&window.location.href||"http://localhost",yE=Object.freeze({__proto__:null,hasBrowserEnv:sc,hasStandardBrowserWebWorkerEnv:gE,hasStandardBrowserEnv:mE,navigator:Ju,origin:pE}),He={...yE,...hE};function bE(e,t){return Jr(e,new He.classes.URLSearchParams,Object.assign({visitor:function(n,a,i,s){return He.isNode&&w.isBuffer(n)?(this.append(a,n.toString("base64")),!1):s.defaultVisitor.apply(this,arguments)}},t))}function vE(e){return w.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function SE(e){let t={},n=Object.keys(e),a,i=n.length,s;for(a=0;a<i;a++)s=n[a],t[s]=e[s];return t}function Gm(e){function t(n,a,i,s){let r=n[s++];if(r==="__proto__")return!0;let l=Number.isFinite(+r),o=s>=n.length;return r=!r&&w.isArray(i)?i.length:r,o?(w.hasOwnProp(i,r)?i[r]=[i[r],a]:i[r]=a,!l):((!i[r]||!w.isObject(i[r]))&&(i[r]=[]),t(n,a,i[r],s)&&w.isArray(i[r])&&(i[r]=SE(i[r])),!l)}if(w.isFormData(e)&&w.isFunction(e.entries)){let n={};return w.forEachEntry(e,(a,i)=>{t(vE(a),i,n,0)}),n}return null}function TE(e,t,n){if(w.isString(e))try{return(t||JSON.parse)(e),w.trim(e)}catch(a){if(a.name!=="SyntaxError")throw a}return(n||JSON.stringify)(e)}var rc={transitional:Fm,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){let a=n.getContentType()||"",i=a.indexOf("application/json")>-1,s=w.isObject(t);if(s&&w.isHTMLForm(t)&&(t=new FormData(t)),w.isFormData(t))return i?JSON.stringify(Gm(t)):t;if(w.isArrayBuffer(t)||w.isBuffer(t)||w.isStream(t)||w.isFile(t)||w.isBlob(t)||w.isReadableStream(t))return t;if(w.isArrayBufferView(t))return t.buffer;if(w.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let l;if(s){if(a.indexOf("application/x-www-form-urlencoded")>-1)return bE(t,this.formSerializer).toString();if((l=w.isFileList(t))||a.indexOf("multipart/form-data")>-1){let o=this.env&&this.env.FormData;return Jr(l?{"files[]":t}:t,o&&new o,this.formSerializer)}}return s||i?(n.setContentType("application/json",!1),TE(t)):t}],transformResponse:[function(t){let n=this.transitional||rc.transitional,a=n&&n.forcedJSONParsing,i=this.responseType==="json";if(w.isResponse(t)||w.isReadableStream(t))return t;if(t&&w.isString(t)&&(a&&!this.responseType||i)){let r=!(n&&n.silentJSONParsing)&&i;try{return JSON.parse(t)}catch(l){if(r)throw l.name==="SyntaxError"?z.from(l,z.ERR_BAD_RESPONSE,this,null,this.response):l}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:He.classes.FormData,Blob:He.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};w.forEach(["delete","get","head","post","put","patch"],e=>{rc.headers[e]={}});var lc=rc,EE=w.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),wE=e=>{let t={},n,a,i;return e&&e.split(`
`).forEach(function(r){i=r.indexOf(":"),n=r.substring(0,i).trim().toLowerCase(),a=r.substring(i+1).trim(),!(!n||t[n]&&EE[n])&&(n==="set-cookie"?t[n]?t[n].push(a):t[n]=[a]:t[n]=t[n]?t[n]+", "+a:a)}),t},Em=Symbol("internals");function as(e){return e&&String(e).trim().toLowerCase()}function Fr(e){return e===!1||e==null?e:w.isArray(e)?e.map(Fr):String(e)}function ME(e){let t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g,a;for(;a=n.exec(e);)t[a[1]]=a[2];return t}var CE=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function Yu(e,t,n,a,i){if(w.isFunction(a))return a.call(this,t,n);if(i&&(t=n),!!w.isString(t)){if(w.isString(a))return t.indexOf(a)!==-1;if(w.isRegExp(a))return a.test(t)}}function AE(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,a)=>n.toUpperCase()+a)}function xE(e,t){let n=w.toCamelCase(" "+t);["get","set","has"].forEach(a=>{Object.defineProperty(e,a+n,{value:function(i,s,r){return this[a].call(this,t,i,s,r)},configurable:!0})})}var $a=class{constructor(t){t&&this.set(t)}set(t,n,a){let i=this;function s(l,o,u){let c=as(o);if(!c)throw new Error("header name must be a non-empty string");let f=w.findKey(i,c);(!f||i[f]===void 0||u===!0||u===void 0&&i[f]!==!1)&&(i[f||o]=Fr(l))}let r=(l,o)=>w.forEach(l,(u,c)=>s(u,c,o));if(w.isPlainObject(t)||t instanceof this.constructor)r(t,n);else if(w.isString(t)&&(t=t.trim())&&!CE(t))r(wE(t),n);else if(w.isObject(t)&&w.isIterable(t)){let l={},o,u;for(let c of t){if(!w.isArray(c))throw TypeError("Object iterator must return a key-value pair");l[u=c[0]]=(o=l[u])?w.isArray(o)?[...o,c[1]]:[o,c[1]]:c[1]}r(l,n)}else t!=null&&s(n,t,a);return this}get(t,n){if(t=as(t),t){let a=w.findKey(this,t);if(a){let i=this[a];if(!n)return i;if(n===!0)return ME(i);if(w.isFunction(n))return n.call(this,i,a);if(w.isRegExp(n))return n.exec(i);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=as(t),t){let a=w.findKey(this,t);return!!(a&&this[a]!==void 0&&(!n||Yu(this,this[a],a,n)))}return!1}delete(t,n){let a=this,i=!1;function s(r){if(r=as(r),r){let l=w.findKey(a,r);l&&(!n||Yu(a,a[l],l,n))&&(delete a[l],i=!0)}}return w.isArray(t)?t.forEach(s):s(t),i}clear(t){let n=Object.keys(this),a=n.length,i=!1;for(;a--;){let s=n[a];(!t||Yu(this,this[s],s,t,!0))&&(delete this[s],i=!0)}return i}normalize(t){let n=this,a={};return w.forEach(this,(i,s)=>{let r=w.findKey(a,s);if(r){n[r]=Fr(i),delete n[s];return}let l=t?AE(s):String(s).trim();l!==s&&delete n[s],n[l]=Fr(i),a[l]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){let n=Object.create(null);return w.forEach(this,(a,i)=>{a!=null&&a!==!1&&(n[i]=t&&w.isArray(a)?a.join(", "):a)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){let a=new this(t);return n.forEach(i=>a.set(i)),a}static accessor(t){let a=(this[Em]=this[Em]={accessors:{}}).accessors,i=this.prototype;function s(r){let l=as(r);a[l]||(xE(i,r),a[l]=!0)}return w.isArray(t)?t.forEach(s):s(t),this}};$a.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);w.reduceDescriptors($a.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(a){this[n]=a}}});w.freezeMethods($a);var Rt=$a;function Xu(e,t){let n=this||lc,a=t||n,i=Rt.from(a.headers),s=a.data;return w.forEach(e,function(l){s=l.call(n,s,i.normalize(),t?t.status:void 0)}),i.normalize(),s}function $m(e){return!!(e&&e.__CANCEL__)}function Ia(e,t,n){z.call(this,e==null?"canceled":e,z.ERR_CANCELED,t,n),this.name="CanceledError"}w.inherits(Ia,z,{__CANCEL__:!0});function Ym(e,t,n){let a=n.config.validateStatus;!n.status||!a||a(n.status)?e(n):t(new z("Request failed with status code "+n.status,[z.ERR_BAD_REQUEST,z.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function OE(e){let t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function RE(e,t){e=e||10;let n=new Array(e),a=new Array(e),i=0,s=0,r;return t=t!==void 0?t:1e3,function(o){let u=Date.now(),c=a[s];r||(r=u),n[i]=o,a[i]=u;let f=s,d=0;for(;f!==i;)d+=n[f++],f=f%e;if(i=(i+1)%e,i===s&&(s=(s+1)%e),u-r<t)return;let m=c&&u-c;return m?Math.round(d*1e3/m):void 0}}function _E(e,t){let n=0,a=1e3/t,i,s,r=(u,c=Date.now())=>{n=c,i=null,s&&(clearTimeout(s),s=null),e.apply(null,u)};return[(...u)=>{let c=Date.now(),f=c-n;f>=a?r(u,c):(i=u,s||(s=setTimeout(()=>{s=null,r(i)},a-f)))},()=>i&&r(i)]}var Yr=(e,t,n=3)=>{let a=0,i=RE(50,250);return _E(s=>{let r=s.loaded,l=s.lengthComputable?s.total:void 0,o=r-a,u=i(o),c=r<=l;a=r;let f={loaded:r,total:l,progress:l?r/l:void 0,bytes:o,rate:u||void 0,estimated:u&&l&&c?(l-r)/u:void 0,event:s,lengthComputable:l!=null,[t?"download":"upload"]:!0};e(f)},n)},wm=(e,t)=>{let n=e!=null;return[a=>t[0]({lengthComputable:n,total:e,loaded:a}),t[1]]},Mm=e=>(...t)=>w.asap(()=>e(...t)),kE=He.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,He.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(He.origin),He.navigator&&/(msie|trident)/i.test(He.navigator.userAgent)):()=>!0,UE=He.hasStandardBrowserEnv?{write(e,t,n,a,i,s){let r=[e+"="+encodeURIComponent(t)];w.isNumber(n)&&r.push("expires="+new Date(n).toGMTString()),w.isString(a)&&r.push("path="+a),w.isString(i)&&r.push("domain="+i),s===!0&&r.push("secure"),document.cookie=r.join("; ")},read(e){let t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function NE(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function DE(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Xm(e,t,n){let a=!NE(t);return e&&(a||n==!1)?DE(e,t):t}var Cm=e=>e instanceof Rt?{...e}:e;function ha(e,t){t=t||{};let n={};function a(u,c,f,d){return w.isPlainObject(u)&&w.isPlainObject(c)?w.merge.call({caseless:d},u,c):w.isPlainObject(c)?w.merge({},c):w.isArray(c)?c.slice():c}function i(u,c,f,d){if(w.isUndefined(c)){if(!w.isUndefined(u))return a(void 0,u,f,d)}else return a(u,c,f,d)}function s(u,c){if(!w.isUndefined(c))return a(void 0,c)}function r(u,c){if(w.isUndefined(c)){if(!w.isUndefined(u))return a(void 0,u)}else return a(void 0,c)}function l(u,c,f){if(f in t)return a(u,c);if(f in e)return a(void 0,u)}let o={url:s,method:s,data:s,baseURL:r,transformRequest:r,transformResponse:r,paramsSerializer:r,timeout:r,timeoutMessage:r,withCredentials:r,withXSRFToken:r,adapter:r,responseType:r,xsrfCookieName:r,xsrfHeaderName:r,onUploadProgress:r,onDownloadProgress:r,decompress:r,maxContentLength:r,maxBodyLength:r,beforeRedirect:r,transport:r,httpAgent:r,httpsAgent:r,cancelToken:r,socketPath:r,responseEncoding:r,validateStatus:l,headers:(u,c,f)=>i(Cm(u),Cm(c),f,!0)};return w.forEach(Object.keys(Object.assign({},e,t)),function(c){let f=o[c]||i,d=f(e[c],t[c],c);w.isUndefined(d)&&f!==l||(n[c]=d)}),n}var Im=e=>{let t=ha({},e),{data:n,withXSRFToken:a,xsrfHeaderName:i,xsrfCookieName:s,headers:r,auth:l}=t;t.headers=r=Rt.from(r),t.url=Vm(Xm(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&r.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):"")));let o;if(w.isFormData(n)){if(He.hasStandardBrowserEnv||He.hasStandardBrowserWebWorkerEnv)r.setContentType(void 0);else if((o=r.getContentType())!==!1){let[u,...c]=o?o.split(";").map(f=>f.trim()).filter(Boolean):[];r.setContentType([u||"multipart/form-data",...c].join("; "))}}if(He.hasStandardBrowserEnv&&(a&&w.isFunction(a)&&(a=a(t)),a||a!==!1&&kE(t.url))){let u=i&&s&&UE.read(s);u&&r.set(i,u)}return t},BE=typeof XMLHttpRequest!="undefined",PE=BE&&function(e){return new Promise(function(n,a){let i=Im(e),s=i.data,r=Rt.from(i.headers).normalize(),{responseType:l,onUploadProgress:o,onDownloadProgress:u}=i,c,f,d,m,y;function E(){m&&m(),y&&y(),i.cancelToken&&i.cancelToken.unsubscribe(c),i.signal&&i.signal.removeEventListener("abort",c)}let M=new XMLHttpRequest;M.open(i.method.toUpperCase(),i.url,!0),M.timeout=i.timeout;function g(){if(!M)return;let p=Rt.from("getAllResponseHeaders"in M&&M.getAllResponseHeaders()),A={data:!l||l==="text"||l==="json"?M.responseText:M.response,status:M.status,statusText:M.statusText,headers:p,config:e,request:M};Ym(function(O){n(O),E()},function(O){a(O),E()},A),M=null}"onloadend"in M?M.onloadend=g:M.onreadystatechange=function(){!M||M.readyState!==4||M.status===0&&!(M.responseURL&&M.responseURL.indexOf("file:")===0)||setTimeout(g)},M.onabort=function(){M&&(a(new z("Request aborted",z.ECONNABORTED,e,M)),M=null)},M.onerror=function(){a(new z("Network Error",z.ERR_NETWORK,e,M)),M=null},M.ontimeout=function(){let S=i.timeout?"timeout of "+i.timeout+"ms exceeded":"timeout exceeded",A=i.transitional||Fm;i.timeoutErrorMessage&&(S=i.timeoutErrorMessage),a(new z(S,A.clarifyTimeoutError?z.ETIMEDOUT:z.ECONNABORTED,e,M)),M=null},s===void 0&&r.setContentType(null),"setRequestHeader"in M&&w.forEach(r.toJSON(),function(S,A){M.setRequestHeader(A,S)}),w.isUndefined(i.withCredentials)||(M.withCredentials=!!i.withCredentials),l&&l!=="json"&&(M.responseType=i.responseType),u&&([d,y]=Yr(u,!0),M.addEventListener("progress",d)),o&&M.upload&&([f,m]=Yr(o),M.upload.addEventListener("progress",f),M.upload.addEventListener("loadend",m)),(i.cancelToken||i.signal)&&(c=p=>{M&&(a(!p||p.type?new Ia(null,e,M):p),M.abort(),M=null)},i.cancelToken&&i.cancelToken.subscribe(c),i.signal&&(i.signal.aborted?c():i.signal.addEventListener("abort",c)));let h=OE(i.url);if(h&&He.protocols.indexOf(h)===-1){a(new z("Unsupported protocol "+h+":",z.ERR_BAD_REQUEST,e));return}M.send(s||null)})},LE=(e,t)=>{let{length:n}=e=e?e.filter(Boolean):[];if(t||n){let a=new AbortController,i,s=function(u){if(!i){i=!0,l();let c=u instanceof Error?u:this.reason;a.abort(c instanceof z?c:new Ia(c instanceof Error?c.message:c))}},r=t&&setTimeout(()=>{r=null,s(new z(`timeout ${t} of ms exceeded`,z.ETIMEDOUT))},t),l=()=>{e&&(r&&clearTimeout(r),r=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(s):u.removeEventListener("abort",s)}),e=null)};e.forEach(u=>u.addEventListener("abort",s));let{signal:o}=a;return o.unsubscribe=()=>w.asap(l),o}},zE=LE,qE=function*(e,t){let n=e.byteLength;if(!t||n<t){yield e;return}let a=0,i;for(;a<n;)i=a+t,yield e.slice(a,i),a=i},HE=async function*(e,t){for await(let n of QE(e))yield*qE(n,t)},QE=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}let t=e.getReader();try{for(;;){let{done:n,value:a}=await t.read();if(n)break;yield a}}finally{await t.cancel()}},Am=(e,t,n,a)=>{let i=HE(e,t),s=0,r,l=o=>{r||(r=!0,a&&a(o))};return new ReadableStream({async pull(o){try{let{done:u,value:c}=await i.next();if(u){l(),o.close();return}let f=c.byteLength;if(n){let d=s+=f;n(d)}o.enqueue(new Uint8Array(c))}catch(u){throw l(u),u}},cancel(o){return l(o),i.return()}},{highWaterMark:2})},el=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Zm=el&&typeof ReadableStream=="function",jE=el&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Km=(e,...t)=>{try{return!!e(...t)}catch(n){return!1}},VE=Zm&&Km(()=>{let e=!1,t=new Request(He.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),xm=64*1024,ec=Zm&&Km(()=>w.isReadableStream(new Response("").body)),Xr={stream:ec&&(e=>e.body)};el&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Xr[t]&&(Xr[t]=w.isFunction(e[t])?n=>n[t]():(n,a)=>{throw new z(`Response type '${t}' is not supported`,z.ERR_NOT_SUPPORT,a)})})})(new Response);var FE=async e=>{if(e==null)return 0;if(w.isBlob(e))return e.size;if(w.isSpecCompliantForm(e))return(await new Request(He.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(w.isArrayBufferView(e)||w.isArrayBuffer(e))return e.byteLength;if(w.isURLSearchParams(e)&&(e=e+""),w.isString(e))return(await jE(e)).byteLength},GE=async(e,t)=>{let n=w.toFiniteNumber(e.getContentLength());return n==null?FE(t):n},$E=el&&(async e=>{let{url:t,method:n,data:a,signal:i,cancelToken:s,timeout:r,onDownloadProgress:l,onUploadProgress:o,responseType:u,headers:c,withCredentials:f="same-origin",fetchOptions:d}=Im(e);u=u?(u+"").toLowerCase():"text";let m=zE([i,s&&s.toAbortSignal()],r),y,E=m&&m.unsubscribe&&(()=>{m.unsubscribe()}),M;try{if(o&&VE&&n!=="get"&&n!=="head"&&(M=await GE(c,a))!==0){let A=new Request(t,{method:"POST",body:a,duplex:"half"}),_;if(w.isFormData(a)&&(_=A.headers.get("content-type"))&&c.setContentType(_),A.body){let[O,R]=wm(M,Yr(Mm(o)));a=Am(A.body,xm,O,R)}}w.isString(f)||(f=f?"include":"omit");let g="credentials"in Request.prototype;y=new Request(t,{...d,signal:m,method:n.toUpperCase(),headers:c.normalize().toJSON(),body:a,duplex:"half",credentials:g?f:void 0});let h=await fetch(y),p=ec&&(u==="stream"||u==="response");if(ec&&(l||p&&E)){let A={};["status","statusText","headers"].forEach(q=>{A[q]=h[q]});let _=w.toFiniteNumber(h.headers.get("content-length")),[O,R]=l&&wm(_,Yr(Mm(l),!0))||[];h=new Response(Am(h.body,xm,O,()=>{R&&R(),E&&E()}),A)}u=u||"text";let S=await Xr[w.findKey(Xr,u)||"text"](h,e);return!p&&E&&E(),await new Promise((A,_)=>{Ym(A,_,{data:S,headers:Rt.from(h.headers),status:h.status,statusText:h.statusText,config:e,request:y})})}catch(g){throw E&&E(),g&&g.name==="TypeError"&&/Load failed|fetch/i.test(g.message)?Object.assign(new z("Network Error",z.ERR_NETWORK,e,y),{cause:g.cause||g}):z.from(g,g&&g.code,e,y)}}),tc={http:rE,xhr:PE,fetch:$E};w.forEach(tc,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(n){}Object.defineProperty(e,"adapterName",{value:t})}});var Om=e=>`- ${e}`,YE=e=>w.isFunction(e)||e===null||e===!1,Wm={getAdapter:e=>{e=w.isArray(e)?e:[e];let{length:t}=e,n,a,i={};for(let s=0;s<t;s++){n=e[s];let r;if(a=n,!YE(n)&&(a=tc[(r=String(n)).toLowerCase()],a===void 0))throw new z(`Unknown adapter '${r}'`);if(a)break;i[r||"#"+s]=a}if(!a){let s=Object.entries(i).map(([l,o])=>`adapter ${l} `+(o===!1?"is not supported by the environment":"is not available in the build")),r=t?s.length>1?`since :
`+s.map(Om).join(`
`):" "+Om(s[0]):"as no adapter specified";throw new z("There is no suitable adapter to dispatch the request "+r,"ERR_NOT_SUPPORT")}return a},adapters:tc};function Iu(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Ia(null,e)}function Rm(e){return Iu(e),e.headers=Rt.from(e.headers),e.data=Xu.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Wm.getAdapter(e.adapter||lc.adapter)(e).then(function(a){return Iu(e),a.data=Xu.call(e,e.transformResponse,a),a.headers=Rt.from(a.headers),a},function(a){return $m(a)||(Iu(e),a&&a.response&&(a.response.data=Xu.call(e,e.transformResponse,a.response),a.response.headers=Rt.from(a.response.headers))),Promise.reject(a)})}var Jm="1.9.0",tl={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{tl[e]=function(a){return typeof a===e||"a"+(t<1?"n ":" ")+e}});var _m={};tl.transitional=function(t,n,a){function i(s,r){return"[Axios v"+Jm+"] Transitional option '"+s+"'"+r+(a?". "+a:"")}return(s,r,l)=>{if(t===!1)throw new z(i(r," has been removed"+(n?" in "+n:"")),z.ERR_DEPRECATED);return n&&!_m[r]&&(_m[r]=!0,console.warn(i(r," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(s,r,l):!0}};tl.spelling=function(t){return(n,a)=>(console.warn(`${a} is likely a misspelling of ${t}`),!0)};function XE(e,t,n){if(typeof e!="object")throw new z("options must be an object",z.ERR_BAD_OPTION_VALUE);let a=Object.keys(e),i=a.length;for(;i-- >0;){let s=a[i],r=t[s];if(r){let l=e[s],o=l===void 0||r(l,s,e);if(o!==!0)throw new z("option "+s+" must be "+o,z.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new z("Unknown option "+s,z.ERR_BAD_OPTION)}}var Gr={assertOptions:XE,validators:tl},Lt=Gr.validators,Ya=class{constructor(t){this.defaults=t||{},this.interceptors={request:new Tm,response:new Tm}}async request(t,n){try{return await this._request(t,n)}catch(a){if(a instanceof Error){let i={};Error.captureStackTrace?Error.captureStackTrace(i):i=new Error;let s=i.stack?i.stack.replace(/^.+\n/,""):"";try{a.stack?s&&!String(a.stack).endsWith(s.replace(/^.+\n.+\n/,""))&&(a.stack+=`
`+s):a.stack=s}catch(r){}}throw a}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=ha(this.defaults,n);let{transitional:a,paramsSerializer:i,headers:s}=n;a!==void 0&&Gr.assertOptions(a,{silentJSONParsing:Lt.transitional(Lt.boolean),forcedJSONParsing:Lt.transitional(Lt.boolean),clarifyTimeoutError:Lt.transitional(Lt.boolean)},!1),i!=null&&(w.isFunction(i)?n.paramsSerializer={serialize:i}:Gr.assertOptions(i,{encode:Lt.function,serialize:Lt.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),Gr.assertOptions(n,{baseUrl:Lt.spelling("baseURL"),withXsrfToken:Lt.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let r=s&&w.merge(s.common,s[n.method]);s&&w.forEach(["delete","get","head","post","put","patch","common"],y=>{delete s[y]}),n.headers=Rt.concat(r,s);let l=[],o=!0;this.interceptors.request.forEach(function(E){typeof E.runWhen=="function"&&E.runWhen(n)===!1||(o=o&&E.synchronous,l.unshift(E.fulfilled,E.rejected))});let u=[];this.interceptors.response.forEach(function(E){u.push(E.fulfilled,E.rejected)});let c,f=0,d;if(!o){let y=[Rm.bind(this),void 0];for(y.unshift.apply(y,l),y.push.apply(y,u),d=y.length,c=Promise.resolve(n);f<d;)c=c.then(y[f++],y[f++]);return c}d=l.length;let m=n;for(f=0;f<d;){let y=l[f++],E=l[f++];try{m=y(m)}catch(M){E.call(this,M);break}}try{c=Rm.call(this,m)}catch(y){return Promise.reject(y)}for(f=0,d=u.length;f<d;)c=c.then(u[f++],u[f++]);return c}getUri(t){t=ha(this.defaults,t);let n=Xm(t.baseURL,t.url,t.allowAbsoluteUrls);return Vm(n,t.params,t.paramsSerializer)}};w.forEach(["delete","get","head","options"],function(t){Ya.prototype[t]=function(n,a){return this.request(ha(a||{},{method:t,url:n,data:(a||{}).data}))}});w.forEach(["post","put","patch"],function(t){function n(a){return function(s,r,l){return this.request(ha(l||{},{method:t,headers:a?{"Content-Type":"multipart/form-data"}:{},url:s,data:r}))}}Ya.prototype[t]=n(),Ya.prototype[t+"Form"]=n(!0)});var $r=Ya,ss=class{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(s){n=s});let a=this;this.promise.then(i=>{if(!a._listeners)return;let s=a._listeners.length;for(;s-- >0;)a._listeners[s](i);a._listeners=null}),this.promise.then=i=>{let s,r=new Promise(l=>{a.subscribe(l),s=l}).then(i);return r.cancel=function(){a.unsubscribe(s)},r},t(function(s,r,l){a.reason||(a.reason=new Ia(s,r,l),n(a.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;let n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){let t=new AbortController,n=a=>{t.abort(a)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new ss(function(i){t=i}),cancel:t}}},IE=ss;function ZE(e){return function(n){return e.apply(null,n)}}function KE(e){return w.isObject(e)&&e.isAxiosError===!0}var nc={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(nc).forEach(([e,t])=>{nc[t]=e});var WE=nc;function eg(e){let t=new $r(e),n=km($r.prototype.request,t);return w.extend(n,$r.prototype,t,{allOwnKeys:!0}),w.extend(n,t,null,{allOwnKeys:!0}),n.create=function(i){return eg(ha(e,i))},n}var ve=eg(lc);ve.Axios=$r;ve.CanceledError=Ia;ve.CancelToken=IE;ve.isCancel=$m;ve.VERSION=Jm;ve.toFormData=Jr;ve.AxiosError=z;ve.Cancel=ve.CanceledError;ve.all=function(t){return Promise.all(t)};ve.spread=ZE;ve.isAxiosError=KE;ve.mergeConfig=ha;ve.AxiosHeaders=Rt;ve.formToJSON=e=>Gm(w.isHTMLForm(e)?new FormData(e):e);ve.getAdapter=Wm.getAdapter;ve.HttpStatusCode=WE;ve.default=ve;tg.exports=ve});var Og=D(de=>{"use strict";var JE=Nu(),ew=Du(),tw=(Pu(),nu(Bu)),nw=require("crypto"),oc=ng(),aw=Lu();function cl(e){return e&&e.__esModule?e:{default:e}}var rg=cl(ew),lg=cl(tw),iw=cl(oc),sw=cl(aw),rw=(e=>typeof require!="undefined"?require:typeof Proxy!="undefined"?new Proxy(e,{get:(t,n)=>(typeof require!="undefined"?require:t)[n]}):e)(function(e){if(typeof require!="undefined")return require.apply(this,arguments);throw Error('Dynamic require of "'+e+'" is not supported')}),ya=class extends Error{constructor(e){super(e),this.name="EdgeTTSException"}},Za=class extends ya{constructor(e){super(e),this.name="SkewAdjustmentError"}},us=class extends ya{constructor(e){super(e),this.name="UnknownResponse"}},ot=class extends ya{constructor(e){super(e),this.name="UnexpectedResponse"}},uc=class extends ya{constructor(e){super(e),this.name="NoAudioReceived"}},sl=class extends ya{constructor(e){super(e),this.name="WebSocketError"}},Ka=class extends ya{constructor(e){super(e),this.name="ValueError"}};function lw(e){let t=e.indexOf(`\r
\r
`),n={},a=e.subarray(0,t).toString("utf-8");if(a){let i=a.split(`\r
`);for(let s of i){let[r,l]=s.split(":",2);r&&l&&(n[r]=l.trim())}}return[n,e.subarray(t+2)]}function ow(e){let t=e.readUInt16BE(0),n={},a=e.subarray(2,t+2).toString("utf-8");if(a){let i=a.split(`\r
`);for(let s of i){let[r,l]=s.split(":",2);r&&l&&(n[r]=l.trim())}}return[n,e.subarray(t+2)]}function og(e){return e.replace(/[\u0000-\u0008\u000B\u000C\u000E-\u001F]/g," ")}function cs(){return JE.v4().replace(/-/g,"")}function uw(e,t){let n=e.subarray(0,t),a=n.lastIndexOf(`
`);return a<0&&(a=n.lastIndexOf(" ")),a}function cw(e){let t=e.length;for(;t>0;){if(e.subarray(0,t).toString("utf-8").endsWith("\uFFFD")){t--;continue}return t}return t}function fw(e,t){let n=e.lastIndexOf("&",t-1);for(;n!==-1;){let a=e.indexOf(";",n);if(a!==-1&&a<t)break;t=n,n=e.lastIndexOf("&",t-1)}return t}function*ug(e,t){let n=Buffer.isBuffer(e)?e:Buffer.from(e,"utf-8");if(t<=0)throw new Ka("byteLength must be greater than 0");for(;n.length>t;){let i=uw(n,t);if(i<0&&(i=cw(n.subarray(0,t))),i=fw(n,i),i<=0)throw new Ka("Maximum byte length is too small or invalid text structure near '&' or invalid UTF-8");let r=n.subarray(0,i).toString("utf-8").trim();r&&(yield Buffer.from(r,"utf-8")),n=n.subarray(i)}let a=n.toString("utf-8").trim();a&&(yield Buffer.from(a,"utf-8"))}function cc(e,t){let n=Buffer.isBuffer(t)?t.toString("utf-8"):t;return`<speak version='1.0' xmlns='http://www.w3.org/2001/10/synthesis' xml:lang='en-US'><voice name='${e.voice}'><prosody pitch='${e.pitch}' rate='${e.rate}' volume='${e.volume}'>${n}</prosody></voice></speak>`}function fs(){return new Date().toUTCString().replace("GMT","GMT+0000 (Coordinated Universal Time)")}function fc(e,t,n){return`X-RequestId:${e}\r
Content-Type:application/ssml+xml\r
X-Timestamp:${t}Z\r
Path:ssml\r
\r
${n}`}function cg(e){return 65536-(fc(cs(),fs(),cc(e,"")).length+50)}function fg(e){return e.replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&gt;/g,">")}var dg=class ls{constructor({voice:t,rate:n="+0%",volume:a="+0%",pitch:i="+0Hz"}){this.voice=t,this.rate=n,this.volume=a,this.pitch=i,this.validate()}validate(){let t=/^([a-z]{2,})-([A-Z]{2,})-(.+Neural)$/.exec(this.voice);if(t){let[,n]=t,[,,a,i]=t;if(i.includes("-")){let s=i.split("-");a+=`-${s[0]}`,i=s[1]}this.voice=`Microsoft Server Speech Text to Speech Voice (${n}-${a}, ${i})`}ls.validateStringParam("voice",this.voice,/^Microsoft Server Speech Text to Speech Voice \(.+,.+\)$/),ls.validateStringParam("rate",this.rate,/^[+-]\d+%$/),ls.validateStringParam("volume",this.volume,/^[+-]\d+%$/),ls.validateStringParam("pitch",this.pitch,/^[+-]\d+Hz$/)}static validateStringParam(t,n,a){if(typeof n!="string")throw new TypeError(`${t} must be a string`);if(!a.test(n))throw new Ka(`Invalid ${t} '${n}'.`)}},hg="speech.platform.bing.com/consumer/speech/synthesize/readaloud",fl="6A5AA1D4EAFF4E9FB37E23D68491D6F4",mg=`wss://${hg}/edge/v1?TrustedClientToken=${fl}`,gg=`https://${hg}/voices/list?trustedclienttoken=${fl}`,pg="en-US-EmmaMultilingualNeural",yg="130.0.2849.68",rl=yg.split(".")[0],dl=`1-${yg}`,bg={"User-Agent":`Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/${rl}.0.0.0 Safari/537.36 Edg/${rl}.0.0.0`,"Accept-Encoding":"gzip, deflate, br","Accept-Language":"en-US,en;q=0.9"},vg={...bg,Pragma:"no-cache","Cache-Control":"no-cache",Origin:"chrome-extension://jdiccldimpdaibmpdkjnbmckianbfold"},Sg={...bg,Authority:"speech.platform.bing.com","Sec-CH-UA":`" Not;A Brand";v="99", "Microsoft Edge";v="${rl}", "Chromium";v="${rl}"`,"Sec-CH-UA-Mobile":"?0",Accept:"*/*","Sec-Fetch-Site":"none","Sec-Fetch-Mode":"cors","Sec-Fetch-Dest":"empty"},dw=11644473600,hw=1e9,Tg=class ma{static adjClockSkewSeconds(t){ma.clockSkewSeconds+=t}static getUnixTimestamp(){return Date.now()/1e3+ma.clockSkewSeconds}static parseRfc2616Date(t){try{return new Date(t).getTime()/1e3}catch(n){return null}}static handleClientResponseError(t){if(!t.response||!t.response.headers)throw new Za("No server date in headers.");let n=t.response.headers.date;if(!n||typeof n!="string")throw new Za("No server date in headers.");let a=ma.parseRfc2616Date(n);if(a===null)throw new Za(`Failed to parse server date: ${n}`);let i=ma.getUnixTimestamp();ma.adjClockSkewSeconds(a-i)}static generateSecMsGec(){let t=ma.getUnixTimestamp();t+=dw,t-=t%300,t*=hw/100;let n=`${t.toFixed(0)}${fl}`;return nw.createHash("sha256").update(n,"ascii").digest("hex").toUpperCase()}};Tg.clockSkewSeconds=0;var ll=Tg,nl,Eg=class{constructor(e,t={}){if(this.state={partialText:Buffer.from(""),offsetCompensation:0,lastDurationOffset:0,streamWasCalled:!1},this.ttsConfig=new dg({voice:t.voice||pg,rate:t.rate,volume:t.volume,pitch:t.pitch}),typeof e!="string")throw new TypeError("text must be a string");this.texts=ug(rg.default(og(e)),cg(this.ttsConfig)),this.proxy=t.proxy,this.connectionTimeout=t.connectionTimeout}parseMetadata(e){let t=JSON.parse(e.toString("utf-8"));for(let n of t.Metadata){let a=n.Type;if(a==="WordBoundary"){let i=n.Data.Offset+this.state.offsetCompensation,s=n.Data.Duration;return{type:a,offset:i,duration:s,text:fg(n.Data.text.Text)}}if(a!=="SessionEnd")throw new us(`Unknown metadata type: ${a}`)}throw new ot("No WordBoundary metadata found")}async*_stream(){let e=`${mg}&Sec-MS-GEC=${ll.generateSecMsGec()}&Sec-MS-GEC-Version=${dl}&ConnectionId=${cs()}`,t;if(this.proxy){if(!nl)try{nl=(await Promise.resolve().then(()=>Ce(zr()))).HttpsProxyAgent}catch(r){console.warn("https-proxy-agent not available:",r)}nl&&(t=new nl(this.proxy))}let n=new lg.default(e,{headers:vg,timeout:this.connectionTimeout,agent:t}),a=[],i=null;n.on("message",(r,l)=>{if(l)if(r.length<2)a.push(new ot("We received a binary message, but it is missing the header length."));else if(r.readUInt16BE(0)>r.length)a.push(new ot("The header length is greater than the length of the data."));else{let[u,c]=ow(r);u.Path!=="audio"?a.push(new ot("Received binary message, but the path is not audio.")):u["Content-Type"]!=="audio/mpeg"?c.length>0&&a.push(new ot("Received binary message, but with an unexpected Content-Type.")):c.length===0?a.push(new ot("Received binary message, but it is missing the audio data.")):a.push({type:"audio",data:c})}else{let[o,u]=lw(r),c=o.Path;if(c==="audio.metadata")try{let f=this.parseMetadata(u);this.state.lastDurationOffset=f.offset+f.duration,a.push(f)}catch(f){a.push(f)}else c==="turn.end"?(this.state.offsetCompensation=this.state.lastDurationOffset,n.close()):c!=="response"&&c!=="turn.start"&&a.push(new us(`Unknown path received: ${c}`))}i&&i()}),n.on("error",r=>{a.push(new sl(r.message)),i&&i()}),n.on("close",()=>{a.push("close"),i&&i()}),await new Promise(r=>n.on("open",r)),n.send(`X-Timestamp:${fs()}\r
Content-Type:application/json; charset=utf-8\r
Path:speech.config\r
\r
{"context":{"synthesis":{"audio":{"metadataoptions":{"sentenceBoundaryEnabled":"false","wordBoundaryEnabled":"true"},"outputFormat":"audio-24khz-48kbitrate-mono-mp3"}}}}\r
`),n.send(fc(cs(),fs(),cc(this.ttsConfig,this.state.partialText)));let s=!1;for(;;)if(a.length>0){let r=a.shift();if(r==="close"){if(!s)throw new uc("No audio was received.");break}else{if(r instanceof Error)throw r;r.type==="audio"&&(s=!0),yield r}}else await new Promise(r=>{i=r,setTimeout(r,50)})}async*stream(){var e;if(this.state.streamWasCalled)throw new Error("stream can only be called once.");this.state.streamWasCalled=!0;for(let t of this.texts){this.state.partialText=t;try{for await(let n of this._stream())yield n}catch(n){if(n instanceof oc.AxiosError&&((e=n.response)==null?void 0:e.status)===403){ll.handleClientResponseError(n);for await(let a of this._stream())yield a}else throw n}}}};function ag(e){let t=Math.floor(e/3600),n=Math.floor(e%3600/60),a=Math.floor(e%60),i=Math.round((e-Math.floor(e))*1e3),s=(r,l=2)=>r.toString().padStart(l,"0");return`${s(t)}:${s(n)}:${s(a)},${s(i,3)}`}var mw=class{constructor(){this.cues=[]}feed(e){if(e.type!=="WordBoundary"||e.offset===void 0||e.duration===void 0||e.text===void 0)throw new Ka("Invalid message type, expected 'WordBoundary' with offset, duration and text");let t=e.offset/1e7,n=(e.offset+e.duration)/1e7;this.cues.push({index:this.cues.length+1,start:t,end:n,content:e.text})}mergeCues(e){if(e<=0)throw new Ka("Invalid number of words to merge, expected > 0");if(this.cues.length===0)return;let t=[],n=this.cues[0];for(let a of this.cues.slice(1))n.content.split(" ").length<e?n={...n,end:a.end,content:`${n.content} ${a.content}`}:(t.push(n),n=a);t.push(n),this.cues=t.map((a,i)=>({...a,index:i+1}))}getSrt(){return this.cues.map(e=>`${e.index}\r
${ag(e.start)} --> ${ag(e.end)}\r
${e.content}\r
`).join(`\r
`)}toString(){return this.getSrt()}};function gw(e){try{let t=new URL(e);return{host:t.hostname,port:parseInt(t.port),protocol:t.protocol}}catch(t){return!1}}async function ig(e){let t=`${gg}&Sec-MS-GEC=${ll.generateSecMsGec()}&Sec-MS-GEC-Version=${dl}`,a=(await iw.default.get(t,{headers:Sg,proxy:e?gw(e):!1})).data;for(let i of a)i.VoiceTag.ContentCategories=i.VoiceTag.ContentCategories.map(s=>s.trim()),i.VoiceTag.VoicePersonalities=i.VoiceTag.VoicePersonalities.map(s=>s.trim());return a}async function wg(e){var t;try{return await ig(e)}catch(n){if(n instanceof oc.AxiosError&&((t=n.response)==null?void 0:t.status)===403)return ll.handleClientResponseError(n),await ig(e);throw n}}var pw=class Mg{constructor(){this.voices=[],this.calledCreate=!1}static async create(t,n){let a=new Mg,i=t!=null?t:await wg(n);return a.voices=i.map(s=>({...s,Language:s.Locale.split("-")[0]})),a.calledCreate=!0,a}find(t){if(!this.calledCreate)throw new Error("VoicesManager.find() called before VoicesManager.create()");return this.voices.filter(n=>Object.entries(t).every(([a,i])=>n[a]===i))}},yw=class{constructor(e,t="Microsoft Server Speech Text to Speech Voice (zh-CN, XiaoxiaoNeural)",n={}){this.text=e,this.voice=t,this.rate=n.rate||"+0%",this.volume=n.volume||"+0%",this.pitch=n.pitch||"+0Hz"}async synthesize(){let e=new Eg(this.text,{voice:this.voice,rate:this.rate,volume:this.volume,pitch:this.pitch}),t=[],n=[];for await(let s of e.stream())s.type==="audio"&&s.data?t.push(s.data):s.type==="WordBoundary"&&s.offset!==void 0&&s.duration!==void 0&&s.text!==void 0&&n.push({offset:s.offset,duration:s.duration,text:s.text});let a=Buffer.concat(t);return{audio:new Blob([a],{type:"audio/mpeg"}),subtitle:n}}};function ol(e,t){let n=Math.floor(e/1e7),a=Math.floor(n/3600),i=Math.floor(n%3600/60),s=n%60,r=Math.floor(e%1e7/1e4),l=t==="vtt"?".":",";return`${al(a)}:${al(i)}:${al(s)}${l}${al(r,3)}`}function al(e,t=2){return e.toString().padStart(t,"0")}function bw(e){let t=`WEBVTT

`;return e.forEach((n,a)=>{let i=ol(n.offset,"vtt"),s=ol(n.offset+n.duration,"vtt");t+=`${a+1}
`,t+=`${i} --> ${s}
`,t+=`${n.text}

`}),t}function vw(e){let t="";return e.forEach((n,a)=>{let i=ol(n.offset,"srt"),s=ol(n.offset+n.duration,"srt");t+=`${a+1}
`,t+=`${i} --> ${s}
`,t+=`${n.text}

`}),t}function Sw(){var t;let e={name:"unknown",isNode:!1,isDeno:!1,isBun:!1,isBrowser:!1,isWebWorker:!1};return typeof globalThis.Deno!="undefined"?(e.name="deno",e.isDeno=!0,e.version=(t=globalThis.Deno.version)==null?void 0:t.deno,e):typeof globalThis.Bun!="undefined"?(e.name="bun",e.isBun=!0,e.version=globalThis.Bun.version,e):typeof process!="undefined"&&process.versions&&process.versions.node?(e.name="node",e.isNode=!0,e.version=process.versions.node,e):typeof globalThis.importScripts=="function"&&typeof globalThis.WorkerGlobalScope!="undefined"?(e.name="webworker",e.isWebWorker=!0,e):(typeof window!="undefined"&&(e.name="browser",e.isBrowser=!0),e)}function Tw(){let e=Sw();if(e.isDeno||e.isBrowser||e.isWebWorker)return globalThis.crypto;if(e.isNode||e.isBun)try{return typeof globalThis.crypto!="undefined"?globalThis.crypto:rw("isomorphic-webcrypto")}catch(t){throw new Error("No crypto implementation available. Please install isomorphic-webcrypto.")}throw new Error("Unsupported runtime environment")}var Ew=11644473600,ww=1e9,Cg=class ga{static adjClockSkewSeconds(t){ga.clockSkewSeconds+=t}static getUnixTimestamp(){return Date.now()/1e3+ga.clockSkewSeconds}static parseRfc2616Date(t){try{return new Date(t).getTime()/1e3}catch(n){return null}}static handleClientResponseError(t){let n=null;if("headers"in t&&typeof t.headers=="object")if("get"in t.headers&&typeof t.headers.get=="function")n=t.headers.get("date");else{let s=t.headers;n=s.date||s.Date}if(!n)throw new Za("No server date in headers.");let a=ga.parseRfc2616Date(n);if(a===null)throw new Za(`Failed to parse server date: ${n}`);let i=ga.getUnixTimestamp();ga.adjClockSkewSeconds(a-i)}static async generateSecMsGec(){let t=ga.getUnixTimestamp();t+=Ew,t-=t%300,t*=ww/100;let n=`${t.toFixed(0)}${fl}`,a=Tw();if(!a||!a.subtle)throw new Error("Crypto API not available");let s=new TextEncoder().encode(n),r=await a.subtle.digest("SHA-256",s);return Array.from(new Uint8Array(r)).map(o=>o.toString(16).padStart(2,"0")).join("").toUpperCase()}};Cg.clockSkewSeconds=0;var hl=Cg,pa={from:(e,t)=>{if(typeof e=="string")return new TextEncoder().encode(e);if(e instanceof ArrayBuffer)return new Uint8Array(e);if(e instanceof Uint8Array)return e;throw new Error("Unsupported input type for IsomorphicBuffer.from")},concat:e=>{let t=e.reduce((i,s)=>i+s.length,0),n=new Uint8Array(t),a=0;for(let i of e)n.set(i,a),a+=i.length;return n},isBuffer:e=>e instanceof Uint8Array,toString:(e,t)=>new TextDecoder(t||"utf-8").decode(e)};function Mw(e){let t=pa.toString(e),n=t.indexOf(`\r
\r
`),a={};if(n!==-1){let r=t.substring(0,n).split(`\r
`);for(let l of r){let[o,u]=l.split(":",2);o&&u&&(a[o]=u.trim())}}let i=new TextEncoder().encode(t.substring(0,n+4)).length;return[a,e.slice(i)]}function Cw(e){if(e.length<2)throw new Error("Message too short to contain header length");let t=e[0]<<8|e[1],n={};if(t>0&&t+2<=e.length){let a=e.slice(2,t+2),s=pa.toString(a).split(`\r
`);for(let r of s){let[l,o]=r.split(":",2);l&&o&&(n[l]=o.trim())}}return[n,e.slice(t+2)]}var Aw=class{constructor(e,t={}){var i,s,r;if(this.state={partialText:pa.from(""),offsetCompensation:0,lastDurationOffset:0,streamWasCalled:!1},this.ttsConfig=new dg({voice:t.voice||pg,rate:t.rate,volume:t.volume,pitch:t.pitch}),typeof e!="string")throw new TypeError("text must be a string");let n=rg.default(og(e)),a=cg(this.ttsConfig);this.texts=function*(){for(let l of ug(n,a))l instanceof Uint8Array?yield l:yield new Uint8Array(l)}(),this.proxy=t.proxy,this.connectionTimeout=t.connectionTimeout,this.isNode=typeof globalThis!="undefined"?((s=(i=globalThis.process)==null?void 0:i.versions)==null?void 0:s.node)!==void 0:typeof process!="undefined"&&((r=process.versions)==null?void 0:r.node)!==void 0}parseMetadata(e){let t=JSON.parse(pa.toString(e));for(let n of t.Metadata){let a=n.Type;if(a==="WordBoundary"){let i=n.Data.Offset+this.state.offsetCompensation,s=n.Data.Duration;return{type:a,offset:i,duration:s,text:fg(n.Data.text.Text)}}if(a!=="SessionEnd")throw new us(`Unknown metadata type: ${a}`)}throw new ot("No WordBoundary metadata found")}async createWebSocket(e){let t={headers:vg};if(this.connectionTimeout&&(t.timeout=this.connectionTimeout),this.isNode&&this.proxy)try{let{HttpsProxyAgent:n}=await Promise.resolve().then(()=>Ce(zr()));t.agent=new n(this.proxy)}catch(n){console.warn("Proxy not supported in this environment:",n)}return new lg.default(e,t)}async*_stream(){let e=`${mg}&Sec-MS-GEC=${await hl.generateSecMsGec()}&Sec-MS-GEC-Version=${dl}&ConnectionId=${cs()}`,t=await this.createWebSocket(e),n=[],a=null,i=(r,l)=>{let o=r.data||r;if(!(l!=null?l:o instanceof ArrayBuffer||o instanceof Uint8Array)&&typeof o=="string"){let[c,f]=Mw(pa.from(o)),d=c.Path;if(d==="audio.metadata")try{let m=this.parseMetadata(f);this.state.lastDurationOffset=m.offset+m.duration,n.push(m)}catch(m){n.push(m)}else d==="turn.end"?(this.state.offsetCompensation=this.state.lastDurationOffset,t.close()):d!=="response"&&d!=="turn.start"&&n.push(new us(`Unknown path received: ${d}`))}else{let c;if(o instanceof ArrayBuffer)c=pa.from(o);else if(o instanceof Uint8Array)c=o;else{n.push(new ot("Unknown binary data type"));return}if(c.length<2)n.push(new ot("We received a binary message, but it is missing the header length."));else{let[f,d]=Cw(c);f.Path!=="audio"?n.push(new ot("Received binary message, but the path is not audio.")):f["Content-Type"]!=="audio/mpeg"?d.length>0&&n.push(new ot("Received binary message, but with an unexpected Content-Type.")):d.length===0?n.push(new ot("Received binary message, but it is missing the audio data.")):n.push({type:"audio",data:d})}}a&&a()};this.isNode?(t.on("message",i),t.on("error",r=>{n.push(new sl(r.message)),a&&a()}),t.on("close",()=>{n.push("close"),a&&a()})):(t.onmessage=i,t.onerror=r=>{n.push(new sl(r.message||"WebSocket error")),a&&a()},t.onclose=()=>{n.push("close"),a&&a()}),await new Promise((r,l)=>{let o=()=>r(),u=c=>l(c);this.isNode?(t.on("open",o),t.on("error",u)):(t.onopen=o,t.onerror=u)}),t.send(`X-Timestamp:${fs()}\r
Content-Type:application/json; charset=utf-8\r
Path:speech.config\r
\r
{"context":{"synthesis":{"audio":{"metadataoptions":{"sentenceBoundaryEnabled":"false","wordBoundaryEnabled":"true"},"outputFormat":"audio-24khz-48kbitrate-mono-mp3"}}}}\r
`),t.send(fc(cs(),fs(),cc(this.ttsConfig,pa.toString(this.state.partialText))));let s=!1;for(;;)if(n.length>0){let r=n.shift();if(r==="close"){if(!s)throw new uc("No audio was received.");break}else{if(r instanceof Error)throw r;r.type==="audio"&&(s=!0),yield r}}else await new Promise(r=>{a=r,setTimeout(r,50)})}async*stream(){if(this.state.streamWasCalled)throw new Error("stream can only be called once.");this.state.streamWasCalled=!0;for(let e of this.texts){this.state.partialText=e;for await(let t of this._stream())yield t}}},os=class extends Error{constructor(e,t){super(e),this.name="FetchError",this.response=t}};async function sg(e){let t=`${gg}&Sec-MS-GEC=${await hl.generateSecMsGec()}&Sec-MS-GEC-Version=${dl}`,n={headers:Sg};e&&console.warn("Proxy support in isomorphic environment is limited. Consider using a backend proxy.");try{let a=await sw.default(t,n);if(!a.ok){let s={};throw a.headers.forEach((r,l)=>{s[l]=r}),new os(`HTTP ${a.status}`,{status:a.status,headers:s})}let i=await a.json();for(let s of i)s.VoiceTag.ContentCategories=s.VoiceTag.ContentCategories.map(r=>r.trim()),s.VoiceTag.VoicePersonalities=s.VoiceTag.VoicePersonalities.map(r=>r.trim());return i}catch(a){throw a instanceof os?a:new os(a instanceof Error?a.message:"Unknown fetch error")}}async function Ag(e){var t;try{return await sg(e)}catch(n){if(n instanceof os&&((t=n.response)==null?void 0:t.status)===403)return hl.handleClientResponseError(n.response),await sg(e);throw n}}var xw=class xg{constructor(){this.voices=[],this.calledCreate=!1}static async create(t,n){let a=new xg,i=t!=null?t:await Ag(n);return a.voices=i.map(s=>({...s,Language:s.Locale.split("-")[0]})),a.calledCreate=!0,a}find(t){if(!this.calledCreate)throw new Error("IsomorphicVoicesManager.find() called before IsomorphicVoicesManager.create()");return this.voices.filter(n=>Object.entries(t).every(([a,i])=>n[a]===i))}},Ow=class{constructor(e,t="Microsoft Server Speech Text to Speech Voice (en-US, EmmaMultilingualNeural)",n={}){this.ws=null,this.WSS_URL="wss://speech.platform.bing.com/consumer/speech/synthesize/readaloud/edge/v1",this.TRUSTED_CLIENT_TOKEN="6A5AA1D4EAFF4E9FB37E23D68491D6F4",this.text=e,this.voice=t,this.rate=n.rate||"+0%",this.volume=n.volume||"+0%",this.pitch=n.pitch||"+0Hz"}async synthesize(){if(await this.connect(),!this.ws||this.ws.readyState!==WebSocket.OPEN)throw new Error("WebSocket is not connected.");return this.ws.send(this.createSpeechConfig()),this.ws.send(this.createSSML()),new Promise((e,t)=>{let n=[],a=[];this.ws&&(this.ws.onmessage=i=>{if(typeof i.data=="string"){let{headers:s,body:r}=this.parseMessage(i.data);if(s.Path==="audio.metadata")try{let l=JSON.parse(r);if(l.Metadata&&Array.isArray(l.Metadata)){let o=l.Metadata.filter(u=>u.Type==="WordBoundary"&&u.Data).map(u=>({offset:u.Data.Offset,duration:u.Data.Duration,text:u.Data.text.Text}));a=a.concat(o)}}catch(l){}else s.Path==="turn.end"&&this.ws&&this.ws.close()}else i.data instanceof Blob&&i.data.arrayBuffer().then(s=>{let l=new DataView(s).getUint16(0);if(s.byteLength>l+2){let o=new Uint8Array(s,l+2);n.push(o)}})},this.ws.onclose=()=>{let i=new Blob(n,{type:"audio/mpeg"});e({audio:i,subtitle:a})},this.ws.onerror=i=>{t(i)})})}connect(){let e=this.generateConnectionId(),t=this.generateSecMsGec(),n=`${this.WSS_URL}?TrustedClientToken=${this.TRUSTED_CLIENT_TOKEN}&ConnectionId=${e}&Sec-MS-GEC=${t}&Sec-MS-GEC-Version=1-130.0.2849.68`;return this.ws=new WebSocket(n),new Promise((a,i)=>{if(!this.ws)return i(new Error("WebSocket not initialized"));this.ws.onopen=()=>{a()},this.ws.onerror=s=>{i(s)}})}parseMessage(e){let t=e.split(`\r
\r
`),n=t[0].split(`\r
`),a={};return n.forEach(i=>{let[s,r]=i.split(":",2);s&&r&&(a[s.trim()]=r.trim())}),{headers:a,body:t[1]||""}}createSpeechConfig(){let e={context:{synthesis:{audio:{metadataoptions:{sentenceBoundaryEnabled:!1,wordBoundaryEnabled:!0},outputFormat:"audio-24khz-48kbitrate-mono-mp3"}}}};return`X-Timestamp:${this.getTimestamp()}\r
Content-Type:application/json; charset=utf-8\r
Path:speech.config\r
\r
${JSON.stringify(e)}`}createSSML(){let e=`<speak version='1.0' xmlns='http://www.w3.org/2001/10/synthesis' xml:lang='en-US'>
      <voice name='${this.voice}'>
        <prosody pitch='${this.pitch}' rate='${this.rate}' volume='${this.volume}'>
          ${this.escapeXml(this.text)}
        </prosody>
      </voice>
    </speak>`;return`X-RequestId:${this.generateConnectionId()}\r
Content-Type:application/ssml+xml\r
X-Timestamp:${this.getTimestamp()}Z\r
Path:ssml\r
\r
${e}`}generateConnectionId(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,e=>{let t=Math.random()*16|0;return(e==="x"?t:t&3|8).toString(16)})}getTimestamp(){return new Date().toISOString().replace(/[:-]|\.\d{3}/g,"")}escapeXml(e){return e.replace(/[<>&'"]/g,t=>{switch(t){case"<":return"&lt;";case">":return"&gt;";case"&":return"&amp;";case"'":return"&apos;";case'"':return"&quot;";default:return t}})}async generateSecMsGec(){let n=Date.now()/1e3;n+=11644473600,n-=n%300,n*=1e9/100;let a=`${n.toFixed(0)}${this.TRUSTED_CLIENT_TOKEN}`,s=new TextEncoder().encode(a),r=await crypto.subtle.digest("SHA-256",s);return Array.from(new Uint8Array(r)).map(o=>o.toString(16).padStart(2,"0")).join("").toUpperCase()}};function ul(e,t){let n=Math.floor(e/1e7),a=Math.floor(n/3600),i=Math.floor(n%3600/60),s=n%60,r=Math.floor(e%1e7/1e4),l=t==="vtt"?".":",";return`${il(a)}:${il(i)}:${il(s)}${l}${il(r,3)}`}function il(e,t=2){return e.toString().padStart(t,"0")}function Rw(e){let t=`WEBVTT

`;return e.forEach((n,a)=>{let i=ul(n.offset,"vtt"),s=ul(n.offset+n.duration,"vtt");t+=`${a+1}
`,t+=`${i} --> ${s}
`,t+=`${n.text}

`}),t}function _w(e){let t="";return e.forEach((n,a)=>{let i=ul(n.offset,"srt"),s=ul(n.offset+n.duration,"srt");t+=`${a+1}
`,t+=`${i} --> ${s}
`,t+=`${n.text}

`}),t}de.Communicate=Eg;de.EdgeTTS=yw;de.EdgeTTSBrowser=Ow;de.EdgeTTSException=ya;de.FetchError=os;de.IsomorphicCommunicate=Aw;de.IsomorphicDRM=hl;de.IsomorphicVoicesManager=xw;de.NoAudioReceived=uc;de.SkewAdjustmentError=Za;de.SubMaker=mw;de.UnexpectedResponse=ot;de.UnknownResponse=us;de.ValueError=Ka;de.VoicesManager=pw;de.WebSocketError=sl;de.createSRT=vw;de.createSRTBrowser=_w;de.createVTT=bw;de.createVTTBrowser=Rw;de.listVoices=wg;de.listVoicesIsomorphic=Ag});var Qg=D(H=>{"use strict";var mc=Symbol.for("react.transitional.element"),zw=Symbol.for("react.portal"),qw=Symbol.for("react.fragment"),Hw=Symbol.for("react.strict_mode"),Qw=Symbol.for("react.profiler"),jw=Symbol.for("react.consumer"),Vw=Symbol.for("react.context"),Fw=Symbol.for("react.forward_ref"),Gw=Symbol.for("react.suspense"),$w=Symbol.for("react.memo"),Bg=Symbol.for("react.lazy"),_g=Symbol.iterator;function Yw(e){return e===null||typeof e!="object"?null:(e=_g&&e[_g]||e["@@iterator"],typeof e=="function"?e:null)}var Pg={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Lg=Object.assign,zg={};function Ja(e,t,n){this.props=e,this.context=t,this.refs=zg,this.updater=n||Pg}Ja.prototype.isReactComponent={};Ja.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Ja.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function qg(){}qg.prototype=Ja.prototype;function gc(e,t,n){this.props=e,this.context=t,this.refs=zg,this.updater=n||Pg}var pc=gc.prototype=new qg;pc.constructor=gc;Lg(pc,Ja.prototype);pc.isPureReactComponent=!0;var kg=Array.isArray,oe={H:null,A:null,T:null,S:null,V:null},Hg=Object.prototype.hasOwnProperty;function yc(e,t,n,a,i,s){return n=s.ref,{$$typeof:mc,type:e,key:t,ref:n!==void 0?n:null,props:s}}function Xw(e,t){return yc(e.type,t,void 0,void 0,void 0,e.props)}function bc(e){return typeof e=="object"&&e!==null&&e.$$typeof===mc}function Iw(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Ug=/\/+/g;function hc(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Iw(""+e.key):t.toString(36)}function Ng(){}function Zw(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch(typeof e.status=="string"?e.then(Ng,Ng):(e.status="pending",e.then(function(t){e.status==="pending"&&(e.status="fulfilled",e.value=t)},function(t){e.status==="pending"&&(e.status="rejected",e.reason=t)})),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}function Wa(e,t,n,a,i){var s=typeof e;(s==="undefined"||s==="boolean")&&(e=null);var r=!1;if(e===null)r=!0;else switch(s){case"bigint":case"string":case"number":r=!0;break;case"object":switch(e.$$typeof){case mc:case zw:r=!0;break;case Bg:return r=e._init,Wa(r(e._payload),t,n,a,i)}}if(r)return i=i(e),r=a===""?"."+hc(e,0):a,kg(i)?(n="",r!=null&&(n=r.replace(Ug,"$&/")+"/"),Wa(i,t,n,"",function(u){return u})):i!=null&&(bc(i)&&(i=Xw(i,n+(i.key==null||e&&e.key===i.key?"":(""+i.key).replace(Ug,"$&/")+"/")+r)),t.push(i)),1;r=0;var l=a===""?".":a+":";if(kg(e))for(var o=0;o<e.length;o++)a=e[o],s=l+hc(a,o),r+=Wa(a,t,n,s,i);else if(o=Yw(e),typeof o=="function")for(e=o.call(e),o=0;!(a=e.next()).done;)a=a.value,s=l+hc(a,o++),r+=Wa(a,t,n,s,i);else if(s==="object"){if(typeof e.then=="function")return Wa(Zw(e),t,n,a,i);throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.")}return r}function Sl(e,t,n){if(e==null)return e;var a=[],i=0;return Wa(e,a,"","",function(s){return t.call(n,s,i++)}),a}function Kw(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Dg=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Ww(){}H.Children={map:Sl,forEach:function(e,t,n){Sl(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Sl(e,function(){t++}),t},toArray:function(e){return Sl(e,function(t){return t})||[]},only:function(e){if(!bc(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};H.Component=Ja;H.Fragment=qw;H.Profiler=Qw;H.PureComponent=gc;H.StrictMode=Hw;H.Suspense=Gw;H.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=oe;H.__COMPILER_RUNTIME={__proto__:null,c:function(e){return oe.H.useMemoCache(e)}};H.cache=function(e){return function(){return e.apply(null,arguments)}};H.cloneElement=function(e,t,n){if(e==null)throw Error("The argument must be a React element, but you passed "+e+".");var a=Lg({},e.props),i=e.key,s=void 0;if(t!=null)for(r in t.ref!==void 0&&(s=void 0),t.key!==void 0&&(i=""+t.key),t)!Hg.call(t,r)||r==="key"||r==="__self"||r==="__source"||r==="ref"&&t.ref===void 0||(a[r]=t[r]);var r=arguments.length-2;if(r===1)a.children=n;else if(1<r){for(var l=Array(r),o=0;o<r;o++)l[o]=arguments[o+2];a.children=l}return yc(e.type,i,void 0,void 0,s,a)};H.createContext=function(e){return e={$$typeof:Vw,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null},e.Provider=e,e.Consumer={$$typeof:jw,_context:e},e};H.createElement=function(e,t,n){var a,i={},s=null;if(t!=null)for(a in t.key!==void 0&&(s=""+t.key),t)Hg.call(t,a)&&a!=="key"&&a!=="__self"&&a!=="__source"&&(i[a]=t[a]);var r=arguments.length-2;if(r===1)i.children=n;else if(1<r){for(var l=Array(r),o=0;o<r;o++)l[o]=arguments[o+2];i.children=l}if(e&&e.defaultProps)for(a in r=e.defaultProps,r)i[a]===void 0&&(i[a]=r[a]);return yc(e,s,void 0,void 0,null,i)};H.createRef=function(){return{current:null}};H.forwardRef=function(e){return{$$typeof:Fw,render:e}};H.isValidElement=bc;H.lazy=function(e){return{$$typeof:Bg,_payload:{_status:-1,_result:e},_init:Kw}};H.memo=function(e,t){return{$$typeof:$w,type:e,compare:t===void 0?null:t}};H.startTransition=function(e){var t=oe.T,n={};oe.T=n;try{var a=e(),i=oe.S;i!==null&&i(n,a),typeof a=="object"&&a!==null&&typeof a.then=="function"&&a.then(Ww,Dg)}catch(s){Dg(s)}finally{oe.T=t}};H.unstable_useCacheRefresh=function(){return oe.H.useCacheRefresh()};H.use=function(e){return oe.H.use(e)};H.useActionState=function(e,t,n){return oe.H.useActionState(e,t,n)};H.useCallback=function(e,t){return oe.H.useCallback(e,t)};H.useContext=function(e){return oe.H.useContext(e)};H.useDebugValue=function(){};H.useDeferredValue=function(e,t){return oe.H.useDeferredValue(e,t)};H.useEffect=function(e,t,n){var a=oe.H;if(typeof n=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return a.useEffect(e,t)};H.useId=function(){return oe.H.useId()};H.useImperativeHandle=function(e,t,n){return oe.H.useImperativeHandle(e,t,n)};H.useInsertionEffect=function(e,t){return oe.H.useInsertionEffect(e,t)};H.useLayoutEffect=function(e,t){return oe.H.useLayoutEffect(e,t)};H.useMemo=function(e,t){return oe.H.useMemo(e,t)};H.useOptimistic=function(e,t){return oe.H.useOptimistic(e,t)};H.useReducer=function(e,t,n){return oe.H.useReducer(e,t,n)};H.useRef=function(e){return oe.H.useRef(e)};H.useState=function(e){return oe.H.useState(e)};H.useSyncExternalStore=function(e,t,n){return oe.H.useSyncExternalStore(e,t,n)};H.useTransition=function(){return oe.H.useTransition()};H.version="19.1.0"});var tn=D((A2,jg)=>{"use strict";jg.exports=Qg()});var Wg=D(ue=>{"use strict";function Ec(e,t){var n=e.length;e.push(t);e:for(;0<n;){var a=n-1>>>1,i=e[a];if(0<Tl(i,t))e[a]=t,e[n]=i,n=a;else break e}}function Ht(e){return e.length===0?null:e[0]}function wl(e){if(e.length===0)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var a=0,i=e.length,s=i>>>1;a<s;){var r=2*(a+1)-1,l=e[r],o=r+1,u=e[o];if(0>Tl(l,n))o<i&&0>Tl(u,l)?(e[a]=u,e[o]=n,a=o):(e[a]=l,e[r]=n,a=r);else if(o<i&&0>Tl(u,n))e[a]=u,e[o]=n,a=o;else break e}}return t}function Tl(e,t){var n=e.sortIndex-t.sortIndex;return n!==0?n:e.id-t.id}ue.unstable_now=void 0;typeof performance=="object"&&typeof performance.now=="function"?(Vg=performance,ue.unstable_now=function(){return Vg.now()}):(vc=Date,Fg=vc.now(),ue.unstable_now=function(){return vc.now()-Fg});var Vg,vc,Fg,nn=[],Cn=[],Jw=1,vt=null,Qe=3,wc=!1,ds=!1,hs=!1,Mc=!1,Yg=typeof setTimeout=="function"?setTimeout:null,Xg=typeof clearTimeout=="function"?clearTimeout:null,Gg=typeof setImmediate!="undefined"?setImmediate:null;function El(e){for(var t=Ht(Cn);t!==null;){if(t.callback===null)wl(Cn);else if(t.startTime<=e)wl(Cn),t.sortIndex=t.expirationTime,Ec(nn,t);else break;t=Ht(Cn)}}function Cc(e){if(hs=!1,El(e),!ds)if(Ht(nn)!==null)ds=!0,ti||(ti=!0,ei());else{var t=Ht(Cn);t!==null&&Ac(Cc,t.startTime-e)}}var ti=!1,ms=-1,Ig=5,Zg=-1;function Kg(){return Mc?!0:!(ue.unstable_now()-Zg<Ig)}function Sc(){if(Mc=!1,ti){var e=ue.unstable_now();Zg=e;var t=!0;try{e:{ds=!1,hs&&(hs=!1,Xg(ms),ms=-1),wc=!0;var n=Qe;try{t:{for(El(e),vt=Ht(nn);vt!==null&&!(vt.expirationTime>e&&Kg());){var a=vt.callback;if(typeof a=="function"){vt.callback=null,Qe=vt.priorityLevel;var i=a(vt.expirationTime<=e);if(e=ue.unstable_now(),typeof i=="function"){vt.callback=i,El(e),t=!0;break t}vt===Ht(nn)&&wl(nn),El(e)}else wl(nn);vt=Ht(nn)}if(vt!==null)t=!0;else{var s=Ht(Cn);s!==null&&Ac(Cc,s.startTime-e),t=!1}}break e}finally{vt=null,Qe=n,wc=!1}t=void 0}}finally{t?ei():ti=!1}}}var ei;typeof Gg=="function"?ei=function(){Gg(Sc)}:typeof MessageChannel!="undefined"?(Tc=new MessageChannel,$g=Tc.port2,Tc.port1.onmessage=Sc,ei=function(){$g.postMessage(null)}):ei=function(){Yg(Sc,0)};var Tc,$g;function Ac(e,t){ms=Yg(function(){e(ue.unstable_now())},t)}ue.unstable_IdlePriority=5;ue.unstable_ImmediatePriority=1;ue.unstable_LowPriority=4;ue.unstable_NormalPriority=3;ue.unstable_Profiling=null;ue.unstable_UserBlockingPriority=2;ue.unstable_cancelCallback=function(e){e.callback=null};ue.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):Ig=0<e?Math.floor(1e3/e):5};ue.unstable_getCurrentPriorityLevel=function(){return Qe};ue.unstable_next=function(e){switch(Qe){case 1:case 2:case 3:var t=3;break;default:t=Qe}var n=Qe;Qe=t;try{return e()}finally{Qe=n}};ue.unstable_requestPaint=function(){Mc=!0};ue.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=Qe;Qe=e;try{return t()}finally{Qe=n}};ue.unstable_scheduleCallback=function(e,t,n){var a=ue.unstable_now();switch(typeof n=="object"&&n!==null?(n=n.delay,n=typeof n=="number"&&0<n?a+n:a):n=a,e){case 1:var i=-1;break;case 2:i=250;break;case 5:i=1073741823;break;case 4:i=1e4;break;default:i=5e3}return i=n+i,e={id:Jw++,callback:t,priorityLevel:e,startTime:n,expirationTime:i,sortIndex:-1},n>a?(e.sortIndex=n,Ec(Cn,e),Ht(nn)===null&&e===Ht(Cn)&&(hs?(Xg(ms),ms=-1):hs=!0,Ac(Cc,n-a))):(e.sortIndex=i,Ec(nn,e),ds||wc||(ds=!0,ti||(ti=!0,ei()))),e};ue.unstable_shouldYield=Kg;ue.unstable_wrapCallback=function(e){var t=Qe;return function(){var n=Qe;Qe=t;try{return e.apply(this,arguments)}finally{Qe=n}}}});var ep=D((O2,Jg)=>{"use strict";Jg.exports=Wg()});var np=D(Ye=>{"use strict";var eM=tn();function tp(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function An(){}var $e={d:{f:An,r:function(){throw Error(tp(522))},D:An,C:An,L:An,m:An,X:An,S:An,M:An},p:0,findDOMNode:null},tM=Symbol.for("react.portal");function nM(e,t,n){var a=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:tM,key:a==null?null:""+a,children:e,containerInfo:t,implementation:n}}var gs=eM.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function Ml(e,t){if(e==="font")return"";if(typeof t=="string")return t==="use-credentials"?t:""}Ye.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=$e;Ye.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)throw Error(tp(299));return nM(e,t,null,n)};Ye.flushSync=function(e){var t=gs.T,n=$e.p;try{if(gs.T=null,$e.p=2,e)return e()}finally{gs.T=t,$e.p=n,$e.d.f()}};Ye.preconnect=function(e,t){typeof e=="string"&&(t?(t=t.crossOrigin,t=typeof t=="string"?t==="use-credentials"?t:"":void 0):t=null,$e.d.C(e,t))};Ye.prefetchDNS=function(e){typeof e=="string"&&$e.d.D(e)};Ye.preinit=function(e,t){if(typeof e=="string"&&t&&typeof t.as=="string"){var n=t.as,a=Ml(n,t.crossOrigin),i=typeof t.integrity=="string"?t.integrity:void 0,s=typeof t.fetchPriority=="string"?t.fetchPriority:void 0;n==="style"?$e.d.S(e,typeof t.precedence=="string"?t.precedence:void 0,{crossOrigin:a,integrity:i,fetchPriority:s}):n==="script"&&$e.d.X(e,{crossOrigin:a,integrity:i,fetchPriority:s,nonce:typeof t.nonce=="string"?t.nonce:void 0})}};Ye.preinitModule=function(e,t){if(typeof e=="string")if(typeof t=="object"&&t!==null){if(t.as==null||t.as==="script"){var n=Ml(t.as,t.crossOrigin);$e.d.M(e,{crossOrigin:n,integrity:typeof t.integrity=="string"?t.integrity:void 0,nonce:typeof t.nonce=="string"?t.nonce:void 0})}}else t==null&&$e.d.M(e)};Ye.preload=function(e,t){if(typeof e=="string"&&typeof t=="object"&&t!==null&&typeof t.as=="string"){var n=t.as,a=Ml(n,t.crossOrigin);$e.d.L(e,n,{crossOrigin:a,integrity:typeof t.integrity=="string"?t.integrity:void 0,nonce:typeof t.nonce=="string"?t.nonce:void 0,type:typeof t.type=="string"?t.type:void 0,fetchPriority:typeof t.fetchPriority=="string"?t.fetchPriority:void 0,referrerPolicy:typeof t.referrerPolicy=="string"?t.referrerPolicy:void 0,imageSrcSet:typeof t.imageSrcSet=="string"?t.imageSrcSet:void 0,imageSizes:typeof t.imageSizes=="string"?t.imageSizes:void 0,media:typeof t.media=="string"?t.media:void 0})}};Ye.preloadModule=function(e,t){if(typeof e=="string")if(t){var n=Ml(t.as,t.crossOrigin);$e.d.m(e,{as:typeof t.as=="string"&&t.as!=="script"?t.as:void 0,crossOrigin:n,integrity:typeof t.integrity=="string"?t.integrity:void 0})}else $e.d.m(e)};Ye.requestFormReset=function(e){$e.d.r(e)};Ye.unstable_batchedUpdates=function(e,t){return e(t)};Ye.useFormState=function(e,t,n){return gs.H.useFormState(e,t,n)};Ye.useFormStatus=function(){return gs.H.useHostTransitionStatus()};Ye.version="19.1.0"});var sp=D((_2,ip)=>{"use strict";function ap(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__=="undefined"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(ap)}catch(e){console.error(e)}}ap(),ip.exports=np()});var lv=D(Yo=>{"use strict";var Me=ep(),xy=tn(),aM=sp();function C(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function Oy(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function nr(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Ry(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function rp(e){if(nr(e)!==e)throw Error(C(188))}function iM(e){var t=e.alternate;if(!t){if(t=nr(e),t===null)throw Error(C(188));return t!==e?null:e}for(var n=e,a=t;;){var i=n.return;if(i===null)break;var s=i.alternate;if(s===null){if(a=i.return,a!==null){n=a;continue}break}if(i.child===s.child){for(s=i.child;s;){if(s===n)return rp(i),e;if(s===a)return rp(i),t;s=s.sibling}throw Error(C(188))}if(n.return!==a.return)n=i,a=s;else{for(var r=!1,l=i.child;l;){if(l===n){r=!0,n=i,a=s;break}if(l===a){r=!0,a=i,n=s;break}l=l.sibling}if(!r){for(l=s.child;l;){if(l===n){r=!0,n=s,a=i;break}if(l===a){r=!0,a=s,n=i;break}l=l.sibling}if(!r)throw Error(C(189))}}if(n.alternate!==a)throw Error(C(190))}if(n.tag!==3)throw Error(C(188));return n.stateNode.current===n?e:t}function _y(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=_y(e),t!==null)return t;e=e.sibling}return null}var le=Object.assign,sM=Symbol.for("react.element"),Cl=Symbol.for("react.transitional.element"),Ms=Symbol.for("react.portal"),oi=Symbol.for("react.fragment"),ky=Symbol.for("react.strict_mode"),sf=Symbol.for("react.profiler"),rM=Symbol.for("react.provider"),Uy=Symbol.for("react.consumer"),on=Symbol.for("react.context"),ed=Symbol.for("react.forward_ref"),rf=Symbol.for("react.suspense"),lf=Symbol.for("react.suspense_list"),td=Symbol.for("react.memo"),Rn=Symbol.for("react.lazy");Symbol.for("react.scope");var of=Symbol.for("react.activity");Symbol.for("react.legacy_hidden");Symbol.for("react.tracing_marker");var lM=Symbol.for("react.memo_cache_sentinel");Symbol.for("react.view_transition");var lp=Symbol.iterator;function ps(e){return e===null||typeof e!="object"?null:(e=lp&&e[lp]||e["@@iterator"],typeof e=="function"?e:null)}var oM=Symbol.for("react.client.reference");function uf(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===oM?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case oi:return"Fragment";case sf:return"Profiler";case ky:return"StrictMode";case rf:return"Suspense";case lf:return"SuspenseList";case of:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case Ms:return"Portal";case on:return(e.displayName||"Context")+".Provider";case Uy:return(e._context.displayName||"Context")+".Consumer";case ed:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case td:return t=e.displayName||null,t!==null?t:uf(e.type)||"Memo";case Rn:t=e._payload,e=e._init;try{return uf(e(t))}catch(n){}}return null}var Cs=Array.isArray,N=xy.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,W=aM.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,wa={pending:!1,data:null,method:null,action:null},cf=[],ui=-1;function Yt(e){return{current:e}}function _e(e){0>ui||(e.current=cf[ui],cf[ui]=null,ui--)}function fe(e,t){ui++,cf[ui]=e.current,e.current=t}var Ft=Yt(null),js=Yt(null),qn=Yt(null),to=Yt(null);function no(e,t){switch(fe(qn,t),fe(js,e),fe(Ft,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?hy(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=hy(t),e=Ib(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}_e(Ft),fe(Ft,e)}function Oi(){_e(Ft),_e(js),_e(qn)}function ff(e){e.memoizedState!==null&&fe(to,e);var t=Ft.current,n=Ib(t,e.type);t!==n&&(fe(js,e),fe(Ft,n))}function ao(e){js.current===e&&(_e(Ft),_e(js)),to.current===e&&(_e(to),Ws._currentValue=wa)}var df=Object.prototype.hasOwnProperty,nd=Me.unstable_scheduleCallback,xc=Me.unstable_cancelCallback,uM=Me.unstable_shouldYield,cM=Me.unstable_requestPaint,Gt=Me.unstable_now,fM=Me.unstable_getCurrentPriorityLevel,Ny=Me.unstable_ImmediatePriority,Dy=Me.unstable_UserBlockingPriority,io=Me.unstable_NormalPriority,dM=Me.unstable_LowPriority,By=Me.unstable_IdlePriority,hM=Me.log,mM=Me.unstable_setDisableYieldValue,ar=null,ht=null;function Bn(e){if(typeof hM=="function"&&mM(e),ht&&typeof ht.setStrictMode=="function")try{ht.setStrictMode(ar,e)}catch(t){}}var mt=Math.clz32?Math.clz32:yM,gM=Math.log,pM=Math.LN2;function yM(e){return e>>>=0,e===0?32:31-(gM(e)/pM|0)|0}var Al=256,xl=4194304;function Sa(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function Uo(e,t,n){var a=e.pendingLanes;if(a===0)return 0;var i=0,s=e.suspendedLanes,r=e.pingedLanes;e=e.warmLanes;var l=a&134217727;return l!==0?(a=l&~s,a!==0?i=Sa(a):(r&=l,r!==0?i=Sa(r):n||(n=l&~e,n!==0&&(i=Sa(n))))):(l=a&~s,l!==0?i=Sa(l):r!==0?i=Sa(r):n||(n=a&~e,n!==0&&(i=Sa(n)))),i===0?0:t!==0&&t!==i&&!(t&s)&&(s=i&-i,n=t&-t,s>=n||s===32&&(n&4194048)!==0)?t:i}function ir(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function bM(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Py(){var e=Al;return Al<<=1,!(Al&4194048)&&(Al=256),e}function Ly(){var e=xl;return xl<<=1,!(xl&62914560)&&(xl=4194304),e}function Oc(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function sr(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function vM(e,t,n,a,i,s){var r=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var l=e.entanglements,o=e.expirationTimes,u=e.hiddenUpdates;for(n=r&~n;0<n;){var c=31-mt(n),f=1<<c;l[c]=0,o[c]=-1;var d=u[c];if(d!==null)for(u[c]=null,c=0;c<d.length;c++){var m=d[c];m!==null&&(m.lane&=-536870913)}n&=~f}a!==0&&zy(e,a,0),s!==0&&i===0&&e.tag!==0&&(e.suspendedLanes|=s&~(r&~t))}function zy(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var a=31-mt(t);e.entangledLanes|=t,e.entanglements[a]=e.entanglements[a]|1073741824|n&4194090}function qy(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var a=31-mt(n),i=1<<a;i&t|e[a]&t&&(e[a]|=t),n&=~i}}function ad(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function id(e){return e&=-e,2<e?8<e?e&134217727?32:268435456:8:2}function Hy(){var e=W.p;return e!==0?e:(e=window.event,e===void 0?32:sv(e.type))}function SM(e,t){var n=W.p;try{return W.p=e,t()}finally{W.p=n}}var Zn=Math.random().toString(36).slice(2),je="__reactFiber$"+Zn,at="__reactProps$"+Zn,qi="__reactContainer$"+Zn,hf="__reactEvents$"+Zn,TM="__reactListeners$"+Zn,EM="__reactHandles$"+Zn,op="__reactResources$"+Zn,rr="__reactMarker$"+Zn;function sd(e){delete e[je],delete e[at],delete e[hf],delete e[TM],delete e[EM]}function ci(e){var t=e[je];if(t)return t;for(var n=e.parentNode;n;){if(t=n[qi]||n[je]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=py(e);e!==null;){if(n=e[je])return n;e=py(e)}return t}e=n,n=e.parentNode}return null}function Hi(e){if(e=e[je]||e[qi]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function As(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(C(33))}function Si(e){var t=e[op];return t||(t=e[op]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Oe(e){e[rr]=!0}var Qy=new Set,jy={};function Da(e,t){Ri(e,t),Ri(e+"Capture",t)}function Ri(e,t){for(jy[e]=t,e=0;e<t.length;e++)Qy.add(t[e])}var wM=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),up={},cp={};function MM(e){return df.call(cp,e)?!0:df.call(up,e)?!1:wM.test(e)?cp[e]=!0:(up[e]=!0,!1)}function jl(e,t,n){if(MM(t))if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var a=t.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+n)}}function Ol(e,t,n){if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+n)}}function an(e,t,n,a){if(a===null)e.removeAttribute(n);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(n);return}e.setAttributeNS(t,n,""+a)}}var Rc,fp;function si(e){if(Rc===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Rc=t&&t[1]||"",fp=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Rc+e+fp}var _c=!1;function kc(e,t){if(!e||_c)return"";_c=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(t){var f=function(){throw Error()};if(Object.defineProperty(f.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(f,[])}catch(m){var d=m}Reflect.construct(e,[],f)}else{try{f.call()}catch(m){d=m}e.call(f.prototype)}}else{try{throw Error()}catch(m){d=m}(f=e())&&typeof f.catch=="function"&&f.catch(function(){})}}catch(m){if(m&&d&&typeof m.stack=="string")return[m.stack,d.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var i=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");i&&i.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var s=a.DetermineComponentFrameRoot(),r=s[0],l=s[1];if(r&&l){var o=r.split(`
`),u=l.split(`
`);for(i=a=0;a<o.length&&!o[a].includes("DetermineComponentFrameRoot");)a++;for(;i<u.length&&!u[i].includes("DetermineComponentFrameRoot");)i++;if(a===o.length||i===u.length)for(a=o.length-1,i=u.length-1;1<=a&&0<=i&&o[a]!==u[i];)i--;for(;1<=a&&0<=i;a--,i--)if(o[a]!==u[i]){if(a!==1||i!==1)do if(a--,i--,0>i||o[a]!==u[i]){var c=`
`+o[a].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}while(1<=a&&0<=i);break}}}finally{_c=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?si(n):""}function CM(e){switch(e.tag){case 26:case 27:case 5:return si(e.type);case 16:return si("Lazy");case 13:return si("Suspense");case 19:return si("SuspenseList");case 0:case 15:return kc(e.type,!1);case 11:return kc(e.type.render,!1);case 1:return kc(e.type,!0);case 31:return si("Activity");default:return""}}function dp(e){try{var t="";do t+=CM(e),e=e.return;while(e);return t}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function Tt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Vy(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function AM(e){var t=Vy(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),a=""+e[t];if(!e.hasOwnProperty(t)&&typeof n!="undefined"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,s=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(r){a=""+r,s.call(this,r)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return a},setValue:function(r){a=""+r},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function so(e){e._valueTracker||(e._valueTracker=AM(e))}function Fy(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),a="";return e&&(a=Vy(e)?e.checked?"true":"false":e.value),e=a,e!==n?(t.setValue(e),!0):!1}function ro(e){if(e=e||(typeof document!="undefined"?document:void 0),typeof e=="undefined")return null;try{return e.activeElement||e.body}catch(t){return e.body}}var xM=/[\n"\\]/g;function Mt(e){return e.replace(xM,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function mf(e,t,n,a,i,s,r,l){e.name="",r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"?e.type=r:e.removeAttribute("type"),t!=null?r==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+Tt(t)):e.value!==""+Tt(t)&&(e.value=""+Tt(t)):r!=="submit"&&r!=="reset"||e.removeAttribute("value"),t!=null?gf(e,r,Tt(t)):n!=null?gf(e,r,Tt(n)):a!=null&&e.removeAttribute("value"),i==null&&s!=null&&(e.defaultChecked=!!s),i!=null&&(e.checked=i&&typeof i!="function"&&typeof i!="symbol"),l!=null&&typeof l!="function"&&typeof l!="symbol"&&typeof l!="boolean"?e.name=""+Tt(l):e.removeAttribute("name")}function Gy(e,t,n,a,i,s,r,l){if(s!=null&&typeof s!="function"&&typeof s!="symbol"&&typeof s!="boolean"&&(e.type=s),t!=null||n!=null){if(!(s!=="submit"&&s!=="reset"||t!=null))return;n=n!=null?""+Tt(n):"",t=t!=null?""+Tt(t):n,l||t===e.value||(e.value=t),e.defaultValue=t}a=a!=null?a:i,a=typeof a!="function"&&typeof a!="symbol"&&!!a,e.checked=l?e.checked:!!a,e.defaultChecked=!!a,r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"&&(e.name=r)}function gf(e,t,n){t==="number"&&ro(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function Ti(e,t,n,a){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&a&&(e[n].defaultSelected=!0)}else{for(n=""+Tt(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,a&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function $y(e,t,n){if(t!=null&&(t=""+Tt(t),t!==e.value&&(e.value=t),n==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=n!=null?""+Tt(n):""}function Yy(e,t,n,a){if(t==null){if(a!=null){if(n!=null)throw Error(C(92));if(Cs(a)){if(1<a.length)throw Error(C(93));a=a[0]}n=a}n==null&&(n=""),t=n}n=Tt(t),e.defaultValue=n,a=e.textContent,a===n&&a!==""&&a!==null&&(e.value=a)}function _i(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var OM=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function hp(e,t,n){var a=t.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?a?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":a?e.setProperty(t,n):typeof n!="number"||n===0||OM.has(t)?t==="float"?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function Xy(e,t,n){if(t!=null&&typeof t!="object")throw Error(C(62));if(e=e.style,n!=null){for(var a in n)!n.hasOwnProperty(a)||t!=null&&t.hasOwnProperty(a)||(a.indexOf("--")===0?e.setProperty(a,""):a==="float"?e.cssFloat="":e[a]="");for(var i in t)a=t[i],t.hasOwnProperty(i)&&n[i]!==a&&hp(e,i,a)}else for(var s in t)t.hasOwnProperty(s)&&hp(e,s,t[s])}function rd(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var RM=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),_M=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Vl(e){return _M.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var pf=null;function ld(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var fi=null,Ei=null;function mp(e){var t=Hi(e);if(t&&(e=t.stateNode)){var n=e[at]||null;e:switch(e=t.stateNode,t.type){case"input":if(mf(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+Mt(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var a=n[t];if(a!==e&&a.form===e.form){var i=a[at]||null;if(!i)throw Error(C(90));mf(a,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name)}}for(t=0;t<n.length;t++)a=n[t],a.form===e.form&&Fy(a)}break e;case"textarea":$y(e,n.value,n.defaultValue);break e;case"select":t=n.value,t!=null&&Ti(e,!!n.multiple,t,!1)}}}var Uc=!1;function Iy(e,t,n){if(Uc)return e(t,n);Uc=!0;try{var a=e(t);return a}finally{if(Uc=!1,(fi!==null||Ei!==null)&&(jo(),fi&&(t=fi,e=Ei,Ei=fi=null,mp(t),e)))for(t=0;t<e.length;t++)mp(e[t])}}function Vs(e,t){var n=e.stateNode;if(n===null)return null;var a=n[at]||null;if(a===null)return null;n=a[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(e=e.type,a=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!a;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(C(231,t,typeof n));return n}var gn=!(typeof window=="undefined"||typeof window.document=="undefined"||typeof window.document.createElement=="undefined"),yf=!1;if(gn)try{ni={},Object.defineProperty(ni,"passive",{get:function(){yf=!0}}),window.addEventListener("test",ni,ni),window.removeEventListener("test",ni,ni)}catch(e){yf=!1}var ni,Pn=null,od=null,Fl=null;function Zy(){if(Fl)return Fl;var e,t=od,n=t.length,a,i="value"in Pn?Pn.value:Pn.textContent,s=i.length;for(e=0;e<n&&t[e]===i[e];e++);var r=n-e;for(a=1;a<=r&&t[n-a]===i[s-a];a++);return Fl=i.slice(e,1<a?1-a:void 0)}function Gl(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Rl(){return!0}function gp(){return!1}function it(e){function t(n,a,i,s,r){this._reactName=n,this._targetInst=i,this.type=a,this.nativeEvent=s,this.target=r,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(s):s[l]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?Rl:gp,this.isPropagationStopped=gp,this}return le(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Rl)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Rl)},persist:function(){},isPersistent:Rl}),t}var Ba={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},No=it(Ba),lr=le({},Ba,{view:0,detail:0}),kM=it(lr),Nc,Dc,ys,Do=le({},lr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:ud,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ys&&(ys&&e.type==="mousemove"?(Nc=e.screenX-ys.screenX,Dc=e.screenY-ys.screenY):Dc=Nc=0,ys=e),Nc)},movementY:function(e){return"movementY"in e?e.movementY:Dc}}),pp=it(Do),UM=le({},Do,{dataTransfer:0}),NM=it(UM),DM=le({},lr,{relatedTarget:0}),Bc=it(DM),BM=le({},Ba,{animationName:0,elapsedTime:0,pseudoElement:0}),PM=it(BM),LM=le({},Ba,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),zM=it(LM),qM=le({},Ba,{data:0}),yp=it(qM),HM={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},QM={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},jM={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function VM(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=jM[e])?!!t[e]:!1}function ud(){return VM}var FM=le({},lr,{key:function(e){if(e.key){var t=HM[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Gl(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?QM[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:ud,charCode:function(e){return e.type==="keypress"?Gl(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Gl(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),GM=it(FM),$M=le({},Do,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),bp=it($M),YM=le({},lr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:ud}),XM=it(YM),IM=le({},Ba,{propertyName:0,elapsedTime:0,pseudoElement:0}),ZM=it(IM),KM=le({},Do,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),WM=it(KM),JM=le({},Ba,{newState:0,oldState:0}),eC=it(JM),tC=[9,13,27,32],cd=gn&&"CompositionEvent"in window,Os=null;gn&&"documentMode"in document&&(Os=document.documentMode);var nC=gn&&"TextEvent"in window&&!Os,Ky=gn&&(!cd||Os&&8<Os&&11>=Os),vp=String.fromCharCode(32),Sp=!1;function Wy(e,t){switch(e){case"keyup":return tC.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Jy(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var di=!1;function aC(e,t){switch(e){case"compositionend":return Jy(t);case"keypress":return t.which!==32?null:(Sp=!0,vp);case"textInput":return e=t.data,e===vp&&Sp?null:e;default:return null}}function iC(e,t){if(di)return e==="compositionend"||!cd&&Wy(e,t)?(e=Zy(),Fl=od=Pn=null,di=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Ky&&t.locale!=="ko"?null:t.data;default:return null}}var sC={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Tp(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!sC[e.type]:t==="textarea"}function e0(e,t,n,a){fi?Ei?Ei.push(a):Ei=[a]:fi=a,t=Co(t,"onChange"),0<t.length&&(n=new No("onChange","change",null,n,a),e.push({event:n,listeners:t}))}var Rs=null,Fs=null;function rC(e){$b(e,0)}function Bo(e){var t=As(e);if(Fy(t))return e}function Ep(e,t){if(e==="change")return t}var t0=!1;gn&&(gn?(kl="oninput"in document,kl||(Pc=document.createElement("div"),Pc.setAttribute("oninput","return;"),kl=typeof Pc.oninput=="function"),_l=kl):_l=!1,t0=_l&&(!document.documentMode||9<document.documentMode));var _l,kl,Pc;function wp(){Rs&&(Rs.detachEvent("onpropertychange",n0),Fs=Rs=null)}function n0(e){if(e.propertyName==="value"&&Bo(Fs)){var t=[];e0(t,Fs,e,ld(e)),Iy(rC,t)}}function lC(e,t,n){e==="focusin"?(wp(),Rs=t,Fs=n,Rs.attachEvent("onpropertychange",n0)):e==="focusout"&&wp()}function oC(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Bo(Fs)}function uC(e,t){if(e==="click")return Bo(t)}function cC(e,t){if(e==="input"||e==="change")return Bo(t)}function fC(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var yt=typeof Object.is=="function"?Object.is:fC;function Gs(e,t){if(yt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),a=Object.keys(t);if(n.length!==a.length)return!1;for(a=0;a<n.length;a++){var i=n[a];if(!df.call(t,i)||!yt(e[i],t[i]))return!1}return!0}function Mp(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Cp(e,t){var n=Mp(e);e=0;for(var a;n;){if(n.nodeType===3){if(a=e+n.textContent.length,e<=t&&a>=t)return{node:n,offset:t-e};e=a}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Mp(n)}}function a0(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?a0(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function i0(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=ro(e.document);t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch(a){n=!1}if(n)e=t.contentWindow;else break;t=ro(e.document)}return t}function fd(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var dC=gn&&"documentMode"in document&&11>=document.documentMode,hi=null,bf=null,_s=null,vf=!1;function Ap(e,t,n){var a=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;vf||hi==null||hi!==ro(a)||(a=hi,"selectionStart"in a&&fd(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),_s&&Gs(_s,a)||(_s=a,a=Co(bf,"onSelect"),0<a.length&&(t=new No("onSelect","select",null,t,n),e.push({event:t,listeners:a}),t.target=hi)))}function va(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var mi={animationend:va("Animation","AnimationEnd"),animationiteration:va("Animation","AnimationIteration"),animationstart:va("Animation","AnimationStart"),transitionrun:va("Transition","TransitionRun"),transitionstart:va("Transition","TransitionStart"),transitioncancel:va("Transition","TransitionCancel"),transitionend:va("Transition","TransitionEnd")},Lc={},s0={};gn&&(s0=document.createElement("div").style,"AnimationEvent"in window||(delete mi.animationend.animation,delete mi.animationiteration.animation,delete mi.animationstart.animation),"TransitionEvent"in window||delete mi.transitionend.transition);function Pa(e){if(Lc[e])return Lc[e];if(!mi[e])return e;var t=mi[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in s0)return Lc[e]=t[n];return e}var r0=Pa("animationend"),l0=Pa("animationiteration"),o0=Pa("animationstart"),hC=Pa("transitionrun"),mC=Pa("transitionstart"),gC=Pa("transitioncancel"),u0=Pa("transitionend"),c0=new Map,Sf="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Sf.push("scrollEnd");function Nt(e,t){c0.set(e,t),Da(t,[e])}var xp=new WeakMap;function Ct(e,t){if(typeof e=="object"&&e!==null){var n=xp.get(e);return n!==void 0?n:(t={value:e,source:t,stack:dp(t)},xp.set(e,t),t)}return{value:e,source:t,stack:dp(t)}}var St=[],gi=0,dd=0;function Po(){for(var e=gi,t=dd=gi=0;t<e;){var n=St[t];St[t++]=null;var a=St[t];St[t++]=null;var i=St[t];St[t++]=null;var s=St[t];if(St[t++]=null,a!==null&&i!==null){var r=a.pending;r===null?i.next=i:(i.next=r.next,r.next=i),a.pending=i}s!==0&&f0(n,i,s)}}function Lo(e,t,n,a){St[gi++]=e,St[gi++]=t,St[gi++]=n,St[gi++]=a,dd|=a,e.lanes|=a,e=e.alternate,e!==null&&(e.lanes|=a)}function hd(e,t,n,a){return Lo(e,t,n,a),lo(e)}function Qi(e,t){return Lo(e,null,null,t),lo(e)}function f0(e,t,n){e.lanes|=n;var a=e.alternate;a!==null&&(a.lanes|=n);for(var i=!1,s=e.return;s!==null;)s.childLanes|=n,a=s.alternate,a!==null&&(a.childLanes|=n),s.tag===22&&(e=s.stateNode,e===null||e._visibility&1||(i=!0)),e=s,s=s.return;return e.tag===3?(s=e.stateNode,i&&t!==null&&(i=31-mt(n),e=s.hiddenUpdates,a=e[i],a===null?e[i]=[t]:a.push(t),t.lane=n|536870912),s):null}function lo(e){if(50<Hs)throw Hs=0,Qf=null,Error(C(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var pi={};function pC(e,t,n,a){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function dt(e,t,n,a){return new pC(e,t,n,a)}function md(e){return e=e.prototype,!(!e||!e.isReactComponent)}function hn(e,t){var n=e.alternate;return n===null?(n=dt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&65011712,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function d0(e,t){e.flags&=65011714;var n=e.alternate;return n===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function $l(e,t,n,a,i,s){var r=0;if(a=e,typeof e=="function")md(e)&&(r=1);else if(typeof e=="string")r=p3(e,n,Ft.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case of:return e=dt(31,n,t,i),e.elementType=of,e.lanes=s,e;case oi:return Ma(n.children,i,s,t);case ky:r=8,i|=24;break;case sf:return e=dt(12,n,t,i|2),e.elementType=sf,e.lanes=s,e;case rf:return e=dt(13,n,t,i),e.elementType=rf,e.lanes=s,e;case lf:return e=dt(19,n,t,i),e.elementType=lf,e.lanes=s,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case rM:case on:r=10;break e;case Uy:r=9;break e;case ed:r=11;break e;case td:r=14;break e;case Rn:r=16,a=null;break e}r=29,n=Error(C(130,e===null?"null":typeof e,"")),a=null}return t=dt(r,n,t,i),t.elementType=e,t.type=a,t.lanes=s,t}function Ma(e,t,n,a){return e=dt(7,e,a,t),e.lanes=n,e}function zc(e,t,n){return e=dt(6,e,null,t),e.lanes=n,e}function qc(e,t,n){return t=dt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var yi=[],bi=0,oo=null,uo=0,Et=[],wt=0,Ca=null,un=1,cn="";function Ta(e,t){yi[bi++]=uo,yi[bi++]=oo,oo=e,uo=t}function h0(e,t,n){Et[wt++]=un,Et[wt++]=cn,Et[wt++]=Ca,Ca=e;var a=un;e=cn;var i=32-mt(a)-1;a&=~(1<<i),n+=1;var s=32-mt(t)+i;if(30<s){var r=i-i%5;s=(a&(1<<r)-1).toString(32),a>>=r,i-=r,un=1<<32-mt(t)+i|n<<i|a,cn=s+e}else un=1<<s|n<<i|a,cn=e}function gd(e){e.return!==null&&(Ta(e,1),h0(e,1,0))}function pd(e){for(;e===oo;)oo=yi[--bi],yi[bi]=null,uo=yi[--bi],yi[bi]=null;for(;e===Ca;)Ca=Et[--wt],Et[wt]=null,cn=Et[--wt],Et[wt]=null,un=Et[--wt],Et[wt]=null}var Xe=null,me=null,K=!1,Aa=null,jt=!1,Tf=Error(C(519));function _a(e){var t=Error(C(418,""));throw $s(Ct(t,e)),Tf}function Op(e){var t=e.stateNode,n=e.type,a=e.memoizedProps;switch(t[je]=e,t[at]=a,n){case"dialog":V("cancel",t),V("close",t);break;case"iframe":case"object":case"embed":V("load",t);break;case"video":case"audio":for(n=0;n<Is.length;n++)V(Is[n],t);break;case"source":V("error",t);break;case"img":case"image":case"link":V("error",t),V("load",t);break;case"details":V("toggle",t);break;case"input":V("invalid",t),Gy(t,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),so(t);break;case"select":V("invalid",t);break;case"textarea":V("invalid",t),Yy(t,a.value,a.defaultValue,a.children),so(t)}n=a.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||t.textContent===""+n||a.suppressHydrationWarning===!0||Xb(t.textContent,n)?(a.popover!=null&&(V("beforetoggle",t),V("toggle",t)),a.onScroll!=null&&V("scroll",t),a.onScrollEnd!=null&&V("scrollend",t),a.onClick!=null&&(t.onclick=Go),t=!0):t=!1,t||_a(e)}function Rp(e){for(Xe=e.return;Xe;)switch(Xe.tag){case 5:case 13:jt=!1;return;case 27:case 3:jt=!0;return;default:Xe=Xe.return}}function bs(e){if(e!==Xe)return!1;if(!K)return Rp(e),K=!0,!1;var t=e.tag,n;if((n=t!==3&&t!==27)&&((n=t===5)&&(n=e.type,n=!(n!=="form"&&n!=="button")||Yf(e.type,e.memoizedProps)),n=!n),n&&me&&_a(e),Rp(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(C(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(n=e.data,n==="/$"){if(t===0){me=Ut(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++;e=e.nextSibling}me=null}}else t===27?(t=me,Kn(e.type)?(e=Zf,Zf=null,me=e):me=t):me=Xe?Ut(e.stateNode.nextSibling):null;return!0}function or(){me=Xe=null,K=!1}function _p(){var e=Aa;return e!==null&&(nt===null?nt=e:nt.push.apply(nt,e),Aa=null),e}function $s(e){Aa===null?Aa=[e]:Aa.push(e)}var Ef=Yt(null),La=null,fn=null;function kn(e,t,n){fe(Ef,t._currentValue),t._currentValue=n}function mn(e){e._currentValue=Ef.current,_e(Ef)}function wf(e,t,n){for(;e!==null;){var a=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,a!==null&&(a.childLanes|=t)):a!==null&&(a.childLanes&t)!==t&&(a.childLanes|=t),e===n)break;e=e.return}}function Mf(e,t,n,a){var i=e.child;for(i!==null&&(i.return=e);i!==null;){var s=i.dependencies;if(s!==null){var r=i.child;s=s.firstContext;e:for(;s!==null;){var l=s;s=i;for(var o=0;o<t.length;o++)if(l.context===t[o]){s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),wf(s.return,n,e),a||(r=null);break e}s=l.next}}else if(i.tag===18){if(r=i.return,r===null)throw Error(C(341));r.lanes|=n,s=r.alternate,s!==null&&(s.lanes|=n),wf(r,n,e),r=null}else r=i.child;if(r!==null)r.return=i;else for(r=i;r!==null;){if(r===e){r=null;break}if(i=r.sibling,i!==null){i.return=r.return,r=i;break}r=r.return}i=r}}function ur(e,t,n,a){e=null;for(var i=t,s=!1;i!==null;){if(!s){if(i.flags&524288)s=!0;else if(i.flags&262144)break}if(i.tag===10){var r=i.alternate;if(r===null)throw Error(C(387));if(r=r.memoizedProps,r!==null){var l=i.type;yt(i.pendingProps.value,r.value)||(e!==null?e.push(l):e=[l])}}else if(i===to.current){if(r=i.alternate,r===null)throw Error(C(387));r.memoizedState.memoizedState!==i.memoizedState.memoizedState&&(e!==null?e.push(Ws):e=[Ws])}i=i.return}e!==null&&Mf(t,e,n,a),t.flags|=262144}function co(e){for(e=e.firstContext;e!==null;){if(!yt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function ka(e){La=e,fn=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function Ve(e){return m0(La,e)}function Ul(e,t){return La===null&&ka(e),m0(e,t)}function m0(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},fn===null){if(e===null)throw Error(C(308));fn=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else fn=fn.next=t;return n}var yC=typeof AbortController!="undefined"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(n,a){e.push(a)}};this.abort=function(){t.aborted=!0,e.forEach(function(n){return n()})}},bC=Me.unstable_scheduleCallback,vC=Me.unstable_NormalPriority,Ee={$$typeof:on,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function yd(){return{controller:new yC,data:new Map,refCount:0}}function cr(e){e.refCount--,e.refCount===0&&bC(vC,function(){e.controller.abort()})}var ks=null,Cf=0,ki=0,wi=null;function SC(e,t){if(ks===null){var n=ks=[];Cf=0,ki=Hd(),wi={status:"pending",value:void 0,then:function(a){n.push(a)}}}return Cf++,t.then(kp,kp),t}function kp(){if(--Cf===0&&ks!==null){wi!==null&&(wi.status="fulfilled");var e=ks;ks=null,ki=0,wi=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function TC(e,t){var n=[],a={status:"pending",value:null,reason:null,then:function(i){n.push(i)}};return e.then(function(){a.status="fulfilled",a.value=t;for(var i=0;i<n.length;i++)(0,n[i])(t)},function(i){for(a.status="rejected",a.reason=i,i=0;i<n.length;i++)(0,n[i])(void 0)}),a}var Up=N.S;N.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&SC(e,t),Up!==null&&Up(e,t)};var xa=Yt(null);function bd(){var e=xa.current;return e!==null?e:re.pooledCache}function Yl(e,t){t===null?fe(xa,xa.current):fe(xa,t.pool)}function g0(){var e=bd();return e===null?null:{parent:Ee._currentValue,pool:e}}var fr=Error(C(460)),p0=Error(C(474)),zo=Error(C(542)),Af={then:function(){}};function Np(e){return e=e.status,e==="fulfilled"||e==="rejected"}function Nl(){}function y0(e,t,n){switch(n=e[n],n===void 0?e.push(t):n!==t&&(t.then(Nl,Nl),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Bp(e),e;default:if(typeof t.status=="string")t.then(Nl,Nl);else{if(e=re,e!==null&&100<e.shellSuspendCounter)throw Error(C(482));e=t,e.status="pending",e.then(function(a){if(t.status==="pending"){var i=t;i.status="fulfilled",i.value=a}},function(a){if(t.status==="pending"){var i=t;i.status="rejected",i.reason=a}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Bp(e),e}throw Us=t,fr}}var Us=null;function Dp(){if(Us===null)throw Error(C(459));var e=Us;return Us=null,e}function Bp(e){if(e===fr||e===zo)throw Error(C(483))}var _n=!1;function vd(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function xf(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Hn(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Qn(e,t,n){var a=e.updateQueue;if(a===null)return null;if(a=a.shared,ne&2){var i=a.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),a.pending=t,t=lo(e),f0(e,null,n),t}return Lo(e,a,t,n),lo(e)}function Ns(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194048)!==0)){var a=t.lanes;a&=e.pendingLanes,n|=a,t.lanes=n,qy(e,n)}}function Hc(e,t){var n=e.updateQueue,a=e.alternate;if(a!==null&&(a=a.updateQueue,n===a)){var i=null,s=null;if(n=n.firstBaseUpdate,n!==null){do{var r={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};s===null?i=s=r:s=s.next=r,n=n.next}while(n!==null);s===null?i=s=t:s=s.next=t}else i=s=t;n={baseState:a.baseState,firstBaseUpdate:i,lastBaseUpdate:s,shared:a.shared,callbacks:a.callbacks},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var Of=!1;function Ds(){if(Of){var e=wi;if(e!==null)throw e}}function Bs(e,t,n,a){Of=!1;var i=e.updateQueue;_n=!1;var s=i.firstBaseUpdate,r=i.lastBaseUpdate,l=i.shared.pending;if(l!==null){i.shared.pending=null;var o=l,u=o.next;o.next=null,r===null?s=u:r.next=u,r=o;var c=e.alternate;c!==null&&(c=c.updateQueue,l=c.lastBaseUpdate,l!==r&&(l===null?c.firstBaseUpdate=u:l.next=u,c.lastBaseUpdate=o))}if(s!==null){var f=i.baseState;r=0,c=u=o=null,l=s;do{var d=l.lane&-536870913,m=d!==l.lane;if(m?(X&d)===d:(a&d)===d){d!==0&&d===ki&&(Of=!0),c!==null&&(c=c.next={lane:0,tag:l.tag,payload:l.payload,callback:null,next:null});e:{var y=e,E=l;d=t;var M=n;switch(E.tag){case 1:if(y=E.payload,typeof y=="function"){f=y.call(M,f,d);break e}f=y;break e;case 3:y.flags=y.flags&-65537|128;case 0:if(y=E.payload,d=typeof y=="function"?y.call(M,f,d):y,d==null)break e;f=le({},f,d);break e;case 2:_n=!0}}d=l.callback,d!==null&&(e.flags|=64,m&&(e.flags|=8192),m=i.callbacks,m===null?i.callbacks=[d]:m.push(d))}else m={lane:d,tag:l.tag,payload:l.payload,callback:l.callback,next:null},c===null?(u=c=m,o=f):c=c.next=m,r|=d;if(l=l.next,l===null){if(l=i.shared.pending,l===null)break;m=l,l=m.next,m.next=null,i.lastBaseUpdate=m,i.shared.pending=null}}while(1);c===null&&(o=f),i.baseState=o,i.firstBaseUpdate=u,i.lastBaseUpdate=c,s===null&&(i.shared.lanes=0),In|=r,e.lanes=r,e.memoizedState=f}}function b0(e,t){if(typeof e!="function")throw Error(C(191,e));e.call(t)}function v0(e,t){var n=e.callbacks;if(n!==null)for(e.callbacks=null,e=0;e<n.length;e++)b0(n[e],t)}var Ui=Yt(null),fo=Yt(0);function Pp(e,t){e=bn,fe(fo,e),fe(Ui,t),bn=e|t.baseLanes}function Rf(){fe(fo,bn),fe(Ui,Ui.current)}function Sd(){bn=fo.current,_e(Ui),_e(fo)}var Yn=0,Q=null,ie=null,Se=null,ho=!1,Mi=!1,Ua=!1,mo=0,Ys=0,Ci=null,EC=0;function pe(){throw Error(C(321))}function Td(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!yt(e[n],t[n]))return!1;return!0}function Ed(e,t,n,a,i,s){return Yn=s,Q=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,N.H=e===null||e.memoizedState===null?Z0:K0,Ua=!1,s=n(a,i),Ua=!1,Mi&&(s=T0(t,n,a,i)),S0(e),s}function S0(e){N.H=go;var t=ie!==null&&ie.next!==null;if(Yn=0,Se=ie=Q=null,ho=!1,Ys=0,Ci=null,t)throw Error(C(300));e===null||Re||(e=e.dependencies,e!==null&&co(e)&&(Re=!0))}function T0(e,t,n,a){Q=e;var i=0;do{if(Mi&&(Ci=null),Ys=0,Mi=!1,25<=i)throw Error(C(301));if(i+=1,Se=ie=null,e.updateQueue!=null){var s=e.updateQueue;s.lastEffect=null,s.events=null,s.stores=null,s.memoCache!=null&&(s.memoCache.index=0)}N.H=RC,s=t(n,a)}while(Mi);return s}function wC(){var e=N.H,t=e.useState()[0];return t=typeof t.then=="function"?dr(t):t,e=e.useState()[0],(ie!==null?ie.memoizedState:null)!==e&&(Q.flags|=1024),t}function wd(){var e=mo!==0;return mo=0,e}function Md(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function Cd(e){if(ho){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}ho=!1}Yn=0,Se=ie=Q=null,Mi=!1,Ys=mo=0,Ci=null}function et(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Se===null?Q.memoizedState=Se=e:Se=Se.next=e,Se}function Te(){if(ie===null){var e=Q.alternate;e=e!==null?e.memoizedState:null}else e=ie.next;var t=Se===null?Q.memoizedState:Se.next;if(t!==null)Se=t,ie=e;else{if(e===null)throw Q.alternate===null?Error(C(467)):Error(C(310));ie=e,e={memoizedState:ie.memoizedState,baseState:ie.baseState,baseQueue:ie.baseQueue,queue:ie.queue,next:null},Se===null?Q.memoizedState=Se=e:Se=Se.next=e}return Se}function Ad(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function dr(e){var t=Ys;return Ys+=1,Ci===null&&(Ci=[]),e=y0(Ci,e,t),t=Q,(Se===null?t.memoizedState:Se.next)===null&&(t=t.alternate,N.H=t===null||t.memoizedState===null?Z0:K0),e}function qo(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return dr(e);if(e.$$typeof===on)return Ve(e)}throw Error(C(438,String(e)))}function xd(e){var t=null,n=Q.updateQueue;if(n!==null&&(t=n.memoCache),t==null){var a=Q.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(t={data:a.data.map(function(i){return i.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),n===null&&(n=Ad(),Q.updateQueue=n),n.memoCache=t,n=t.data[t.index],n===void 0)for(n=t.data[t.index]=Array(e),a=0;a<e;a++)n[a]=lM;return t.index++,n}function pn(e,t){return typeof t=="function"?t(e):t}function Xl(e){var t=Te();return Od(t,ie,e)}function Od(e,t,n){var a=e.queue;if(a===null)throw Error(C(311));a.lastRenderedReducer=n;var i=e.baseQueue,s=a.pending;if(s!==null){if(i!==null){var r=i.next;i.next=s.next,s.next=r}t.baseQueue=i=s,a.pending=null}if(s=e.baseState,i===null)e.memoizedState=s;else{t=i.next;var l=r=null,o=null,u=t,c=!1;do{var f=u.lane&-536870913;if(f!==u.lane?(X&f)===f:(Yn&f)===f){var d=u.revertLane;if(d===0)o!==null&&(o=o.next={lane:0,revertLane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),f===ki&&(c=!0);else if((Yn&d)===d){u=u.next,d===ki&&(c=!0);continue}else f={lane:0,revertLane:u.revertLane,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null},o===null?(l=o=f,r=s):o=o.next=f,Q.lanes|=d,In|=d;f=u.action,Ua&&n(s,f),s=u.hasEagerState?u.eagerState:n(s,f)}else d={lane:f,revertLane:u.revertLane,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null},o===null?(l=o=d,r=s):o=o.next=d,Q.lanes|=f,In|=f;u=u.next}while(u!==null&&u!==t);if(o===null?r=s:o.next=l,!yt(s,e.memoizedState)&&(Re=!0,c&&(n=wi,n!==null)))throw n;e.memoizedState=s,e.baseState=r,e.baseQueue=o,a.lastRenderedState=s}return i===null&&(a.lanes=0),[e.memoizedState,a.dispatch]}function Qc(e){var t=Te(),n=t.queue;if(n===null)throw Error(C(311));n.lastRenderedReducer=e;var a=n.dispatch,i=n.pending,s=t.memoizedState;if(i!==null){n.pending=null;var r=i=i.next;do s=e(s,r.action),r=r.next;while(r!==i);yt(s,t.memoizedState)||(Re=!0),t.memoizedState=s,t.baseQueue===null&&(t.baseState=s),n.lastRenderedState=s}return[s,a]}function E0(e,t,n){var a=Q,i=Te(),s=K;if(s){if(n===void 0)throw Error(C(407));n=n()}else n=t();var r=!yt((ie||i).memoizedState,n);r&&(i.memoizedState=n,Re=!0),i=i.queue;var l=C0.bind(null,a,i,e);if(hr(2048,8,l,[e]),i.getSnapshot!==t||r||Se!==null&&Se.memoizedState.tag&1){if(a.flags|=2048,Ni(9,Ho(),M0.bind(null,a,i,n,t),null),re===null)throw Error(C(349));s||Yn&124||w0(a,t,n)}return n}function w0(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Q.updateQueue,t===null?(t=Ad(),Q.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function M0(e,t,n,a){t.value=n,t.getSnapshot=a,A0(t)&&x0(e)}function C0(e,t,n){return n(function(){A0(t)&&x0(e)})}function A0(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!yt(e,n)}catch(a){return!0}}function x0(e){var t=Qi(e,2);t!==null&&pt(t,e,2)}function _f(e){var t=et();if(typeof e=="function"){var n=e;if(e=n(),Ua){Bn(!0);try{n()}finally{Bn(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:pn,lastRenderedState:e},t}function O0(e,t,n,a){return e.baseState=n,Od(e,ie,typeof a=="function"?a:pn)}function MC(e,t,n,a,i){if(Qo(e))throw Error(C(485));if(e=t.action,e!==null){var s={payload:i,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(r){s.listeners.push(r)}};N.T!==null?n(!0):s.isTransition=!1,a(s),n=t.pending,n===null?(s.next=t.pending=s,R0(t,s)):(s.next=n.next,t.pending=n.next=s)}}function R0(e,t){var n=t.action,a=t.payload,i=e.state;if(t.isTransition){var s=N.T,r={};N.T=r;try{var l=n(i,a),o=N.S;o!==null&&o(r,l),Lp(e,t,l)}catch(u){kf(e,t,u)}finally{N.T=s}}else try{s=n(i,a),Lp(e,t,s)}catch(u){kf(e,t,u)}}function Lp(e,t,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(a){zp(e,t,a)},function(a){return kf(e,t,a)}):zp(e,t,n)}function zp(e,t,n){t.status="fulfilled",t.value=n,_0(t),e.state=n,t=e.pending,t!==null&&(n=t.next,n===t?e.pending=null:(n=n.next,t.next=n,R0(e,n)))}function kf(e,t,n){var a=e.pending;if(e.pending=null,a!==null){a=a.next;do t.status="rejected",t.reason=n,_0(t),t=t.next;while(t!==a)}e.action=null}function _0(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function k0(e,t){return t}function qp(e,t){if(K){var n=re.formState;if(n!==null){e:{var a=Q;if(K){if(me){t:{for(var i=me,s=jt;i.nodeType!==8;){if(!s){i=null;break t}if(i=Ut(i.nextSibling),i===null){i=null;break t}}s=i.data,i=s==="F!"||s==="F"?i:null}if(i){me=Ut(i.nextSibling),a=i.data==="F!";break e}}_a(a)}a=!1}a&&(t=n[0])}}return n=et(),n.memoizedState=n.baseState=t,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:k0,lastRenderedState:t},n.queue=a,n=Y0.bind(null,Q,a),a.dispatch=n,a=_f(!1),s=Ud.bind(null,Q,!1,a.queue),a=et(),i={state:t,dispatch:null,action:e,pending:null},a.queue=i,n=MC.bind(null,Q,i,s,n),i.dispatch=n,a.memoizedState=e,[t,n,!1]}function Hp(e){var t=Te();return U0(t,ie,e)}function U0(e,t,n){if(t=Od(e,t,k0)[0],e=Xl(pn)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var a=dr(t)}catch(r){throw r===fr?zo:r}else a=t;t=Te();var i=t.queue,s=i.dispatch;return n!==t.memoizedState&&(Q.flags|=2048,Ni(9,Ho(),CC.bind(null,i,n),null)),[a,s,e]}function CC(e,t){e.action=t}function Qp(e){var t=Te(),n=ie;if(n!==null)return U0(t,n,e);Te(),t=t.memoizedState,n=Te();var a=n.queue.dispatch;return n.memoizedState=e,[t,a,!1]}function Ni(e,t,n,a){return e={tag:e,create:n,deps:a,inst:t,next:null},t=Q.updateQueue,t===null&&(t=Ad(),Q.updateQueue=t),n=t.lastEffect,n===null?t.lastEffect=e.next=e:(a=n.next,n.next=e,e.next=a,t.lastEffect=e),e}function Ho(){return{destroy:void 0,resource:void 0}}function N0(){return Te().memoizedState}function Il(e,t,n,a){var i=et();a=a===void 0?null:a,Q.flags|=e,i.memoizedState=Ni(1|t,Ho(),n,a)}function hr(e,t,n,a){var i=Te();a=a===void 0?null:a;var s=i.memoizedState.inst;ie!==null&&a!==null&&Td(a,ie.memoizedState.deps)?i.memoizedState=Ni(t,s,n,a):(Q.flags|=e,i.memoizedState=Ni(1|t,s,n,a))}function jp(e,t){Il(8390656,8,e,t)}function D0(e,t){hr(2048,8,e,t)}function B0(e,t){return hr(4,2,e,t)}function P0(e,t){return hr(4,4,e,t)}function L0(e,t){if(typeof t=="function"){e=e();var n=t(e);return function(){typeof n=="function"?n():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function z0(e,t,n){n=n!=null?n.concat([e]):null,hr(4,4,L0.bind(null,t,e),n)}function Rd(){}function q0(e,t){var n=Te();t=t===void 0?null:t;var a=n.memoizedState;return t!==null&&Td(t,a[1])?a[0]:(n.memoizedState=[e,t],e)}function H0(e,t){var n=Te();t=t===void 0?null:t;var a=n.memoizedState;if(t!==null&&Td(t,a[1]))return a[0];if(a=e(),Ua){Bn(!0);try{e()}finally{Bn(!1)}}return n.memoizedState=[a,t],a}function _d(e,t,n){return n===void 0||Yn&1073741824?e.memoizedState=t:(e.memoizedState=n,e=_b(),Q.lanes|=e,In|=e,n)}function Q0(e,t,n,a){return yt(n,t)?n:Ui.current!==null?(e=_d(e,n,a),yt(e,t)||(Re=!0),e):Yn&42?(e=_b(),Q.lanes|=e,In|=e,t):(Re=!0,e.memoizedState=n)}function j0(e,t,n,a,i){var s=W.p;W.p=s!==0&&8>s?s:8;var r=N.T,l={};N.T=l,Ud(e,!1,t,n);try{var o=i(),u=N.S;if(u!==null&&u(l,o),o!==null&&typeof o=="object"&&typeof o.then=="function"){var c=TC(o,a);Ps(e,t,c,gt(e))}else Ps(e,t,a,gt(e))}catch(f){Ps(e,t,{then:function(){},status:"rejected",reason:f},gt())}finally{W.p=s,N.T=r}}function AC(){}function Uf(e,t,n,a){if(e.tag!==5)throw Error(C(476));var i=V0(e).queue;j0(e,i,t,wa,n===null?AC:function(){return F0(e),n(a)})}function V0(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:wa,baseState:wa,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:pn,lastRenderedState:wa},next:null};var n={};return t.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:pn,lastRenderedState:n},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function F0(e){var t=V0(e).next.queue;Ps(e,t,{},gt())}function kd(){return Ve(Ws)}function G0(){return Te().memoizedState}function $0(){return Te().memoizedState}function xC(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var n=gt();e=Hn(n);var a=Qn(t,e,n);a!==null&&(pt(a,t,n),Ns(a,t,n)),t={cache:yd()},e.payload=t;return}t=t.return}}function OC(e,t,n){var a=gt();n={lane:a,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Qo(e)?X0(t,n):(n=hd(e,t,n,a),n!==null&&(pt(n,e,a),I0(n,t,a)))}function Y0(e,t,n){var a=gt();Ps(e,t,n,a)}function Ps(e,t,n,a){var i={lane:a,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Qo(e))X0(t,i);else{var s=e.alternate;if(e.lanes===0&&(s===null||s.lanes===0)&&(s=t.lastRenderedReducer,s!==null))try{var r=t.lastRenderedState,l=s(r,n);if(i.hasEagerState=!0,i.eagerState=l,yt(l,r))return Lo(e,t,i,0),re===null&&Po(),!1}catch(o){}finally{}if(n=hd(e,t,i,a),n!==null)return pt(n,e,a),I0(n,t,a),!0}return!1}function Ud(e,t,n,a){if(a={lane:2,revertLane:Hd(),action:a,hasEagerState:!1,eagerState:null,next:null},Qo(e)){if(t)throw Error(C(479))}else t=hd(e,n,a,2),t!==null&&pt(t,e,2)}function Qo(e){var t=e.alternate;return e===Q||t!==null&&t===Q}function X0(e,t){Mi=ho=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function I0(e,t,n){if(n&4194048){var a=t.lanes;a&=e.pendingLanes,n|=a,t.lanes=n,qy(e,n)}}var go={readContext:Ve,use:qo,useCallback:pe,useContext:pe,useEffect:pe,useImperativeHandle:pe,useLayoutEffect:pe,useInsertionEffect:pe,useMemo:pe,useReducer:pe,useRef:pe,useState:pe,useDebugValue:pe,useDeferredValue:pe,useTransition:pe,useSyncExternalStore:pe,useId:pe,useHostTransitionStatus:pe,useFormState:pe,useActionState:pe,useOptimistic:pe,useMemoCache:pe,useCacheRefresh:pe},Z0={readContext:Ve,use:qo,useCallback:function(e,t){return et().memoizedState=[e,t===void 0?null:t],e},useContext:Ve,useEffect:jp,useImperativeHandle:function(e,t,n){n=n!=null?n.concat([e]):null,Il(4194308,4,L0.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Il(4194308,4,e,t)},useInsertionEffect:function(e,t){Il(4,2,e,t)},useMemo:function(e,t){var n=et();t=t===void 0?null:t;var a=e();if(Ua){Bn(!0);try{e()}finally{Bn(!1)}}return n.memoizedState=[a,t],a},useReducer:function(e,t,n){var a=et();if(n!==void 0){var i=n(t);if(Ua){Bn(!0);try{n(t)}finally{Bn(!1)}}}else i=t;return a.memoizedState=a.baseState=i,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:i},a.queue=e,e=e.dispatch=OC.bind(null,Q,e),[a.memoizedState,e]},useRef:function(e){var t=et();return e={current:e},t.memoizedState=e},useState:function(e){e=_f(e);var t=e.queue,n=Y0.bind(null,Q,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:Rd,useDeferredValue:function(e,t){var n=et();return _d(n,e,t)},useTransition:function(){var e=_f(!1);return e=j0.bind(null,Q,e.queue,!0,!1),et().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var a=Q,i=et();if(K){if(n===void 0)throw Error(C(407));n=n()}else{if(n=t(),re===null)throw Error(C(349));X&124||w0(a,t,n)}i.memoizedState=n;var s={value:n,getSnapshot:t};return i.queue=s,jp(C0.bind(null,a,s,e),[e]),a.flags|=2048,Ni(9,Ho(),M0.bind(null,a,s,n,t),null),n},useId:function(){var e=et(),t=re.identifierPrefix;if(K){var n=cn,a=un;n=(a&~(1<<32-mt(a)-1)).toString(32)+n,t="\xAB"+t+"R"+n,n=mo++,0<n&&(t+="H"+n.toString(32)),t+="\xBB"}else n=EC++,t="\xAB"+t+"r"+n.toString(32)+"\xBB";return e.memoizedState=t},useHostTransitionStatus:kd,useFormState:qp,useActionState:qp,useOptimistic:function(e){var t=et();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=Ud.bind(null,Q,!0,n),n.dispatch=t,[e,t]},useMemoCache:xd,useCacheRefresh:function(){return et().memoizedState=xC.bind(null,Q)}},K0={readContext:Ve,use:qo,useCallback:q0,useContext:Ve,useEffect:D0,useImperativeHandle:z0,useInsertionEffect:B0,useLayoutEffect:P0,useMemo:H0,useReducer:Xl,useRef:N0,useState:function(){return Xl(pn)},useDebugValue:Rd,useDeferredValue:function(e,t){var n=Te();return Q0(n,ie.memoizedState,e,t)},useTransition:function(){var e=Xl(pn)[0],t=Te().memoizedState;return[typeof e=="boolean"?e:dr(e),t]},useSyncExternalStore:E0,useId:G0,useHostTransitionStatus:kd,useFormState:Hp,useActionState:Hp,useOptimistic:function(e,t){var n=Te();return O0(n,ie,e,t)},useMemoCache:xd,useCacheRefresh:$0},RC={readContext:Ve,use:qo,useCallback:q0,useContext:Ve,useEffect:D0,useImperativeHandle:z0,useInsertionEffect:B0,useLayoutEffect:P0,useMemo:H0,useReducer:Qc,useRef:N0,useState:function(){return Qc(pn)},useDebugValue:Rd,useDeferredValue:function(e,t){var n=Te();return ie===null?_d(n,e,t):Q0(n,ie.memoizedState,e,t)},useTransition:function(){var e=Qc(pn)[0],t=Te().memoizedState;return[typeof e=="boolean"?e:dr(e),t]},useSyncExternalStore:E0,useId:G0,useHostTransitionStatus:kd,useFormState:Qp,useActionState:Qp,useOptimistic:function(e,t){var n=Te();return ie!==null?O0(n,ie,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:xd,useCacheRefresh:$0},Ai=null,Xs=0;function Dl(e){var t=Xs;return Xs+=1,Ai===null&&(Ai=[]),y0(Ai,e,t)}function vs(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function Bl(e,t){throw t.$$typeof===sM?Error(C(525)):(e=Object.prototype.toString.call(t),Error(C(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function Vp(e){var t=e._init;return t(e._payload)}function W0(e){function t(g,h){if(e){var p=g.deletions;p===null?(g.deletions=[h],g.flags|=16):p.push(h)}}function n(g,h){if(!e)return null;for(;h!==null;)t(g,h),h=h.sibling;return null}function a(g){for(var h=new Map;g!==null;)g.key!==null?h.set(g.key,g):h.set(g.index,g),g=g.sibling;return h}function i(g,h){return g=hn(g,h),g.index=0,g.sibling=null,g}function s(g,h,p){return g.index=p,e?(p=g.alternate,p!==null?(p=p.index,p<h?(g.flags|=67108866,h):p):(g.flags|=67108866,h)):(g.flags|=1048576,h)}function r(g){return e&&g.alternate===null&&(g.flags|=67108866),g}function l(g,h,p,S){return h===null||h.tag!==6?(h=zc(p,g.mode,S),h.return=g,h):(h=i(h,p),h.return=g,h)}function o(g,h,p,S){var A=p.type;return A===oi?c(g,h,p.props.children,S,p.key):h!==null&&(h.elementType===A||typeof A=="object"&&A!==null&&A.$$typeof===Rn&&Vp(A)===h.type)?(h=i(h,p.props),vs(h,p),h.return=g,h):(h=$l(p.type,p.key,p.props,null,g.mode,S),vs(h,p),h.return=g,h)}function u(g,h,p,S){return h===null||h.tag!==4||h.stateNode.containerInfo!==p.containerInfo||h.stateNode.implementation!==p.implementation?(h=qc(p,g.mode,S),h.return=g,h):(h=i(h,p.children||[]),h.return=g,h)}function c(g,h,p,S,A){return h===null||h.tag!==7?(h=Ma(p,g.mode,S,A),h.return=g,h):(h=i(h,p),h.return=g,h)}function f(g,h,p){if(typeof h=="string"&&h!==""||typeof h=="number"||typeof h=="bigint")return h=zc(""+h,g.mode,p),h.return=g,h;if(typeof h=="object"&&h!==null){switch(h.$$typeof){case Cl:return p=$l(h.type,h.key,h.props,null,g.mode,p),vs(p,h),p.return=g,p;case Ms:return h=qc(h,g.mode,p),h.return=g,h;case Rn:var S=h._init;return h=S(h._payload),f(g,h,p)}if(Cs(h)||ps(h))return h=Ma(h,g.mode,p,null),h.return=g,h;if(typeof h.then=="function")return f(g,Dl(h),p);if(h.$$typeof===on)return f(g,Ul(g,h),p);Bl(g,h)}return null}function d(g,h,p,S){var A=h!==null?h.key:null;if(typeof p=="string"&&p!==""||typeof p=="number"||typeof p=="bigint")return A!==null?null:l(g,h,""+p,S);if(typeof p=="object"&&p!==null){switch(p.$$typeof){case Cl:return p.key===A?o(g,h,p,S):null;case Ms:return p.key===A?u(g,h,p,S):null;case Rn:return A=p._init,p=A(p._payload),d(g,h,p,S)}if(Cs(p)||ps(p))return A!==null?null:c(g,h,p,S,null);if(typeof p.then=="function")return d(g,h,Dl(p),S);if(p.$$typeof===on)return d(g,h,Ul(g,p),S);Bl(g,p)}return null}function m(g,h,p,S,A){if(typeof S=="string"&&S!==""||typeof S=="number"||typeof S=="bigint")return g=g.get(p)||null,l(h,g,""+S,A);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case Cl:return g=g.get(S.key===null?p:S.key)||null,o(h,g,S,A);case Ms:return g=g.get(S.key===null?p:S.key)||null,u(h,g,S,A);case Rn:var _=S._init;return S=_(S._payload),m(g,h,p,S,A)}if(Cs(S)||ps(S))return g=g.get(p)||null,c(h,g,S,A,null);if(typeof S.then=="function")return m(g,h,p,Dl(S),A);if(S.$$typeof===on)return m(g,h,p,Ul(h,S),A);Bl(h,S)}return null}function y(g,h,p,S){for(var A=null,_=null,O=h,R=h=0,q=null;O!==null&&R<p.length;R++){O.index>R?(q=O,O=null):q=O.sibling;var k=d(g,O,p[R],S);if(k===null){O===null&&(O=q);break}e&&O&&k.alternate===null&&t(g,O),h=s(k,h,R),_===null?A=k:_.sibling=k,_=k,O=q}if(R===p.length)return n(g,O),K&&Ta(g,R),A;if(O===null){for(;R<p.length;R++)O=f(g,p[R],S),O!==null&&(h=s(O,h,R),_===null?A=O:_.sibling=O,_=O);return K&&Ta(g,R),A}for(O=a(O);R<p.length;R++)q=m(O,g,R,p[R],S),q!==null&&(e&&q.alternate!==null&&O.delete(q.key===null?R:q.key),h=s(q,h,R),_===null?A=q:_.sibling=q,_=q);return e&&O.forEach(function(b){return t(g,b)}),K&&Ta(g,R),A}function E(g,h,p,S){if(p==null)throw Error(C(151));for(var A=null,_=null,O=h,R=h=0,q=null,k=p.next();O!==null&&!k.done;R++,k=p.next()){O.index>R?(q=O,O=null):q=O.sibling;var b=d(g,O,k.value,S);if(b===null){O===null&&(O=q);break}e&&O&&b.alternate===null&&t(g,O),h=s(b,h,R),_===null?A=b:_.sibling=b,_=b,O=q}if(k.done)return n(g,O),K&&Ta(g,R),A;if(O===null){for(;!k.done;R++,k=p.next())k=f(g,k.value,S),k!==null&&(h=s(k,h,R),_===null?A=k:_.sibling=k,_=k);return K&&Ta(g,R),A}for(O=a(O);!k.done;R++,k=p.next())k=m(O,g,R,k.value,S),k!==null&&(e&&k.alternate!==null&&O.delete(k.key===null?R:k.key),h=s(k,h,R),_===null?A=k:_.sibling=k,_=k);return e&&O.forEach(function(x){return t(g,x)}),K&&Ta(g,R),A}function M(g,h,p,S){if(typeof p=="object"&&p!==null&&p.type===oi&&p.key===null&&(p=p.props.children),typeof p=="object"&&p!==null){switch(p.$$typeof){case Cl:e:{for(var A=p.key;h!==null;){if(h.key===A){if(A=p.type,A===oi){if(h.tag===7){n(g,h.sibling),S=i(h,p.props.children),S.return=g,g=S;break e}}else if(h.elementType===A||typeof A=="object"&&A!==null&&A.$$typeof===Rn&&Vp(A)===h.type){n(g,h.sibling),S=i(h,p.props),vs(S,p),S.return=g,g=S;break e}n(g,h);break}else t(g,h);h=h.sibling}p.type===oi?(S=Ma(p.props.children,g.mode,S,p.key),S.return=g,g=S):(S=$l(p.type,p.key,p.props,null,g.mode,S),vs(S,p),S.return=g,g=S)}return r(g);case Ms:e:{for(A=p.key;h!==null;){if(h.key===A)if(h.tag===4&&h.stateNode.containerInfo===p.containerInfo&&h.stateNode.implementation===p.implementation){n(g,h.sibling),S=i(h,p.children||[]),S.return=g,g=S;break e}else{n(g,h);break}else t(g,h);h=h.sibling}S=qc(p,g.mode,S),S.return=g,g=S}return r(g);case Rn:return A=p._init,p=A(p._payload),M(g,h,p,S)}if(Cs(p))return y(g,h,p,S);if(ps(p)){if(A=ps(p),typeof A!="function")throw Error(C(150));return p=A.call(p),E(g,h,p,S)}if(typeof p.then=="function")return M(g,h,Dl(p),S);if(p.$$typeof===on)return M(g,h,Ul(g,p),S);Bl(g,p)}return typeof p=="string"&&p!==""||typeof p=="number"||typeof p=="bigint"?(p=""+p,h!==null&&h.tag===6?(n(g,h.sibling),S=i(h,p),S.return=g,g=S):(n(g,h),S=zc(p,g.mode,S),S.return=g,g=S),r(g)):n(g,h)}return function(g,h,p,S){try{Xs=0;var A=M(g,h,p,S);return Ai=null,A}catch(O){if(O===fr||O===zo)throw O;var _=dt(29,O,null,g.mode);return _.lanes=S,_.return=g,_}finally{}}}var Di=W0(!0),J0=W0(!1),xt=Yt(null),$t=null;function Un(e){var t=e.alternate;fe(we,we.current&1),fe(xt,e),$t===null&&(t===null||Ui.current!==null||t.memoizedState!==null)&&($t=e)}function eb(e){if(e.tag===22){if(fe(we,we.current),fe(xt,e),$t===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&($t=e)}}else Nn(e)}function Nn(){fe(we,we.current),fe(xt,xt.current)}function dn(e){_e(xt),$t===e&&($t=null),_e(we)}var we=Yt(0);function po(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||If(n)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function jc(e,t,n,a){t=e.memoizedState,n=n(a,t),n=n==null?t:le({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Nf={enqueueSetState:function(e,t,n){e=e._reactInternals;var a=gt(),i=Hn(a);i.payload=t,n!=null&&(i.callback=n),t=Qn(e,i,a),t!==null&&(pt(t,e,a),Ns(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var a=gt(),i=Hn(a);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=Qn(e,i,a),t!==null&&(pt(t,e,a),Ns(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=gt(),a=Hn(n);a.tag=2,t!=null&&(a.callback=t),t=Qn(e,a,n),t!==null&&(pt(t,e,n),Ns(t,e,n))}};function Fp(e,t,n,a,i,s,r){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(a,s,r):t.prototype&&t.prototype.isPureReactComponent?!Gs(n,a)||!Gs(i,s):!0}function Gp(e,t,n,a){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,a),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,a),t.state!==e&&Nf.enqueueReplaceState(t,t.state,null)}function Na(e,t){var n=t;if("ref"in t){n={};for(var a in t)a!=="ref"&&(n[a]=t[a])}if(e=e.defaultProps){n===t&&(n=le({},n));for(var i in e)n[i]===void 0&&(n[i]=e[i])}return n}var yo=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function tb(e){yo(e)}function nb(e){console.error(e)}function ab(e){yo(e)}function bo(e,t){try{var n=e.onUncaughtError;n(t.value,{componentStack:t.stack})}catch(a){setTimeout(function(){throw a})}}function $p(e,t,n){try{var a=e.onCaughtError;a(n.value,{componentStack:n.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(i){setTimeout(function(){throw i})}}function Df(e,t,n){return n=Hn(n),n.tag=3,n.payload={element:null},n.callback=function(){bo(e,t)},n}function ib(e){return e=Hn(e),e.tag=3,e}function sb(e,t,n,a){var i=n.type.getDerivedStateFromError;if(typeof i=="function"){var s=a.value;e.payload=function(){return i(s)},e.callback=function(){$p(t,n,a)}}var r=n.stateNode;r!==null&&typeof r.componentDidCatch=="function"&&(e.callback=function(){$p(t,n,a),typeof i!="function"&&(jn===null?jn=new Set([this]):jn.add(this));var l=a.stack;this.componentDidCatch(a.value,{componentStack:l!==null?l:""})})}function _C(e,t,n,a,i){if(n.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(t=n.alternate,t!==null&&ur(t,n,i,!0),n=xt.current,n!==null){switch(n.tag){case 13:return $t===null?jf():n.alternate===null&&ge===0&&(ge=3),n.flags&=-257,n.flags|=65536,n.lanes=i,a===Af?n.flags|=16384:(t=n.updateQueue,t===null?n.updateQueue=new Set([a]):t.add(a),Jc(e,a,i)),!1;case 22:return n.flags|=65536,a===Af?n.flags|=16384:(t=n.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([a])},n.updateQueue=t):(n=t.retryQueue,n===null?t.retryQueue=new Set([a]):n.add(a)),Jc(e,a,i)),!1}throw Error(C(435,n.tag))}return Jc(e,a,i),jf(),!1}if(K)return t=xt.current,t!==null?(!(t.flags&65536)&&(t.flags|=256),t.flags|=65536,t.lanes=i,a!==Tf&&(e=Error(C(422),{cause:a}),$s(Ct(e,n)))):(a!==Tf&&(t=Error(C(423),{cause:a}),$s(Ct(t,n))),e=e.current.alternate,e.flags|=65536,i&=-i,e.lanes|=i,a=Ct(a,n),i=Df(e.stateNode,a,i),Hc(e,i),ge!==4&&(ge=2)),!1;var s=Error(C(520),{cause:a});if(s=Ct(s,n),qs===null?qs=[s]:qs.push(s),ge!==4&&(ge=2),t===null)return!0;a=Ct(a,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=i&-i,n.lanes|=e,e=Df(n.stateNode,a,e),Hc(n,e),!1;case 1:if(t=n.type,s=n.stateNode,(n.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||s!==null&&typeof s.componentDidCatch=="function"&&(jn===null||!jn.has(s))))return n.flags|=65536,i&=-i,n.lanes|=i,i=ib(i),sb(i,e,n,a),Hc(n,i),!1}n=n.return}while(n!==null);return!1}var rb=Error(C(461)),Re=!1;function Ne(e,t,n,a){t.child=e===null?J0(t,null,n,a):Di(t,e.child,n,a)}function Yp(e,t,n,a,i){n=n.render;var s=t.ref;if("ref"in a){var r={};for(var l in a)l!=="ref"&&(r[l]=a[l])}else r=a;return ka(t),a=Ed(e,t,n,r,s,i),l=wd(),e!==null&&!Re?(Md(e,t,i),yn(e,t,i)):(K&&l&&gd(t),t.flags|=1,Ne(e,t,a,i),t.child)}function Xp(e,t,n,a,i){if(e===null){var s=n.type;return typeof s=="function"&&!md(s)&&s.defaultProps===void 0&&n.compare===null?(t.tag=15,t.type=s,lb(e,t,s,a,i)):(e=$l(n.type,null,a,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(s=e.child,!Nd(e,i)){var r=s.memoizedProps;if(n=n.compare,n=n!==null?n:Gs,n(r,a)&&e.ref===t.ref)return yn(e,t,i)}return t.flags|=1,e=hn(s,a),e.ref=t.ref,e.return=t,t.child=e}function lb(e,t,n,a,i){if(e!==null){var s=e.memoizedProps;if(Gs(s,a)&&e.ref===t.ref)if(Re=!1,t.pendingProps=a=s,Nd(e,i))e.flags&131072&&(Re=!0);else return t.lanes=e.lanes,yn(e,t,i)}return Bf(e,t,n,a,i)}function ob(e,t,n){var a=t.pendingProps,i=a.children,s=e!==null?e.memoizedState:null;if(a.mode==="hidden"){if(t.flags&128){if(a=s!==null?s.baseLanes|n:n,e!==null){for(i=t.child=e.child,s=0;i!==null;)s=s|i.lanes|i.childLanes,i=i.sibling;t.childLanes=s&~a}else t.childLanes=0,t.child=null;return Ip(e,t,a,n)}if(n&536870912)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&Yl(t,s!==null?s.cachePool:null),s!==null?Pp(t,s):Rf(),eb(t);else return t.lanes=t.childLanes=536870912,Ip(e,t,s!==null?s.baseLanes|n:n,n)}else s!==null?(Yl(t,s.cachePool),Pp(t,s),Nn(t),t.memoizedState=null):(e!==null&&Yl(t,null),Rf(),Nn(t));return Ne(e,t,i,n),t.child}function Ip(e,t,n,a){var i=bd();return i=i===null?null:{parent:Ee._currentValue,pool:i},t.memoizedState={baseLanes:n,cachePool:i},e!==null&&Yl(t,null),Rf(),eb(t),e!==null&&ur(e,t,a,!0),null}function Zl(e,t){var n=t.ref;if(n===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(C(284));(e===null||e.ref!==n)&&(t.flags|=4194816)}}function Bf(e,t,n,a,i){return ka(t),n=Ed(e,t,n,a,void 0,i),a=wd(),e!==null&&!Re?(Md(e,t,i),yn(e,t,i)):(K&&a&&gd(t),t.flags|=1,Ne(e,t,n,i),t.child)}function Zp(e,t,n,a,i,s){return ka(t),t.updateQueue=null,n=T0(t,a,n,i),S0(e),a=wd(),e!==null&&!Re?(Md(e,t,s),yn(e,t,s)):(K&&a&&gd(t),t.flags|=1,Ne(e,t,n,s),t.child)}function Kp(e,t,n,a,i){if(ka(t),t.stateNode===null){var s=pi,r=n.contextType;typeof r=="object"&&r!==null&&(s=Ve(r)),s=new n(a,s),t.memoizedState=s.state!==null&&s.state!==void 0?s.state:null,s.updater=Nf,t.stateNode=s,s._reactInternals=t,s=t.stateNode,s.props=a,s.state=t.memoizedState,s.refs={},vd(t),r=n.contextType,s.context=typeof r=="object"&&r!==null?Ve(r):pi,s.state=t.memoizedState,r=n.getDerivedStateFromProps,typeof r=="function"&&(jc(t,n,r,a),s.state=t.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof s.getSnapshotBeforeUpdate=="function"||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(r=s.state,typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount(),r!==s.state&&Nf.enqueueReplaceState(s,s.state,null),Bs(t,a,s,i),Ds(),s.state=t.memoizedState),typeof s.componentDidMount=="function"&&(t.flags|=4194308),a=!0}else if(e===null){s=t.stateNode;var l=t.memoizedProps,o=Na(n,l);s.props=o;var u=s.context,c=n.contextType;r=pi,typeof c=="object"&&c!==null&&(r=Ve(c));var f=n.getDerivedStateFromProps;c=typeof f=="function"||typeof s.getSnapshotBeforeUpdate=="function",l=t.pendingProps!==l,c||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l||u!==r)&&Gp(t,s,a,r),_n=!1;var d=t.memoizedState;s.state=d,Bs(t,a,s,i),Ds(),u=t.memoizedState,l||d!==u||_n?(typeof f=="function"&&(jc(t,n,f,a),u=t.memoizedState),(o=_n||Fp(t,n,o,a,d,u,r))?(c||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=a,t.memoizedState=u),s.props=a,s.state=u,s.context=r,a=o):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),a=!1)}else{s=t.stateNode,xf(e,t),r=t.memoizedProps,c=Na(n,r),s.props=c,f=t.pendingProps,d=s.context,u=n.contextType,o=pi,typeof u=="object"&&u!==null&&(o=Ve(u)),l=n.getDerivedStateFromProps,(u=typeof l=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(r!==f||d!==o)&&Gp(t,s,a,o),_n=!1,d=t.memoizedState,s.state=d,Bs(t,a,s,i),Ds();var m=t.memoizedState;r!==f||d!==m||_n||e!==null&&e.dependencies!==null&&co(e.dependencies)?(typeof l=="function"&&(jc(t,n,l,a),m=t.memoizedState),(c=_n||Fp(t,n,c,a,d,m,o)||e!==null&&e.dependencies!==null&&co(e.dependencies))?(u||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(a,m,o),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(a,m,o)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||r===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||r===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),t.memoizedProps=a,t.memoizedState=m),s.props=a,s.state=m,s.context=o,a=c):(typeof s.componentDidUpdate!="function"||r===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||r===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),a=!1)}return s=a,Zl(e,t),a=(t.flags&128)!==0,s||a?(s=t.stateNode,n=a&&typeof n.getDerivedStateFromError!="function"?null:s.render(),t.flags|=1,e!==null&&a?(t.child=Di(t,e.child,null,i),t.child=Di(t,null,n,i)):Ne(e,t,n,i),t.memoizedState=s.state,e=t.child):e=yn(e,t,i),e}function Wp(e,t,n,a){return or(),t.flags|=256,Ne(e,t,n,a),t.child}var Vc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Fc(e){return{baseLanes:e,cachePool:g0()}}function Gc(e,t,n){return e=e!==null?e.childLanes&~n:0,t&&(e|=At),e}function ub(e,t,n){var a=t.pendingProps,i=!1,s=(t.flags&128)!==0,r;if((r=s)||(r=e!==null&&e.memoizedState===null?!1:(we.current&2)!==0),r&&(i=!0,t.flags&=-129),r=(t.flags&32)!==0,t.flags&=-33,e===null){if(K){if(i?Un(t):Nn(t),K){var l=me,o;if(o=l){e:{for(o=l,l=jt;o.nodeType!==8;){if(!l){l=null;break e}if(o=Ut(o.nextSibling),o===null){l=null;break e}}l=o}l!==null?(t.memoizedState={dehydrated:l,treeContext:Ca!==null?{id:un,overflow:cn}:null,retryLane:536870912,hydrationErrors:null},o=dt(18,null,null,0),o.stateNode=l,o.return=t,t.child=o,Xe=t,me=null,o=!0):o=!1}o||_a(t)}if(l=t.memoizedState,l!==null&&(l=l.dehydrated,l!==null))return If(l)?t.lanes=32:t.lanes=536870912,null;dn(t)}return l=a.children,a=a.fallback,i?(Nn(t),i=t.mode,l=vo({mode:"hidden",children:l},i),a=Ma(a,i,n,null),l.return=t,a.return=t,l.sibling=a,t.child=l,i=t.child,i.memoizedState=Fc(n),i.childLanes=Gc(e,r,n),t.memoizedState=Vc,a):(Un(t),Pf(t,l))}if(o=e.memoizedState,o!==null&&(l=o.dehydrated,l!==null)){if(s)t.flags&256?(Un(t),t.flags&=-257,t=$c(e,t,n)):t.memoizedState!==null?(Nn(t),t.child=e.child,t.flags|=128,t=null):(Nn(t),i=a.fallback,l=t.mode,a=vo({mode:"visible",children:a.children},l),i=Ma(i,l,n,null),i.flags|=2,a.return=t,i.return=t,a.sibling=i,t.child=a,Di(t,e.child,null,n),a=t.child,a.memoizedState=Fc(n),a.childLanes=Gc(e,r,n),t.memoizedState=Vc,t=i);else if(Un(t),If(l)){if(r=l.nextSibling&&l.nextSibling.dataset,r)var u=r.dgst;r=u,a=Error(C(419)),a.stack="",a.digest=r,$s({value:a,source:null,stack:null}),t=$c(e,t,n)}else if(Re||ur(e,t,n,!1),r=(n&e.childLanes)!==0,Re||r){if(r=re,r!==null&&(a=n&-n,a=a&42?1:ad(a),a=a&(r.suspendedLanes|n)?0:a,a!==0&&a!==o.retryLane))throw o.retryLane=a,Qi(e,a),pt(r,e,a),rb;l.data==="$?"||jf(),t=$c(e,t,n)}else l.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=o.treeContext,me=Ut(l.nextSibling),Xe=t,K=!0,Aa=null,jt=!1,e!==null&&(Et[wt++]=un,Et[wt++]=cn,Et[wt++]=Ca,un=e.id,cn=e.overflow,Ca=t),t=Pf(t,a.children),t.flags|=4096);return t}return i?(Nn(t),i=a.fallback,l=t.mode,o=e.child,u=o.sibling,a=hn(o,{mode:"hidden",children:a.children}),a.subtreeFlags=o.subtreeFlags&65011712,u!==null?i=hn(u,i):(i=Ma(i,l,n,null),i.flags|=2),i.return=t,a.return=t,a.sibling=i,t.child=a,a=i,i=t.child,l=e.child.memoizedState,l===null?l=Fc(n):(o=l.cachePool,o!==null?(u=Ee._currentValue,o=o.parent!==u?{parent:u,pool:u}:o):o=g0(),l={baseLanes:l.baseLanes|n,cachePool:o}),i.memoizedState=l,i.childLanes=Gc(e,r,n),t.memoizedState=Vc,a):(Un(t),n=e.child,e=n.sibling,n=hn(n,{mode:"visible",children:a.children}),n.return=t,n.sibling=null,e!==null&&(r=t.deletions,r===null?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=n,t.memoizedState=null,n)}function Pf(e,t){return t=vo({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function vo(e,t){return e=dt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function $c(e,t,n){return Di(t,e.child,null,n),e=Pf(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Jp(e,t,n){e.lanes|=t;var a=e.alternate;a!==null&&(a.lanes|=t),wf(e.return,t,n)}function Yc(e,t,n,a,i){var s=e.memoizedState;s===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:a,tail:n,tailMode:i}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=a,s.tail=n,s.tailMode=i)}function cb(e,t,n){var a=t.pendingProps,i=a.revealOrder,s=a.tail;if(Ne(e,t,a.children,n),a=we.current,a&2)a=a&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Jp(e,n,t);else if(e.tag===19)Jp(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}a&=1}switch(fe(we,a),i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&po(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Yc(t,!1,i,n,s);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&po(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Yc(t,!0,n,null,s);break;case"together":Yc(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function yn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),In|=t.lanes,!(n&t.childLanes))if(e!==null){if(ur(e,t,n,!1),(n&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(C(153));if(t.child!==null){for(e=t.child,n=hn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=hn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Nd(e,t){return e.lanes&t?!0:(e=e.dependencies,!!(e!==null&&co(e)))}function kC(e,t,n){switch(t.tag){case 3:no(t,t.stateNode.containerInfo),kn(t,Ee,e.memoizedState.cache),or();break;case 27:case 5:ff(t);break;case 4:no(t,t.stateNode.containerInfo);break;case 10:kn(t,t.type,t.memoizedProps.value);break;case 13:var a=t.memoizedState;if(a!==null)return a.dehydrated!==null?(Un(t),t.flags|=128,null):n&t.child.childLanes?ub(e,t,n):(Un(t),e=yn(e,t,n),e!==null?e.sibling:null);Un(t);break;case 19:var i=(e.flags&128)!==0;if(a=(n&t.childLanes)!==0,a||(ur(e,t,n,!1),a=(n&t.childLanes)!==0),i){if(a)return cb(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),fe(we,we.current),a)break;return null;case 22:case 23:return t.lanes=0,ob(e,t,n);case 24:kn(t,Ee,e.memoizedState.cache)}return yn(e,t,n)}function fb(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps)Re=!0;else{if(!Nd(e,n)&&!(t.flags&128))return Re=!1,kC(e,t,n);Re=!!(e.flags&131072)}else Re=!1,K&&t.flags&1048576&&h0(t,uo,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var a=t.elementType,i=a._init;if(a=i(a._payload),t.type=a,typeof a=="function")md(a)?(e=Na(a,e),t.tag=1,t=Kp(null,t,a,e,n)):(t.tag=0,t=Bf(null,t,a,e,n));else{if(a!=null){if(i=a.$$typeof,i===ed){t.tag=11,t=Yp(null,t,a,e,n);break e}else if(i===td){t.tag=14,t=Xp(null,t,a,e,n);break e}}throw t=uf(a)||a,Error(C(306,t,""))}}return t;case 0:return Bf(e,t,t.type,t.pendingProps,n);case 1:return a=t.type,i=Na(a,t.pendingProps),Kp(e,t,a,i,n);case 3:e:{if(no(t,t.stateNode.containerInfo),e===null)throw Error(C(387));a=t.pendingProps;var s=t.memoizedState;i=s.element,xf(e,t),Bs(t,a,null,n);var r=t.memoizedState;if(a=r.cache,kn(t,Ee,a),a!==s.cache&&Mf(t,[Ee],n,!0),Ds(),a=r.element,s.isDehydrated)if(s={element:a,isDehydrated:!1,cache:r.cache},t.updateQueue.baseState=s,t.memoizedState=s,t.flags&256){t=Wp(e,t,a,n);break e}else if(a!==i){i=Ct(Error(C(424)),t),$s(i),t=Wp(e,t,a,n);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(me=Ut(e.firstChild),Xe=t,K=!0,Aa=null,jt=!0,n=J0(t,null,a,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(or(),a===i){t=yn(e,t,n);break e}Ne(e,t,a,n)}t=t.child}return t;case 26:return Zl(e,t),e===null?(n=by(t.type,null,t.pendingProps,null))?t.memoizedState=n:K||(n=t.type,e=t.pendingProps,a=Ao(qn.current).createElement(n),a[je]=t,a[at]=e,Be(a,n,e),Oe(a),t.stateNode=a):t.memoizedState=by(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return ff(t),e===null&&K&&(a=t.stateNode=Kb(t.type,t.pendingProps,qn.current),Xe=t,jt=!0,i=me,Kn(t.type)?(Zf=i,me=Ut(a.firstChild)):me=i),Ne(e,t,t.pendingProps.children,n),Zl(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&K&&((i=a=me)&&(a=a3(a,t.type,t.pendingProps,jt),a!==null?(t.stateNode=a,Xe=t,me=Ut(a.firstChild),jt=!1,i=!0):i=!1),i||_a(t)),ff(t),i=t.type,s=t.pendingProps,r=e!==null?e.memoizedProps:null,a=s.children,Yf(i,s)?a=null:r!==null&&Yf(i,r)&&(t.flags|=32),t.memoizedState!==null&&(i=Ed(e,t,wC,null,null,n),Ws._currentValue=i),Zl(e,t),Ne(e,t,a,n),t.child;case 6:return e===null&&K&&((e=n=me)&&(n=i3(n,t.pendingProps,jt),n!==null?(t.stateNode=n,Xe=t,me=null,e=!0):e=!1),e||_a(t)),null;case 13:return ub(e,t,n);case 4:return no(t,t.stateNode.containerInfo),a=t.pendingProps,e===null?t.child=Di(t,null,a,n):Ne(e,t,a,n),t.child;case 11:return Yp(e,t,t.type,t.pendingProps,n);case 7:return Ne(e,t,t.pendingProps,n),t.child;case 8:return Ne(e,t,t.pendingProps.children,n),t.child;case 12:return Ne(e,t,t.pendingProps.children,n),t.child;case 10:return a=t.pendingProps,kn(t,t.type,a.value),Ne(e,t,a.children,n),t.child;case 9:return i=t.type._context,a=t.pendingProps.children,ka(t),i=Ve(i),a=a(i),t.flags|=1,Ne(e,t,a,n),t.child;case 14:return Xp(e,t,t.type,t.pendingProps,n);case 15:return lb(e,t,t.type,t.pendingProps,n);case 19:return cb(e,t,n);case 31:return a=t.pendingProps,n=t.mode,a={mode:a.mode,children:a.children},e===null?(n=vo(a,n),n.ref=t.ref,t.child=n,n.return=t,t=n):(n=hn(e.child,a),n.ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return ob(e,t,n);case 24:return ka(t),a=Ve(Ee),e===null?(i=bd(),i===null&&(i=re,s=yd(),i.pooledCache=s,s.refCount++,s!==null&&(i.pooledCacheLanes|=n),i=s),t.memoizedState={parent:a,cache:i},vd(t),kn(t,Ee,i)):(e.lanes&n&&(xf(e,t),Bs(t,null,null,n),Ds()),i=e.memoizedState,s=t.memoizedState,i.parent!==a?(i={parent:a,cache:a},t.memoizedState=i,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=i),kn(t,Ee,a)):(a=s.cache,kn(t,Ee,a),a!==i.cache&&Mf(t,[Ee],n,!0))),Ne(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(C(156,t.tag))}function sn(e){e.flags|=4}function ey(e,t){if(t.type!=="stylesheet"||t.state.loading&4)e.flags&=-16777217;else if(e.flags|=16777216,!ev(t)){if(t=xt.current,t!==null&&((X&4194048)===X?$t!==null:(X&62914560)!==X&&!(X&536870912)||t!==$t))throw Us=Af,p0;e.flags|=8192}}function Pl(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?Ly():536870912,e.lanes|=t,Bi|=t)}function Ss(e,t){if(!K)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var a=null;n!==null;)n.alternate!==null&&(a=n),n=n.sibling;a===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:a.sibling=null}}function he(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,a=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,a|=i.subtreeFlags&65011712,a|=i.flags&65011712,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,a|=i.subtreeFlags,a|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=a,e.childLanes=n,t}function UC(e,t,n){var a=t.pendingProps;switch(pd(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return he(t),null;case 1:return he(t),null;case 3:return n=t.stateNode,a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),mn(Ee),Oi(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&(bs(t)?sn(t):e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,_p())),he(t),null;case 26:return n=t.memoizedState,e===null?(sn(t),n!==null?(he(t),ey(t,n)):(he(t),t.flags&=-16777217)):n?n!==e.memoizedState?(sn(t),he(t),ey(t,n)):(he(t),t.flags&=-16777217):(e.memoizedProps!==a&&sn(t),he(t),t.flags&=-16777217),null;case 27:ao(t),n=qn.current;var i=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==a&&sn(t);else{if(!a){if(t.stateNode===null)throw Error(C(166));return he(t),null}e=Ft.current,bs(t)?Op(t,e):(e=Kb(i,a,n),t.stateNode=e,sn(t))}return he(t),null;case 5:if(ao(t),n=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==a&&sn(t);else{if(!a){if(t.stateNode===null)throw Error(C(166));return he(t),null}if(e=Ft.current,bs(t))Op(t,e);else{switch(i=Ao(qn.current),e){case 1:e=i.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=i.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof a.is=="string"?i.createElement("select",{is:a.is}):i.createElement("select"),a.multiple?e.multiple=!0:a.size&&(e.size=a.size);break;default:e=typeof a.is=="string"?i.createElement(n,{is:a.is}):i.createElement(n)}}e[je]=t,e[at]=a;e:for(i=t.child;i!==null;){if(i.tag===5||i.tag===6)e.appendChild(i.stateNode);else if(i.tag!==4&&i.tag!==27&&i.child!==null){i.child.return=i,i=i.child;continue}if(i===t)break e;for(;i.sibling===null;){if(i.return===null||i.return===t)break e;i=i.return}i.sibling.return=i.return,i=i.sibling}t.stateNode=e;e:switch(Be(e,n,a),n){case"button":case"input":case"select":case"textarea":e=!!a.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&sn(t)}}return he(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==a&&sn(t);else{if(typeof a!="string"&&t.stateNode===null)throw Error(C(166));if(e=qn.current,bs(t)){if(e=t.stateNode,n=t.memoizedProps,a=null,i=Xe,i!==null)switch(i.tag){case 27:case 5:a=i.memoizedProps}e[je]=t,e=!!(e.nodeValue===n||a!==null&&a.suppressHydrationWarning===!0||Xb(e.nodeValue,n)),e||_a(t)}else e=Ao(e).createTextNode(a),e[je]=t,t.stateNode=e}return he(t),null;case 13:if(a=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(i=bs(t),a!==null&&a.dehydrated!==null){if(e===null){if(!i)throw Error(C(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(C(317));i[je]=t}else or(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;he(t),i=!1}else i=_p(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=i),i=!0;if(!i)return t.flags&256?(dn(t),t):(dn(t),null)}if(dn(t),t.flags&128)return t.lanes=n,t;if(n=a!==null,e=e!==null&&e.memoizedState!==null,n){a=t.child,i=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(i=a.alternate.memoizedState.cachePool.pool);var s=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(s=a.memoizedState.cachePool.pool),s!==i&&(a.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),Pl(t,t.updateQueue),he(t),null;case 4:return Oi(),e===null&&Qd(t.stateNode.containerInfo),he(t),null;case 10:return mn(t.type),he(t),null;case 19:if(_e(we),i=t.memoizedState,i===null)return he(t),null;if(a=(t.flags&128)!==0,s=i.rendering,s===null)if(a)Ss(i,!1);else{if(ge!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=po(e),s!==null){for(t.flags|=128,Ss(i,!1),e=s.updateQueue,t.updateQueue=e,Pl(t,e),t.subtreeFlags=0,e=n,n=t.child;n!==null;)d0(n,e),n=n.sibling;return fe(we,we.current&1|2),t.child}e=e.sibling}i.tail!==null&&Gt()>To&&(t.flags|=128,a=!0,Ss(i,!1),t.lanes=4194304)}else{if(!a)if(e=po(s),e!==null){if(t.flags|=128,a=!0,e=e.updateQueue,t.updateQueue=e,Pl(t,e),Ss(i,!0),i.tail===null&&i.tailMode==="hidden"&&!s.alternate&&!K)return he(t),null}else 2*Gt()-i.renderingStartTime>To&&n!==536870912&&(t.flags|=128,a=!0,Ss(i,!1),t.lanes=4194304);i.isBackwards?(s.sibling=t.child,t.child=s):(e=i.last,e!==null?e.sibling=s:t.child=s,i.last=s)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Gt(),t.sibling=null,e=we.current,fe(we,a?e&1|2:e&1),t):(he(t),null);case 22:case 23:return dn(t),Sd(),a=t.memoizedState!==null,e!==null?e.memoizedState!==null!==a&&(t.flags|=8192):a&&(t.flags|=8192),a?n&536870912&&!(t.flags&128)&&(he(t),t.subtreeFlags&6&&(t.flags|=8192)):he(t),n=t.updateQueue,n!==null&&Pl(t,n.retryQueue),n=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),a=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),a!==n&&(t.flags|=2048),e!==null&&_e(xa),null;case 24:return n=null,e!==null&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),mn(Ee),he(t),null;case 25:return null;case 30:return null}throw Error(C(156,t.tag))}function NC(e,t){switch(pd(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return mn(Ee),Oi(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return ao(t),null;case 13:if(dn(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(C(340));or()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return _e(we),null;case 4:return Oi(),null;case 10:return mn(t.type),null;case 22:case 23:return dn(t),Sd(),e!==null&&_e(xa),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return mn(Ee),null;case 25:return null;default:return null}}function db(e,t){switch(pd(t),t.tag){case 3:mn(Ee),Oi();break;case 26:case 27:case 5:ao(t);break;case 4:Oi();break;case 13:dn(t);break;case 19:_e(we);break;case 10:mn(t.type);break;case 22:case 23:dn(t),Sd(),e!==null&&_e(xa);break;case 24:mn(Ee)}}function mr(e,t){try{var n=t.updateQueue,a=n!==null?n.lastEffect:null;if(a!==null){var i=a.next;n=i;do{if((n.tag&e)===e){a=void 0;var s=n.create,r=n.inst;a=s(),r.destroy=a}n=n.next}while(n!==i)}}catch(l){se(t,t.return,l)}}function Xn(e,t,n){try{var a=t.updateQueue,i=a!==null?a.lastEffect:null;if(i!==null){var s=i.next;a=s;do{if((a.tag&e)===e){var r=a.inst,l=r.destroy;if(l!==void 0){r.destroy=void 0,i=t;var o=n,u=l;try{u()}catch(c){se(i,o,c)}}}a=a.next}while(a!==s)}}catch(c){se(t,t.return,c)}}function hb(e){var t=e.updateQueue;if(t!==null){var n=e.stateNode;try{v0(t,n)}catch(a){se(e,e.return,a)}}}function mb(e,t,n){n.props=Na(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(a){se(e,t,a)}}function Ls(e,t){try{var n=e.ref;if(n!==null){switch(e.tag){case 26:case 27:case 5:var a=e.stateNode;break;case 30:a=e.stateNode;break;default:a=e.stateNode}typeof n=="function"?e.refCleanup=n(a):n.current=a}}catch(i){se(e,t,i)}}function Vt(e,t){var n=e.ref,a=e.refCleanup;if(n!==null)if(typeof a=="function")try{a()}catch(i){se(e,t,i)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(i){se(e,t,i)}else n.current=null}function gb(e){var t=e.type,n=e.memoizedProps,a=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&a.focus();break e;case"img":n.src?a.src=n.src:n.srcSet&&(a.srcset=n.srcSet)}}catch(i){se(e,e.return,i)}}function Xc(e,t,n){try{var a=e.stateNode;WC(a,e.type,n,t),a[at]=t}catch(i){se(e,e.return,i)}}function pb(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Kn(e.type)||e.tag===4}function Ic(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||pb(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Kn(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Lf(e,t,n){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(e,t):(t=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,t.appendChild(e),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Go));else if(a!==4&&(a===27&&Kn(e.type)&&(n=e.stateNode,t=null),e=e.child,e!==null))for(Lf(e,t,n),e=e.sibling;e!==null;)Lf(e,t,n),e=e.sibling}function So(e,t,n){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(a!==4&&(a===27&&Kn(e.type)&&(n=e.stateNode),e=e.child,e!==null))for(So(e,t,n),e=e.sibling;e!==null;)So(e,t,n),e=e.sibling}function yb(e){var t=e.stateNode,n=e.memoizedProps;try{for(var a=e.type,i=t.attributes;i.length;)t.removeAttributeNode(i[0]);Be(t,a,n),t[je]=e,t[at]=n}catch(s){se(e,e.return,s)}}var ln=!1,ye=!1,Zc=!1,ty=typeof WeakSet=="function"?WeakSet:Set,xe=null;function DC(e,t){if(e=e.containerInfo,Gf=_o,e=i0(e),fd(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var a=n.getSelection&&n.getSelection();if(a&&a.rangeCount!==0){n=a.anchorNode;var i=a.anchorOffset,s=a.focusNode;a=a.focusOffset;try{n.nodeType,s.nodeType}catch(E){n=null;break e}var r=0,l=-1,o=-1,u=0,c=0,f=e,d=null;t:for(;;){for(var m;f!==n||i!==0&&f.nodeType!==3||(l=r+i),f!==s||a!==0&&f.nodeType!==3||(o=r+a),f.nodeType===3&&(r+=f.nodeValue.length),(m=f.firstChild)!==null;)d=f,f=m;for(;;){if(f===e)break t;if(d===n&&++u===i&&(l=r),d===s&&++c===a&&(o=r),(m=f.nextSibling)!==null)break;f=d,d=f.parentNode}f=m}n=l===-1||o===-1?null:{start:l,end:o}}else n=null}n=n||{start:0,end:0}}else n=null;for($f={focusedElem:e,selectionRange:n},_o=!1,xe=t;xe!==null;)if(t=xe,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,xe=e;else for(;xe!==null;){switch(t=xe,s=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if(e&1024&&s!==null){e=void 0,n=t,i=s.memoizedProps,s=s.memoizedState,a=n.stateNode;try{var y=Na(n.type,i,n.elementType===n.type);e=a.getSnapshotBeforeUpdate(y,s),a.__reactInternalSnapshotBeforeUpdate=e}catch(E){se(n,n.return,E)}}break;case 3:if(e&1024){if(e=t.stateNode.containerInfo,n=e.nodeType,n===9)Xf(e);else if(n===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Xf(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if(e&1024)throw Error(C(163))}if(e=t.sibling,e!==null){e.return=t.return,xe=e;break}xe=t.return}}function bb(e,t,n){var a=n.flags;switch(n.tag){case 0:case 11:case 15:xn(e,n),a&4&&mr(5,n);break;case 1:if(xn(e,n),a&4)if(e=n.stateNode,t===null)try{e.componentDidMount()}catch(r){se(n,n.return,r)}else{var i=Na(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(i,t,e.__reactInternalSnapshotBeforeUpdate)}catch(r){se(n,n.return,r)}}a&64&&hb(n),a&512&&Ls(n,n.return);break;case 3:if(xn(e,n),a&64&&(e=n.updateQueue,e!==null)){if(t=null,n.child!==null)switch(n.child.tag){case 27:case 5:t=n.child.stateNode;break;case 1:t=n.child.stateNode}try{v0(e,t)}catch(r){se(n,n.return,r)}}break;case 27:t===null&&a&4&&yb(n);case 26:case 5:xn(e,n),t===null&&a&4&&gb(n),a&512&&Ls(n,n.return);break;case 12:xn(e,n);break;case 13:xn(e,n),a&4&&Tb(e,n),a&64&&(e=n.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(n=VC.bind(null,n),s3(e,n))));break;case 22:if(a=n.memoizedState!==null||ln,!a){t=t!==null&&t.memoizedState!==null||ye,i=ln;var s=ye;ln=a,(ye=t)&&!s?On(e,n,(n.subtreeFlags&8772)!==0):xn(e,n),ln=i,ye=s}break;case 30:break;default:xn(e,n)}}function vb(e){var t=e.alternate;t!==null&&(e.alternate=null,vb(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&sd(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var ce=null,tt=!1;function rn(e,t,n){for(n=n.child;n!==null;)Sb(e,t,n),n=n.sibling}function Sb(e,t,n){if(ht&&typeof ht.onCommitFiberUnmount=="function")try{ht.onCommitFiberUnmount(ar,n)}catch(s){}switch(n.tag){case 26:ye||Vt(n,t),rn(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:ye||Vt(n,t);var a=ce,i=tt;Kn(n.type)&&(ce=n.stateNode,tt=!1),rn(e,t,n),Qs(n.stateNode),ce=a,tt=i;break;case 5:ye||Vt(n,t);case 6:if(a=ce,i=tt,ce=null,rn(e,t,n),ce=a,tt=i,ce!==null)if(tt)try{(ce.nodeType===9?ce.body:ce.nodeName==="HTML"?ce.ownerDocument.body:ce).removeChild(n.stateNode)}catch(s){se(n,t,s)}else try{ce.removeChild(n.stateNode)}catch(s){se(n,t,s)}break;case 18:ce!==null&&(tt?(e=ce,gy(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,n.stateNode),tr(e)):gy(ce,n.stateNode));break;case 4:a=ce,i=tt,ce=n.stateNode.containerInfo,tt=!0,rn(e,t,n),ce=a,tt=i;break;case 0:case 11:case 14:case 15:ye||Xn(2,n,t),ye||Xn(4,n,t),rn(e,t,n);break;case 1:ye||(Vt(n,t),a=n.stateNode,typeof a.componentWillUnmount=="function"&&mb(n,t,a)),rn(e,t,n);break;case 21:rn(e,t,n);break;case 22:ye=(a=ye)||n.memoizedState!==null,rn(e,t,n),ye=a;break;default:rn(e,t,n)}}function Tb(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{tr(e)}catch(n){se(t,t.return,n)}}function BC(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new ty),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new ty),t;default:throw Error(C(435,e.tag))}}function Kc(e,t){var n=BC(e);t.forEach(function(a){var i=FC.bind(null,e,a);n.has(a)||(n.add(a),a.then(i,i))})}function ut(e,t){var n=t.deletions;if(n!==null)for(var a=0;a<n.length;a++){var i=n[a],s=e,r=t,l=r;e:for(;l!==null;){switch(l.tag){case 27:if(Kn(l.type)){ce=l.stateNode,tt=!1;break e}break;case 5:ce=l.stateNode,tt=!1;break e;case 3:case 4:ce=l.stateNode.containerInfo,tt=!0;break e}l=l.return}if(ce===null)throw Error(C(160));Sb(s,r,i),ce=null,tt=!1,s=i.alternate,s!==null&&(s.return=null),i.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)Eb(t,e),t=t.sibling}var kt=null;function Eb(e,t){var n=e.alternate,a=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:ut(t,e),ct(e),a&4&&(Xn(3,e,e.return),mr(3,e),Xn(5,e,e.return));break;case 1:ut(t,e),ct(e),a&512&&(ye||n===null||Vt(n,n.return)),a&64&&ln&&(e=e.updateQueue,e!==null&&(a=e.callbacks,a!==null&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=n===null?a:n.concat(a))));break;case 26:var i=kt;if(ut(t,e),ct(e),a&512&&(ye||n===null||Vt(n,n.return)),a&4){var s=n!==null?n.memoizedState:null;if(a=e.memoizedState,n===null)if(a===null)if(e.stateNode===null){e:{a=e.type,n=e.memoizedProps,i=i.ownerDocument||i;t:switch(a){case"title":s=i.getElementsByTagName("title")[0],(!s||s[rr]||s[je]||s.namespaceURI==="http://www.w3.org/2000/svg"||s.hasAttribute("itemprop"))&&(s=i.createElement(a),i.head.insertBefore(s,i.querySelector("head > title"))),Be(s,a,n),s[je]=e,Oe(s),a=s;break e;case"link":var r=Sy("link","href",i).get(a+(n.href||""));if(r){for(var l=0;l<r.length;l++)if(s=r[l],s.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&s.getAttribute("rel")===(n.rel==null?null:n.rel)&&s.getAttribute("title")===(n.title==null?null:n.title)&&s.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){r.splice(l,1);break t}}s=i.createElement(a),Be(s,a,n),i.head.appendChild(s);break;case"meta":if(r=Sy("meta","content",i).get(a+(n.content||""))){for(l=0;l<r.length;l++)if(s=r[l],s.getAttribute("content")===(n.content==null?null:""+n.content)&&s.getAttribute("name")===(n.name==null?null:n.name)&&s.getAttribute("property")===(n.property==null?null:n.property)&&s.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&s.getAttribute("charset")===(n.charSet==null?null:n.charSet)){r.splice(l,1);break t}}s=i.createElement(a),Be(s,a,n),i.head.appendChild(s);break;default:throw Error(C(468,a))}s[je]=e,Oe(s),a=s}e.stateNode=a}else Ty(i,e.type,e.stateNode);else e.stateNode=vy(i,a,e.memoizedProps);else s!==a?(s===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):s.count--,a===null?Ty(i,e.type,e.stateNode):vy(i,a,e.memoizedProps)):a===null&&e.stateNode!==null&&Xc(e,e.memoizedProps,n.memoizedProps)}break;case 27:ut(t,e),ct(e),a&512&&(ye||n===null||Vt(n,n.return)),n!==null&&a&4&&Xc(e,e.memoizedProps,n.memoizedProps);break;case 5:if(ut(t,e),ct(e),a&512&&(ye||n===null||Vt(n,n.return)),e.flags&32){i=e.stateNode;try{_i(i,"")}catch(m){se(e,e.return,m)}}a&4&&e.stateNode!=null&&(i=e.memoizedProps,Xc(e,i,n!==null?n.memoizedProps:i)),a&1024&&(Zc=!0);break;case 6:if(ut(t,e),ct(e),a&4){if(e.stateNode===null)throw Error(C(162));a=e.memoizedProps,n=e.stateNode;try{n.nodeValue=a}catch(m){se(e,e.return,m)}}break;case 3:if(Jl=null,i=kt,kt=xo(t.containerInfo),ut(t,e),kt=i,ct(e),a&4&&n!==null&&n.memoizedState.isDehydrated)try{tr(t.containerInfo)}catch(m){se(e,e.return,m)}Zc&&(Zc=!1,wb(e));break;case 4:a=kt,kt=xo(e.stateNode.containerInfo),ut(t,e),ct(e),kt=a;break;case 12:ut(t,e),ct(e);break;case 13:ut(t,e),ct(e),e.child.flags&8192&&e.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(zd=Gt()),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,Kc(e,a)));break;case 22:i=e.memoizedState!==null;var o=n!==null&&n.memoizedState!==null,u=ln,c=ye;if(ln=u||i,ye=c||o,ut(t,e),ye=c,ln=u,ct(e),a&8192)e:for(t=e.stateNode,t._visibility=i?t._visibility&-2:t._visibility|1,i&&(n===null||o||ln||ye||Ea(e)),n=null,t=e;;){if(t.tag===5||t.tag===26){if(n===null){o=n=t;try{if(s=o.stateNode,i)r=s.style,typeof r.setProperty=="function"?r.setProperty("display","none","important"):r.display="none";else{l=o.stateNode;var f=o.memoizedProps.style,d=f!=null&&f.hasOwnProperty("display")?f.display:null;l.style.display=d==null||typeof d=="boolean"?"":(""+d).trim()}}catch(m){se(o,o.return,m)}}}else if(t.tag===6){if(n===null){o=t;try{o.stateNode.nodeValue=i?"":o.memoizedProps}catch(m){se(o,o.return,m)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}a&4&&(a=e.updateQueue,a!==null&&(n=a.retryQueue,n!==null&&(a.retryQueue=null,Kc(e,n))));break;case 19:ut(t,e),ct(e),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,Kc(e,a)));break;case 30:break;case 21:break;default:ut(t,e),ct(e)}}function ct(e){var t=e.flags;if(t&2){try{for(var n,a=e.return;a!==null;){if(pb(a)){n=a;break}a=a.return}if(n==null)throw Error(C(160));switch(n.tag){case 27:var i=n.stateNode,s=Ic(e);So(e,s,i);break;case 5:var r=n.stateNode;n.flags&32&&(_i(r,""),n.flags&=-33);var l=Ic(e);So(e,l,r);break;case 3:case 4:var o=n.stateNode.containerInfo,u=Ic(e);Lf(e,u,o);break;default:throw Error(C(161))}}catch(c){se(e,e.return,c)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function wb(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;wb(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function xn(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)bb(e,t.alternate,t),t=t.sibling}function Ea(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:Xn(4,t,t.return),Ea(t);break;case 1:Vt(t,t.return);var n=t.stateNode;typeof n.componentWillUnmount=="function"&&mb(t,t.return,n),Ea(t);break;case 27:Qs(t.stateNode);case 26:case 5:Vt(t,t.return),Ea(t);break;case 22:t.memoizedState===null&&Ea(t);break;case 30:Ea(t);break;default:Ea(t)}e=e.sibling}}function On(e,t,n){for(n=n&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var a=t.alternate,i=e,s=t,r=s.flags;switch(s.tag){case 0:case 11:case 15:On(i,s,n),mr(4,s);break;case 1:if(On(i,s,n),a=s,i=a.stateNode,typeof i.componentDidMount=="function")try{i.componentDidMount()}catch(u){se(a,a.return,u)}if(a=s,i=a.updateQueue,i!==null){var l=a.stateNode;try{var o=i.shared.hiddenCallbacks;if(o!==null)for(i.shared.hiddenCallbacks=null,i=0;i<o.length;i++)b0(o[i],l)}catch(u){se(a,a.return,u)}}n&&r&64&&hb(s),Ls(s,s.return);break;case 27:yb(s);case 26:case 5:On(i,s,n),n&&a===null&&r&4&&gb(s),Ls(s,s.return);break;case 12:On(i,s,n);break;case 13:On(i,s,n),n&&r&4&&Tb(i,s);break;case 22:s.memoizedState===null&&On(i,s,n),Ls(s,s.return);break;case 30:break;default:On(i,s,n)}t=t.sibling}}function Dd(e,t){var n=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==n&&(e!=null&&e.refCount++,n!=null&&cr(n))}function Bd(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&cr(e))}function Qt(e,t,n,a){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Mb(e,t,n,a),t=t.sibling}function Mb(e,t,n,a){var i=t.flags;switch(t.tag){case 0:case 11:case 15:Qt(e,t,n,a),i&2048&&mr(9,t);break;case 1:Qt(e,t,n,a);break;case 3:Qt(e,t,n,a),i&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&cr(e)));break;case 12:if(i&2048){Qt(e,t,n,a),e=t.stateNode;try{var s=t.memoizedProps,r=s.id,l=s.onPostCommit;typeof l=="function"&&l(r,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(o){se(t,t.return,o)}}else Qt(e,t,n,a);break;case 13:Qt(e,t,n,a);break;case 23:break;case 22:s=t.stateNode,r=t.alternate,t.memoizedState!==null?s._visibility&2?Qt(e,t,n,a):zs(e,t):s._visibility&2?Qt(e,t,n,a):(s._visibility|=2,ri(e,t,n,a,(t.subtreeFlags&10256)!==0)),i&2048&&Dd(r,t);break;case 24:Qt(e,t,n,a),i&2048&&Bd(t.alternate,t);break;default:Qt(e,t,n,a)}}function ri(e,t,n,a,i){for(i=i&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var s=e,r=t,l=n,o=a,u=r.flags;switch(r.tag){case 0:case 11:case 15:ri(s,r,l,o,i),mr(8,r);break;case 23:break;case 22:var c=r.stateNode;r.memoizedState!==null?c._visibility&2?ri(s,r,l,o,i):zs(s,r):(c._visibility|=2,ri(s,r,l,o,i)),i&&u&2048&&Dd(r.alternate,r);break;case 24:ri(s,r,l,o,i),i&&u&2048&&Bd(r.alternate,r);break;default:ri(s,r,l,o,i)}t=t.sibling}}function zs(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var n=e,a=t,i=a.flags;switch(a.tag){case 22:zs(n,a),i&2048&&Dd(a.alternate,a);break;case 24:zs(n,a),i&2048&&Bd(a.alternate,a);break;default:zs(n,a)}t=t.sibling}}var xs=8192;function ai(e){if(e.subtreeFlags&xs)for(e=e.child;e!==null;)Cb(e),e=e.sibling}function Cb(e){switch(e.tag){case 26:ai(e),e.flags&xs&&e.memoizedState!==null&&b3(kt,e.memoizedState,e.memoizedProps);break;case 5:ai(e);break;case 3:case 4:var t=kt;kt=xo(e.stateNode.containerInfo),ai(e),kt=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=xs,xs=16777216,ai(e),xs=t):ai(e));break;default:ai(e)}}function Ab(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function Ts(e){var t=e.deletions;if(e.flags&16){if(t!==null)for(var n=0;n<t.length;n++){var a=t[n];xe=a,Ob(a,e)}Ab(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)xb(e),e=e.sibling}function xb(e){switch(e.tag){case 0:case 11:case 15:Ts(e),e.flags&2048&&Xn(9,e,e.return);break;case 3:Ts(e);break;case 12:Ts(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,Kl(e)):Ts(e);break;default:Ts(e)}}function Kl(e){var t=e.deletions;if(e.flags&16){if(t!==null)for(var n=0;n<t.length;n++){var a=t[n];xe=a,Ob(a,e)}Ab(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:Xn(8,t,t.return),Kl(t);break;case 22:n=t.stateNode,n._visibility&2&&(n._visibility&=-3,Kl(t));break;default:Kl(t)}e=e.sibling}}function Ob(e,t){for(;xe!==null;){var n=xe;switch(n.tag){case 0:case 11:case 15:Xn(8,n,t);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var a=n.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:cr(n.memoizedState.cache)}if(a=n.child,a!==null)a.return=n,xe=a;else e:for(n=e;xe!==null;){a=xe;var i=a.sibling,s=a.return;if(vb(a),a===n){xe=null;break e}if(i!==null){i.return=s,xe=i;break e}xe=s}}}var PC={getCacheForType:function(e){var t=Ve(Ee),n=t.data.get(e);return n===void 0&&(n=e(),t.data.set(e,n)),n}},LC=typeof WeakMap=="function"?WeakMap:Map,ne=0,re=null,F=null,X=0,te=0,ft=null,Ln=!1,ji=!1,Pd=!1,bn=0,ge=0,In=0,Oa=0,Ld=0,At=0,Bi=0,qs=null,nt=null,zf=!1,zd=0,To=1/0,Eo=null,jn=null,De=0,Vn=null,Pi=null,xi=0,qf=0,Hf=null,Rb=null,Hs=0,Qf=null;function gt(){if(ne&2&&X!==0)return X&-X;if(N.T!==null){var e=ki;return e!==0?e:Hd()}return Hy()}function _b(){At===0&&(At=!(X&536870912)||K?Py():536870912);var e=xt.current;return e!==null&&(e.flags|=32),At}function pt(e,t,n){(e===re&&(te===2||te===9)||e.cancelPendingCommit!==null)&&(Li(e,0),zn(e,X,At,!1)),sr(e,n),(!(ne&2)||e!==re)&&(e===re&&(!(ne&2)&&(Oa|=n),ge===4&&zn(e,X,At,!1)),Xt(e))}function kb(e,t,n){if(ne&6)throw Error(C(327));var a=!n&&(t&124)===0&&(t&e.expiredLanes)===0||ir(e,t),i=a?HC(e,t):Wc(e,t,!0),s=a;do{if(i===0){ji&&!a&&zn(e,t,0,!1);break}else{if(n=e.current.alternate,s&&!zC(n)){i=Wc(e,t,!1),s=!1;continue}if(i===2){if(s=t,e.errorRecoveryDisabledLanes&s)var r=0;else r=e.pendingLanes&-536870913,r=r!==0?r:r&536870912?536870912:0;if(r!==0){t=r;e:{var l=e;i=qs;var o=l.current.memoizedState.isDehydrated;if(o&&(Li(l,r).flags|=256),r=Wc(l,r,!1),r!==2){if(Pd&&!o){l.errorRecoveryDisabledLanes|=s,Oa|=s,i=4;break e}s=nt,nt=i,s!==null&&(nt===null?nt=s:nt.push.apply(nt,s))}i=r}if(s=!1,i!==2)continue}}if(i===1){Li(e,0),zn(e,t,0,!0);break}e:{switch(a=e,s=i,s){case 0:case 1:throw Error(C(345));case 4:if((t&4194048)!==t)break;case 6:zn(a,t,At,!Ln);break e;case 2:nt=null;break;case 3:case 5:break;default:throw Error(C(329))}if((t&62914560)===t&&(i=zd+300-Gt(),10<i)){if(zn(a,t,At,!Ln),Uo(a,0,!0)!==0)break e;a.timeoutHandle=Zb(ny.bind(null,a,n,nt,Eo,zf,t,At,Oa,Bi,Ln,s,2,-0,0),i);break e}ny(a,n,nt,Eo,zf,t,At,Oa,Bi,Ln,s,0,-0,0)}}break}while(1);Xt(e)}function ny(e,t,n,a,i,s,r,l,o,u,c,f,d,m){if(e.timeoutHandle=-1,f=t.subtreeFlags,(f&8192||(f&16785408)===16785408)&&(Ks={stylesheets:null,count:0,unsuspend:y3},Cb(t),f=v3(),f!==null)){e.cancelPendingCommit=f(iy.bind(null,e,t,s,n,a,i,r,l,o,c,1,d,m)),zn(e,s,r,!u);return}iy(e,t,s,n,a,i,r,l,o)}function zC(e){for(var t=e;;){var n=t.tag;if((n===0||n===11||n===15)&&t.flags&16384&&(n=t.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var a=0;a<n.length;a++){var i=n[a],s=i.getSnapshot;i=i.value;try{if(!yt(s(),i))return!1}catch(r){return!1}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function zn(e,t,n,a){t&=~Ld,t&=~Oa,e.suspendedLanes|=t,e.pingedLanes&=~t,a&&(e.warmLanes|=t),a=e.expirationTimes;for(var i=t;0<i;){var s=31-mt(i),r=1<<s;a[s]=-1,i&=~r}n!==0&&zy(e,n,t)}function jo(){return ne&6?!0:(gr(0,!1),!1)}function qd(){if(F!==null){if(te===0)var e=F.return;else e=F,fn=La=null,Cd(e),Ai=null,Xs=0,e=F;for(;e!==null;)db(e.alternate,e),e=e.return;F=null}}function Li(e,t){var n=e.timeoutHandle;n!==-1&&(e.timeoutHandle=-1,e3(n)),n=e.cancelPendingCommit,n!==null&&(e.cancelPendingCommit=null,n()),qd(),re=e,F=n=hn(e.current,null),X=t,te=0,ft=null,Ln=!1,ji=ir(e,t),Pd=!1,Bi=At=Ld=Oa=In=ge=0,nt=qs=null,zf=!1,t&8&&(t|=t&32);var a=e.entangledLanes;if(a!==0)for(e=e.entanglements,a&=t;0<a;){var i=31-mt(a),s=1<<i;t|=e[i],a&=~s}return bn=t,Po(),n}function Ub(e,t){Q=null,N.H=go,t===fr||t===zo?(t=Dp(),te=3):t===p0?(t=Dp(),te=4):te=t===rb?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,ft=t,F===null&&(ge=1,bo(e,Ct(t,e.current)))}function Nb(){var e=N.H;return N.H=go,e===null?go:e}function Db(){var e=N.A;return N.A=PC,e}function jf(){ge=4,Ln||(X&4194048)!==X&&xt.current!==null||(ji=!0),!(In&134217727)&&!(Oa&134217727)||re===null||zn(re,X,At,!1)}function Wc(e,t,n){var a=ne;ne|=2;var i=Nb(),s=Db();(re!==e||X!==t)&&(Eo=null,Li(e,t)),t=!1;var r=ge;e:do try{if(te!==0&&F!==null){var l=F,o=ft;switch(te){case 8:qd(),r=6;break e;case 3:case 2:case 9:case 6:xt.current===null&&(t=!0);var u=te;if(te=0,ft=null,vi(e,l,o,u),n&&ji){r=0;break e}break;default:u=te,te=0,ft=null,vi(e,l,o,u)}}qC(),r=ge;break}catch(c){Ub(e,c)}while(1);return t&&e.shellSuspendCounter++,fn=La=null,ne=a,N.H=i,N.A=s,F===null&&(re=null,X=0,Po()),r}function qC(){for(;F!==null;)Bb(F)}function HC(e,t){var n=ne;ne|=2;var a=Nb(),i=Db();re!==e||X!==t?(Eo=null,To=Gt()+500,Li(e,t)):ji=ir(e,t);e:do try{if(te!==0&&F!==null){t=F;var s=ft;t:switch(te){case 1:te=0,ft=null,vi(e,t,s,1);break;case 2:case 9:if(Np(s)){te=0,ft=null,ay(t);break}t=function(){te!==2&&te!==9||re!==e||(te=7),Xt(e)},s.then(t,t);break e;case 3:te=7;break e;case 4:te=5;break e;case 7:Np(s)?(te=0,ft=null,ay(t)):(te=0,ft=null,vi(e,t,s,7));break;case 5:var r=null;switch(F.tag){case 26:r=F.memoizedState;case 5:case 27:var l=F;if(!r||ev(r)){te=0,ft=null;var o=l.sibling;if(o!==null)F=o;else{var u=l.return;u!==null?(F=u,Vo(u)):F=null}break t}}te=0,ft=null,vi(e,t,s,5);break;case 6:te=0,ft=null,vi(e,t,s,6);break;case 8:qd(),ge=6;break e;default:throw Error(C(462))}}QC();break}catch(c){Ub(e,c)}while(1);return fn=La=null,N.H=a,N.A=i,ne=n,F!==null?0:(re=null,X=0,Po(),ge)}function QC(){for(;F!==null&&!uM();)Bb(F)}function Bb(e){var t=fb(e.alternate,e,bn);e.memoizedProps=e.pendingProps,t===null?Vo(e):F=t}function ay(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=Zp(n,t,t.pendingProps,t.type,void 0,X);break;case 11:t=Zp(n,t,t.pendingProps,t.type.render,t.ref,X);break;case 5:Cd(t);default:db(n,t),t=F=d0(t,bn),t=fb(n,t,bn)}e.memoizedProps=e.pendingProps,t===null?Vo(e):F=t}function vi(e,t,n,a){fn=La=null,Cd(t),Ai=null,Xs=0;var i=t.return;try{if(_C(e,i,t,n,X)){ge=1,bo(e,Ct(n,e.current)),F=null;return}}catch(s){if(i!==null)throw F=i,s;ge=1,bo(e,Ct(n,e.current)),F=null;return}t.flags&32768?(K||a===1?e=!0:ji||X&536870912?e=!1:(Ln=e=!0,(a===2||a===9||a===3||a===6)&&(a=xt.current,a!==null&&a.tag===13&&(a.flags|=16384))),Pb(t,e)):Vo(t)}function Vo(e){var t=e;do{if(t.flags&32768){Pb(t,Ln);return}e=t.return;var n=UC(t.alternate,t,bn);if(n!==null){F=n;return}if(t=t.sibling,t!==null){F=t;return}F=t=e}while(t!==null);ge===0&&(ge=5)}function Pb(e,t){do{var n=NC(e.alternate,e);if(n!==null){n.flags&=32767,F=n;return}if(n=e.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&(e=e.sibling,e!==null)){F=e;return}F=e=n}while(e!==null);ge=6,F=null}function iy(e,t,n,a,i,s,r,l,o){e.cancelPendingCommit=null;do Fo();while(De!==0);if(ne&6)throw Error(C(327));if(t!==null){if(t===e.current)throw Error(C(177));if(s=t.lanes|t.childLanes,s|=dd,vM(e,n,s,r,l,o),e===re&&(F=re=null,X=0),Pi=t,Vn=e,xi=n,qf=s,Hf=i,Rb=a,t.subtreeFlags&10256||t.flags&10256?(e.callbackNode=null,e.callbackPriority=0,GC(io,function(){return Qb(!0),null})):(e.callbackNode=null,e.callbackPriority=0),a=(t.flags&13878)!==0,t.subtreeFlags&13878||a){a=N.T,N.T=null,i=W.p,W.p=2,r=ne,ne|=4;try{DC(e,t,n)}finally{ne=r,W.p=i,N.T=a}}De=1,Lb(),zb(),qb()}}function Lb(){if(De===1){De=0;var e=Vn,t=Pi,n=(t.flags&13878)!==0;if(t.subtreeFlags&13878||n){n=N.T,N.T=null;var a=W.p;W.p=2;var i=ne;ne|=4;try{Eb(t,e);var s=$f,r=i0(e.containerInfo),l=s.focusedElem,o=s.selectionRange;if(r!==l&&l&&l.ownerDocument&&a0(l.ownerDocument.documentElement,l)){if(o!==null&&fd(l)){var u=o.start,c=o.end;if(c===void 0&&(c=u),"selectionStart"in l)l.selectionStart=u,l.selectionEnd=Math.min(c,l.value.length);else{var f=l.ownerDocument||document,d=f&&f.defaultView||window;if(d.getSelection){var m=d.getSelection(),y=l.textContent.length,E=Math.min(o.start,y),M=o.end===void 0?E:Math.min(o.end,y);!m.extend&&E>M&&(r=M,M=E,E=r);var g=Cp(l,E),h=Cp(l,M);if(g&&h&&(m.rangeCount!==1||m.anchorNode!==g.node||m.anchorOffset!==g.offset||m.focusNode!==h.node||m.focusOffset!==h.offset)){var p=f.createRange();p.setStart(g.node,g.offset),m.removeAllRanges(),E>M?(m.addRange(p),m.extend(h.node,h.offset)):(p.setEnd(h.node,h.offset),m.addRange(p))}}}}for(f=[],m=l;m=m.parentNode;)m.nodeType===1&&f.push({element:m,left:m.scrollLeft,top:m.scrollTop});for(typeof l.focus=="function"&&l.focus(),l=0;l<f.length;l++){var S=f[l];S.element.scrollLeft=S.left,S.element.scrollTop=S.top}}_o=!!Gf,$f=Gf=null}finally{ne=i,W.p=a,N.T=n}}e.current=t,De=2}}function zb(){if(De===2){De=0;var e=Vn,t=Pi,n=(t.flags&8772)!==0;if(t.subtreeFlags&8772||n){n=N.T,N.T=null;var a=W.p;W.p=2;var i=ne;ne|=4;try{bb(e,t.alternate,t)}finally{ne=i,W.p=a,N.T=n}}De=3}}function qb(){if(De===4||De===3){De=0,cM();var e=Vn,t=Pi,n=xi,a=Rb;t.subtreeFlags&10256||t.flags&10256?De=5:(De=0,Pi=Vn=null,Hb(e,e.pendingLanes));var i=e.pendingLanes;if(i===0&&(jn=null),id(n),t=t.stateNode,ht&&typeof ht.onCommitFiberRoot=="function")try{ht.onCommitFiberRoot(ar,t,void 0,(t.current.flags&128)===128)}catch(o){}if(a!==null){t=N.T,i=W.p,W.p=2,N.T=null;try{for(var s=e.onRecoverableError,r=0;r<a.length;r++){var l=a[r];s(l.value,{componentStack:l.stack})}}finally{N.T=t,W.p=i}}xi&3&&Fo(),Xt(e),i=e.pendingLanes,n&4194090&&i&42?e===Qf?Hs++:(Hs=0,Qf=e):Hs=0,gr(0,!1)}}function Hb(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,cr(t)))}function Fo(e){return Lb(),zb(),qb(),Qb(e)}function Qb(){if(De!==5)return!1;var e=Vn,t=qf;qf=0;var n=id(xi),a=N.T,i=W.p;try{W.p=32>n?32:n,N.T=null,n=Hf,Hf=null;var s=Vn,r=xi;if(De=0,Pi=Vn=null,xi=0,ne&6)throw Error(C(331));var l=ne;if(ne|=4,xb(s.current),Mb(s,s.current,r,n),ne=l,gr(0,!1),ht&&typeof ht.onPostCommitFiberRoot=="function")try{ht.onPostCommitFiberRoot(ar,s)}catch(o){}return!0}finally{W.p=i,N.T=a,Hb(e,t)}}function sy(e,t,n){t=Ct(n,t),t=Df(e.stateNode,t,2),e=Qn(e,t,2),e!==null&&(sr(e,2),Xt(e))}function se(e,t,n){if(e.tag===3)sy(e,e,n);else for(;t!==null;){if(t.tag===3){sy(t,e,n);break}else if(t.tag===1){var a=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(jn===null||!jn.has(a))){e=Ct(n,e),n=ib(2),a=Qn(t,n,2),a!==null&&(sb(n,a,t,e),sr(a,2),Xt(a));break}}t=t.return}}function Jc(e,t,n){var a=e.pingCache;if(a===null){a=e.pingCache=new LC;var i=new Set;a.set(t,i)}else i=a.get(t),i===void 0&&(i=new Set,a.set(t,i));i.has(n)||(Pd=!0,i.add(n),e=jC.bind(null,e,t,n),t.then(e,e))}function jC(e,t,n){var a=e.pingCache;a!==null&&a.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,re===e&&(X&n)===n&&(ge===4||ge===3&&(X&62914560)===X&&300>Gt()-zd?!(ne&2)&&Li(e,0):Ld|=n,Bi===X&&(Bi=0)),Xt(e)}function jb(e,t){t===0&&(t=Ly()),e=Qi(e,t),e!==null&&(sr(e,t),Xt(e))}function VC(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),jb(e,n)}function FC(e,t){var n=0;switch(e.tag){case 13:var a=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:a=e.stateNode;break;case 22:a=e.stateNode._retryCache;break;default:throw Error(C(314))}a!==null&&a.delete(t),jb(e,n)}function GC(e,t){return nd(e,t)}var wo=null,li=null,Vf=!1,Mo=!1,ef=!1,Ra=0;function Xt(e){e!==li&&e.next===null&&(li===null?wo=li=e:li=li.next=e),Mo=!0,Vf||(Vf=!0,YC())}function gr(e,t){if(!ef&&Mo){ef=!0;do for(var n=!1,a=wo;a!==null;){if(!t)if(e!==0){var i=a.pendingLanes;if(i===0)var s=0;else{var r=a.suspendedLanes,l=a.pingedLanes;s=(1<<31-mt(42|e)+1)-1,s&=i&~(r&~l),s=s&201326741?s&201326741|1:s?s|2:0}s!==0&&(n=!0,ry(a,s))}else s=X,s=Uo(a,a===re?s:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),!(s&3)||ir(a,s)||(n=!0,ry(a,s));a=a.next}while(n);ef=!1}}function $C(){Vb()}function Vb(){Mo=Vf=!1;var e=0;Ra!==0&&(JC()&&(e=Ra),Ra=0);for(var t=Gt(),n=null,a=wo;a!==null;){var i=a.next,s=Fb(a,t);s===0?(a.next=null,n===null?wo=i:n.next=i,i===null&&(li=n)):(n=a,(e!==0||s&3)&&(Mo=!0)),a=i}gr(e,!1)}function Fb(e,t){for(var n=e.suspendedLanes,a=e.pingedLanes,i=e.expirationTimes,s=e.pendingLanes&-62914561;0<s;){var r=31-mt(s),l=1<<r,o=i[r];o===-1?(!(l&n)||l&a)&&(i[r]=bM(l,t)):o<=t&&(e.expiredLanes|=l),s&=~l}if(t=re,n=X,n=Uo(e,e===t?n:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a=e.callbackNode,n===0||e===t&&(te===2||te===9)||e.cancelPendingCommit!==null)return a!==null&&a!==null&&xc(a),e.callbackNode=null,e.callbackPriority=0;if(!(n&3)||ir(e,n)){if(t=n&-n,t===e.callbackPriority)return t;switch(a!==null&&xc(a),id(n)){case 2:case 8:n=Dy;break;case 32:n=io;break;case 268435456:n=By;break;default:n=io}return a=Gb.bind(null,e),n=nd(n,a),e.callbackPriority=t,e.callbackNode=n,t}return a!==null&&a!==null&&xc(a),e.callbackPriority=2,e.callbackNode=null,2}function Gb(e,t){if(De!==0&&De!==5)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(Fo(!0)&&e.callbackNode!==n)return null;var a=X;return a=Uo(e,e===re?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a===0?null:(kb(e,a,t),Fb(e,Gt()),e.callbackNode!=null&&e.callbackNode===n?Gb.bind(null,e):null)}function ry(e,t){if(Fo())return null;kb(e,t,!0)}function YC(){t3(function(){ne&6?nd(Ny,$C):Vb()})}function Hd(){return Ra===0&&(Ra=Py()),Ra}function ly(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:Vl(""+e)}function oy(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}function XC(e,t,n,a,i){if(t==="submit"&&n&&n.stateNode===i){var s=ly((i[at]||null).action),r=a.submitter;r&&(t=(t=r[at]||null)?ly(t.formAction):r.getAttribute("formAction"),t!==null&&(s=t,r=null));var l=new No("action","action",null,a,i);e.push({event:l,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(Ra!==0){var o=r?oy(i,r):new FormData(i);Uf(n,{pending:!0,data:o,method:i.method,action:s},null,o)}}else typeof s=="function"&&(l.preventDefault(),o=r?oy(i,r):new FormData(i),Uf(n,{pending:!0,data:o,method:i.method,action:s},s,o))},currentTarget:i}]})}}for(Ll=0;Ll<Sf.length;Ll++)zl=Sf[Ll],uy=zl.toLowerCase(),cy=zl[0].toUpperCase()+zl.slice(1),Nt(uy,"on"+cy);var zl,uy,cy,Ll;Nt(r0,"onAnimationEnd");Nt(l0,"onAnimationIteration");Nt(o0,"onAnimationStart");Nt("dblclick","onDoubleClick");Nt("focusin","onFocus");Nt("focusout","onBlur");Nt(hC,"onTransitionRun");Nt(mC,"onTransitionStart");Nt(gC,"onTransitionCancel");Nt(u0,"onTransitionEnd");Ri("onMouseEnter",["mouseout","mouseover"]);Ri("onMouseLeave",["mouseout","mouseover"]);Ri("onPointerEnter",["pointerout","pointerover"]);Ri("onPointerLeave",["pointerout","pointerover"]);Da("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Da("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Da("onBeforeInput",["compositionend","keypress","textInput","paste"]);Da("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Da("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Da("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Is="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),IC=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Is));function $b(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var a=e[n],i=a.event;a=a.listeners;e:{var s=void 0;if(t)for(var r=a.length-1;0<=r;r--){var l=a[r],o=l.instance,u=l.currentTarget;if(l=l.listener,o!==s&&i.isPropagationStopped())break e;s=l,i.currentTarget=u;try{s(i)}catch(c){yo(c)}i.currentTarget=null,s=o}else for(r=0;r<a.length;r++){if(l=a[r],o=l.instance,u=l.currentTarget,l=l.listener,o!==s&&i.isPropagationStopped())break e;s=l,i.currentTarget=u;try{s(i)}catch(c){yo(c)}i.currentTarget=null,s=o}}}}function V(e,t){var n=t[hf];n===void 0&&(n=t[hf]=new Set);var a=e+"__bubble";n.has(a)||(Yb(t,e,2,!1),n.add(a))}function tf(e,t,n){var a=0;t&&(a|=4),Yb(n,e,a,t)}var ql="_reactListening"+Math.random().toString(36).slice(2);function Qd(e){if(!e[ql]){e[ql]=!0,Qy.forEach(function(n){n!=="selectionchange"&&(IC.has(n)||tf(n,!1,e),tf(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ql]||(t[ql]=!0,tf("selectionchange",!1,t))}}function Yb(e,t,n,a){switch(sv(t)){case 2:var i=E3;break;case 8:i=w3;break;default:i=Gd}n=i.bind(null,t,n,e),i=void 0,!yf||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),a?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function nf(e,t,n,a,i){var s=a;if(!(t&1)&&!(t&2)&&a!==null)e:for(;;){if(a===null)return;var r=a.tag;if(r===3||r===4){var l=a.stateNode.containerInfo;if(l===i)break;if(r===4)for(r=a.return;r!==null;){var o=r.tag;if((o===3||o===4)&&r.stateNode.containerInfo===i)return;r=r.return}for(;l!==null;){if(r=ci(l),r===null)return;if(o=r.tag,o===5||o===6||o===26||o===27){a=s=r;continue e}l=l.parentNode}}a=a.return}Iy(function(){var u=s,c=ld(n),f=[];e:{var d=c0.get(e);if(d!==void 0){var m=No,y=e;switch(e){case"keypress":if(Gl(n)===0)break e;case"keydown":case"keyup":m=GM;break;case"focusin":y="focus",m=Bc;break;case"focusout":y="blur",m=Bc;break;case"beforeblur":case"afterblur":m=Bc;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":m=pp;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":m=NM;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":m=XM;break;case r0:case l0:case o0:m=PM;break;case u0:m=ZM;break;case"scroll":case"scrollend":m=kM;break;case"wheel":m=WM;break;case"copy":case"cut":case"paste":m=zM;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":m=bp;break;case"toggle":case"beforetoggle":m=eC}var E=(t&4)!==0,M=!E&&(e==="scroll"||e==="scrollend"),g=E?d!==null?d+"Capture":null:d;E=[];for(var h=u,p;h!==null;){var S=h;if(p=S.stateNode,S=S.tag,S!==5&&S!==26&&S!==27||p===null||g===null||(S=Vs(h,g),S!=null&&E.push(Zs(h,S,p))),M)break;h=h.return}0<E.length&&(d=new m(d,y,null,n,c),f.push({event:d,listeners:E}))}}if(!(t&7)){e:{if(d=e==="mouseover"||e==="pointerover",m=e==="mouseout"||e==="pointerout",d&&n!==pf&&(y=n.relatedTarget||n.fromElement)&&(ci(y)||y[qi]))break e;if((m||d)&&(d=c.window===c?c:(d=c.ownerDocument)?d.defaultView||d.parentWindow:window,m?(y=n.relatedTarget||n.toElement,m=u,y=y?ci(y):null,y!==null&&(M=nr(y),E=y.tag,y!==M||E!==5&&E!==27&&E!==6)&&(y=null)):(m=null,y=u),m!==y)){if(E=pp,S="onMouseLeave",g="onMouseEnter",h="mouse",(e==="pointerout"||e==="pointerover")&&(E=bp,S="onPointerLeave",g="onPointerEnter",h="pointer"),M=m==null?d:As(m),p=y==null?d:As(y),d=new E(S,h+"leave",m,n,c),d.target=M,d.relatedTarget=p,S=null,ci(c)===u&&(E=new E(g,h+"enter",y,n,c),E.target=p,E.relatedTarget=M,S=E),M=S,m&&y)t:{for(E=m,g=y,h=0,p=E;p;p=ii(p))h++;for(p=0,S=g;S;S=ii(S))p++;for(;0<h-p;)E=ii(E),h--;for(;0<p-h;)g=ii(g),p--;for(;h--;){if(E===g||g!==null&&E===g.alternate)break t;E=ii(E),g=ii(g)}E=null}else E=null;m!==null&&fy(f,d,m,E,!1),y!==null&&M!==null&&fy(f,M,y,E,!0)}}e:{if(d=u?As(u):window,m=d.nodeName&&d.nodeName.toLowerCase(),m==="select"||m==="input"&&d.type==="file")var A=Ep;else if(Tp(d))if(t0)A=cC;else{A=oC;var _=lC}else m=d.nodeName,!m||m.toLowerCase()!=="input"||d.type!=="checkbox"&&d.type!=="radio"?u&&rd(u.elementType)&&(A=Ep):A=uC;if(A&&(A=A(e,u))){e0(f,A,n,c);break e}_&&_(e,d,u),e==="focusout"&&u&&d.type==="number"&&u.memoizedProps.value!=null&&gf(d,"number",d.value)}switch(_=u?As(u):window,e){case"focusin":(Tp(_)||_.contentEditable==="true")&&(hi=_,bf=u,_s=null);break;case"focusout":_s=bf=hi=null;break;case"mousedown":vf=!0;break;case"contextmenu":case"mouseup":case"dragend":vf=!1,Ap(f,n,c);break;case"selectionchange":if(dC)break;case"keydown":case"keyup":Ap(f,n,c)}var O;if(cd)e:{switch(e){case"compositionstart":var R="onCompositionStart";break e;case"compositionend":R="onCompositionEnd";break e;case"compositionupdate":R="onCompositionUpdate";break e}R=void 0}else di?Wy(e,n)&&(R="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(R="onCompositionStart");R&&(Ky&&n.locale!=="ko"&&(di||R!=="onCompositionStart"?R==="onCompositionEnd"&&di&&(O=Zy()):(Pn=c,od="value"in Pn?Pn.value:Pn.textContent,di=!0)),_=Co(u,R),0<_.length&&(R=new yp(R,e,null,n,c),f.push({event:R,listeners:_}),O?R.data=O:(O=Jy(n),O!==null&&(R.data=O)))),(O=nC?aC(e,n):iC(e,n))&&(R=Co(u,"onBeforeInput"),0<R.length&&(_=new yp("onBeforeInput","beforeinput",null,n,c),f.push({event:_,listeners:R}),_.data=O)),XC(f,e,u,n,c)}$b(f,t)})}function Zs(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Co(e,t){for(var n=t+"Capture",a=[];e!==null;){var i=e,s=i.stateNode;if(i=i.tag,i!==5&&i!==26&&i!==27||s===null||(i=Vs(e,n),i!=null&&a.unshift(Zs(e,i,s)),i=Vs(e,t),i!=null&&a.push(Zs(e,i,s))),e.tag===3)return a;e=e.return}return[]}function ii(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function fy(e,t,n,a,i){for(var s=t._reactName,r=[];n!==null&&n!==a;){var l=n,o=l.alternate,u=l.stateNode;if(l=l.tag,o!==null&&o===a)break;l!==5&&l!==26&&l!==27||u===null||(o=u,i?(u=Vs(n,s),u!=null&&r.unshift(Zs(n,u,o))):i||(u=Vs(n,s),u!=null&&r.push(Zs(n,u,o)))),n=n.return}r.length!==0&&e.push({event:t,listeners:r})}var ZC=/\r\n?/g,KC=/\u0000|\uFFFD/g;function dy(e){return(typeof e=="string"?e:""+e).replace(ZC,`
`).replace(KC,"")}function Xb(e,t){return t=dy(t),dy(e)===t}function Go(){}function ae(e,t,n,a,i,s){switch(n){case"children":typeof a=="string"?t==="body"||t==="textarea"&&a===""||_i(e,a):(typeof a=="number"||typeof a=="bigint")&&t!=="body"&&_i(e,""+a);break;case"className":Ol(e,"class",a);break;case"tabIndex":Ol(e,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":Ol(e,n,a);break;case"style":Xy(e,a,s);break;case"data":if(t!=="object"){Ol(e,"data",a);break}case"src":case"href":if(a===""&&(t!=="a"||n!=="href")){e.removeAttribute(n);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(n);break}a=Vl(""+a),e.setAttribute(n,a);break;case"action":case"formAction":if(typeof a=="function"){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof s=="function"&&(n==="formAction"?(t!=="input"&&ae(e,t,"name",i.name,i,null),ae(e,t,"formEncType",i.formEncType,i,null),ae(e,t,"formMethod",i.formMethod,i,null),ae(e,t,"formTarget",i.formTarget,i,null)):(ae(e,t,"encType",i.encType,i,null),ae(e,t,"method",i.method,i,null),ae(e,t,"target",i.target,i,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(n);break}a=Vl(""+a),e.setAttribute(n,a);break;case"onClick":a!=null&&(e.onclick=Go);break;case"onScroll":a!=null&&V("scroll",e);break;case"onScrollEnd":a!=null&&V("scrollend",e);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(C(61));if(n=a.__html,n!=null){if(i.children!=null)throw Error(C(60));e.innerHTML=n}}break;case"multiple":e.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":e.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){e.removeAttribute("xlink:href");break}n=Vl(""+a),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(n,""+a):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":a===!0?e.setAttribute(n,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(n,a):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?e.setAttribute(n,a):e.removeAttribute(n);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?e.removeAttribute(n):e.setAttribute(n,a);break;case"popover":V("beforetoggle",e),V("toggle",e),jl(e,"popover",a);break;case"xlinkActuate":an(e,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":an(e,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":an(e,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":an(e,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":an(e,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":an(e,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":an(e,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":an(e,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":an(e,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":jl(e,"is",a);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=RM.get(n)||n,jl(e,n,a))}}function Ff(e,t,n,a,i,s){switch(n){case"style":Xy(e,a,s);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(C(61));if(n=a.__html,n!=null){if(i.children!=null)throw Error(C(60));e.innerHTML=n}}break;case"children":typeof a=="string"?_i(e,a):(typeof a=="number"||typeof a=="bigint")&&_i(e,""+a);break;case"onScroll":a!=null&&V("scroll",e);break;case"onScrollEnd":a!=null&&V("scrollend",e);break;case"onClick":a!=null&&(e.onclick=Go);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!jy.hasOwnProperty(n))e:{if(n[0]==="o"&&n[1]==="n"&&(i=n.endsWith("Capture"),t=n.slice(2,i?n.length-7:void 0),s=e[at]||null,s=s!=null?s[n]:null,typeof s=="function"&&e.removeEventListener(t,s,i),typeof a=="function")){typeof s!="function"&&s!==null&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,a,i);break e}n in e?e[n]=a:a===!0?e.setAttribute(n,""):jl(e,n,a)}}}function Be(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":V("error",e),V("load",e);var a=!1,i=!1,s;for(s in n)if(n.hasOwnProperty(s)){var r=n[s];if(r!=null)switch(s){case"src":a=!0;break;case"srcSet":i=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(C(137,t));default:ae(e,t,s,r,n,null)}}i&&ae(e,t,"srcSet",n.srcSet,n,null),a&&ae(e,t,"src",n.src,n,null);return;case"input":V("invalid",e);var l=s=r=i=null,o=null,u=null;for(a in n)if(n.hasOwnProperty(a)){var c=n[a];if(c!=null)switch(a){case"name":i=c;break;case"type":r=c;break;case"checked":o=c;break;case"defaultChecked":u=c;break;case"value":s=c;break;case"defaultValue":l=c;break;case"children":case"dangerouslySetInnerHTML":if(c!=null)throw Error(C(137,t));break;default:ae(e,t,a,c,n,null)}}Gy(e,s,l,o,u,r,i,!1),so(e);return;case"select":V("invalid",e),a=r=s=null;for(i in n)if(n.hasOwnProperty(i)&&(l=n[i],l!=null))switch(i){case"value":s=l;break;case"defaultValue":r=l;break;case"multiple":a=l;default:ae(e,t,i,l,n,null)}t=s,n=r,e.multiple=!!a,t!=null?Ti(e,!!a,t,!1):n!=null&&Ti(e,!!a,n,!0);return;case"textarea":V("invalid",e),s=i=a=null;for(r in n)if(n.hasOwnProperty(r)&&(l=n[r],l!=null))switch(r){case"value":a=l;break;case"defaultValue":i=l;break;case"children":s=l;break;case"dangerouslySetInnerHTML":if(l!=null)throw Error(C(91));break;default:ae(e,t,r,l,n,null)}Yy(e,a,i,s),so(e);return;case"option":for(o in n)if(n.hasOwnProperty(o)&&(a=n[o],a!=null))switch(o){case"selected":e.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:ae(e,t,o,a,n,null)}return;case"dialog":V("beforetoggle",e),V("toggle",e),V("cancel",e),V("close",e);break;case"iframe":case"object":V("load",e);break;case"video":case"audio":for(a=0;a<Is.length;a++)V(Is[a],e);break;case"image":V("error",e),V("load",e);break;case"details":V("toggle",e);break;case"embed":case"source":case"link":V("error",e),V("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(u in n)if(n.hasOwnProperty(u)&&(a=n[u],a!=null))switch(u){case"children":case"dangerouslySetInnerHTML":throw Error(C(137,t));default:ae(e,t,u,a,n,null)}return;default:if(rd(t)){for(c in n)n.hasOwnProperty(c)&&(a=n[c],a!==void 0&&Ff(e,t,c,a,n,void 0));return}}for(l in n)n.hasOwnProperty(l)&&(a=n[l],a!=null&&ae(e,t,l,a,n,null))}function WC(e,t,n,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var i=null,s=null,r=null,l=null,o=null,u=null,c=null;for(m in n){var f=n[m];if(n.hasOwnProperty(m)&&f!=null)switch(m){case"checked":break;case"value":break;case"defaultValue":o=f;default:a.hasOwnProperty(m)||ae(e,t,m,null,a,f)}}for(var d in a){var m=a[d];if(f=n[d],a.hasOwnProperty(d)&&(m!=null||f!=null))switch(d){case"type":s=m;break;case"name":i=m;break;case"checked":u=m;break;case"defaultChecked":c=m;break;case"value":r=m;break;case"defaultValue":l=m;break;case"children":case"dangerouslySetInnerHTML":if(m!=null)throw Error(C(137,t));break;default:m!==f&&ae(e,t,d,m,a,f)}}mf(e,r,l,o,u,c,s,i);return;case"select":m=r=l=d=null;for(s in n)if(o=n[s],n.hasOwnProperty(s)&&o!=null)switch(s){case"value":break;case"multiple":m=o;default:a.hasOwnProperty(s)||ae(e,t,s,null,a,o)}for(i in a)if(s=a[i],o=n[i],a.hasOwnProperty(i)&&(s!=null||o!=null))switch(i){case"value":d=s;break;case"defaultValue":l=s;break;case"multiple":r=s;default:s!==o&&ae(e,t,i,s,a,o)}t=l,n=r,a=m,d!=null?Ti(e,!!n,d,!1):!!a!=!!n&&(t!=null?Ti(e,!!n,t,!0):Ti(e,!!n,n?[]:"",!1));return;case"textarea":m=d=null;for(l in n)if(i=n[l],n.hasOwnProperty(l)&&i!=null&&!a.hasOwnProperty(l))switch(l){case"value":break;case"children":break;default:ae(e,t,l,null,a,i)}for(r in a)if(i=a[r],s=n[r],a.hasOwnProperty(r)&&(i!=null||s!=null))switch(r){case"value":d=i;break;case"defaultValue":m=i;break;case"children":break;case"dangerouslySetInnerHTML":if(i!=null)throw Error(C(91));break;default:i!==s&&ae(e,t,r,i,a,s)}$y(e,d,m);return;case"option":for(var y in n)if(d=n[y],n.hasOwnProperty(y)&&d!=null&&!a.hasOwnProperty(y))switch(y){case"selected":e.selected=!1;break;default:ae(e,t,y,null,a,d)}for(o in a)if(d=a[o],m=n[o],a.hasOwnProperty(o)&&d!==m&&(d!=null||m!=null))switch(o){case"selected":e.selected=d&&typeof d!="function"&&typeof d!="symbol";break;default:ae(e,t,o,d,a,m)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var E in n)d=n[E],n.hasOwnProperty(E)&&d!=null&&!a.hasOwnProperty(E)&&ae(e,t,E,null,a,d);for(u in a)if(d=a[u],m=n[u],a.hasOwnProperty(u)&&d!==m&&(d!=null||m!=null))switch(u){case"children":case"dangerouslySetInnerHTML":if(d!=null)throw Error(C(137,t));break;default:ae(e,t,u,d,a,m)}return;default:if(rd(t)){for(var M in n)d=n[M],n.hasOwnProperty(M)&&d!==void 0&&!a.hasOwnProperty(M)&&Ff(e,t,M,void 0,a,d);for(c in a)d=a[c],m=n[c],!a.hasOwnProperty(c)||d===m||d===void 0&&m===void 0||Ff(e,t,c,d,a,m);return}}for(var g in n)d=n[g],n.hasOwnProperty(g)&&d!=null&&!a.hasOwnProperty(g)&&ae(e,t,g,null,a,d);for(f in a)d=a[f],m=n[f],!a.hasOwnProperty(f)||d===m||d==null&&m==null||ae(e,t,f,d,a,m)}var Gf=null,$f=null;function Ao(e){return e.nodeType===9?e:e.ownerDocument}function hy(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Ib(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function Yf(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var af=null;function JC(){var e=window.event;return e&&e.type==="popstate"?e===af?!1:(af=e,!0):(af=null,!1)}var Zb=typeof setTimeout=="function"?setTimeout:void 0,e3=typeof clearTimeout=="function"?clearTimeout:void 0,my=typeof Promise=="function"?Promise:void 0,t3=typeof queueMicrotask=="function"?queueMicrotask:typeof my!="undefined"?function(e){return my.resolve(null).then(e).catch(n3)}:Zb;function n3(e){setTimeout(function(){throw e})}function Kn(e){return e==="head"}function gy(e,t){var n=t,a=0,i=0;do{var s=n.nextSibling;if(e.removeChild(n),s&&s.nodeType===8)if(n=s.data,n==="/$"){if(0<a&&8>a){n=a;var r=e.ownerDocument;if(n&1&&Qs(r.documentElement),n&2&&Qs(r.body),n&4)for(n=r.head,Qs(n),r=n.firstChild;r;){var l=r.nextSibling,o=r.nodeName;r[rr]||o==="SCRIPT"||o==="STYLE"||o==="LINK"&&r.rel.toLowerCase()==="stylesheet"||n.removeChild(r),r=l}}if(i===0){e.removeChild(s),tr(t);return}i--}else n==="$"||n==="$?"||n==="$!"?i++:a=n.charCodeAt(0)-48;else a=0;n=s}while(n);tr(t)}function Xf(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":Xf(n),sd(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}e.removeChild(n)}}function a3(e,t,n,a){for(;e.nodeType===1;){var i=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!a&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(a){if(!e[rr])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(s=e.getAttribute("rel"),s==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(s!==i.rel||e.getAttribute("href")!==(i.href==null||i.href===""?null:i.href)||e.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin)||e.getAttribute("title")!==(i.title==null?null:i.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(s=e.getAttribute("src"),(s!==(i.src==null?null:i.src)||e.getAttribute("type")!==(i.type==null?null:i.type)||e.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin))&&s&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var s=i.name==null?null:""+i.name;if(i.type==="hidden"&&e.getAttribute("name")===s)return e}else return e;if(e=Ut(e.nextSibling),e===null)break}return null}function i3(e,t,n){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!n||(e=Ut(e.nextSibling),e===null))return null;return e}function If(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function s3(e,t){var n=e.ownerDocument;if(e.data!=="$?"||n.readyState==="complete")t();else{var a=function(){t(),n.removeEventListener("DOMContentLoaded",a)};n.addEventListener("DOMContentLoaded",a),e._reactRetry=a}}function Ut(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Zf=null;function py(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}function Kb(e,t,n){switch(t=Ao(n),e){case"html":if(e=t.documentElement,!e)throw Error(C(452));return e;case"head":if(e=t.head,!e)throw Error(C(453));return e;case"body":if(e=t.body,!e)throw Error(C(454));return e;default:throw Error(C(451))}}function Qs(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);sd(e)}var Ot=new Map,yy=new Set;function xo(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var vn=W.d;W.d={f:r3,r:l3,D:o3,C:u3,L:c3,m:f3,X:h3,S:d3,M:m3};function r3(){var e=vn.f(),t=jo();return e||t}function l3(e){var t=Hi(e);t!==null&&t.tag===5&&t.type==="form"?F0(t):vn.r(e)}var Vi=typeof document=="undefined"?null:document;function Wb(e,t,n){var a=Vi;if(a&&typeof t=="string"&&t){var i=Mt(t);i='link[rel="'+e+'"][href="'+i+'"]',typeof n=="string"&&(i+='[crossorigin="'+n+'"]'),yy.has(i)||(yy.add(i),e={rel:e,crossOrigin:n,href:t},a.querySelector(i)===null&&(t=a.createElement("link"),Be(t,"link",e),Oe(t),a.head.appendChild(t)))}}function o3(e){vn.D(e),Wb("dns-prefetch",e,null)}function u3(e,t){vn.C(e,t),Wb("preconnect",e,t)}function c3(e,t,n){vn.L(e,t,n);var a=Vi;if(a&&e&&t){var i='link[rel="preload"][as="'+Mt(t)+'"]';t==="image"&&n&&n.imageSrcSet?(i+='[imagesrcset="'+Mt(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(i+='[imagesizes="'+Mt(n.imageSizes)+'"]')):i+='[href="'+Mt(e)+'"]';var s=i;switch(t){case"style":s=zi(e);break;case"script":s=Fi(e)}Ot.has(s)||(e=le({rel:"preload",href:t==="image"&&n&&n.imageSrcSet?void 0:e,as:t},n),Ot.set(s,e),a.querySelector(i)!==null||t==="style"&&a.querySelector(pr(s))||t==="script"&&a.querySelector(yr(s))||(t=a.createElement("link"),Be(t,"link",e),Oe(t),a.head.appendChild(t)))}}function f3(e,t){vn.m(e,t);var n=Vi;if(n&&e){var a=t&&typeof t.as=="string"?t.as:"script",i='link[rel="modulepreload"][as="'+Mt(a)+'"][href="'+Mt(e)+'"]',s=i;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":s=Fi(e)}if(!Ot.has(s)&&(e=le({rel:"modulepreload",href:e},t),Ot.set(s,e),n.querySelector(i)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(yr(s)))return}a=n.createElement("link"),Be(a,"link",e),Oe(a),n.head.appendChild(a)}}}function d3(e,t,n){vn.S(e,t,n);var a=Vi;if(a&&e){var i=Si(a).hoistableStyles,s=zi(e);t=t||"default";var r=i.get(s);if(!r){var l={loading:0,preload:null};if(r=a.querySelector(pr(s)))l.loading=5;else{e=le({rel:"stylesheet",href:e,"data-precedence":t},n),(n=Ot.get(s))&&jd(e,n);var o=r=a.createElement("link");Oe(o),Be(o,"link",e),o._p=new Promise(function(u,c){o.onload=u,o.onerror=c}),o.addEventListener("load",function(){l.loading|=1}),o.addEventListener("error",function(){l.loading|=2}),l.loading|=4,Wl(r,t,a)}r={type:"stylesheet",instance:r,count:1,state:l},i.set(s,r)}}}function h3(e,t){vn.X(e,t);var n=Vi;if(n&&e){var a=Si(n).hoistableScripts,i=Fi(e),s=a.get(i);s||(s=n.querySelector(yr(i)),s||(e=le({src:e,async:!0},t),(t=Ot.get(i))&&Vd(e,t),s=n.createElement("script"),Oe(s),Be(s,"link",e),n.head.appendChild(s)),s={type:"script",instance:s,count:1,state:null},a.set(i,s))}}function m3(e,t){vn.M(e,t);var n=Vi;if(n&&e){var a=Si(n).hoistableScripts,i=Fi(e),s=a.get(i);s||(s=n.querySelector(yr(i)),s||(e=le({src:e,async:!0,type:"module"},t),(t=Ot.get(i))&&Vd(e,t),s=n.createElement("script"),Oe(s),Be(s,"link",e),n.head.appendChild(s)),s={type:"script",instance:s,count:1,state:null},a.set(i,s))}}function by(e,t,n,a){var i=(i=qn.current)?xo(i):null;if(!i)throw Error(C(446));switch(e){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(t=zi(n.href),n=Si(i).hoistableStyles,a=n.get(t),a||(a={type:"style",instance:null,count:0,state:null},n.set(t,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){e=zi(n.href);var s=Si(i).hoistableStyles,r=s.get(e);if(r||(i=i.ownerDocument||i,r={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},s.set(e,r),(s=i.querySelector(pr(e)))&&!s._p&&(r.instance=s,r.state.loading=5),Ot.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Ot.set(e,n),s||g3(i,e,n,r.state))),t&&a===null)throw Error(C(528,""));return r}if(t&&a!==null)throw Error(C(529,""));return null;case"script":return t=n.async,n=n.src,typeof n=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Fi(n),n=Si(i).hoistableScripts,a=n.get(t),a||(a={type:"script",instance:null,count:0,state:null},n.set(t,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(C(444,e))}}function zi(e){return'href="'+Mt(e)+'"'}function pr(e){return'link[rel="stylesheet"]['+e+"]"}function Jb(e){return le({},e,{"data-precedence":e.precedence,precedence:null})}function g3(e,t,n,a){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?a.loading=1:(t=e.createElement("link"),a.preload=t,t.addEventListener("load",function(){return a.loading|=1}),t.addEventListener("error",function(){return a.loading|=2}),Be(t,"link",n),Oe(t),e.head.appendChild(t))}function Fi(e){return'[src="'+Mt(e)+'"]'}function yr(e){return"script[async]"+e}function vy(e,t,n){if(t.count++,t.instance===null)switch(t.type){case"style":var a=e.querySelector('style[data-href~="'+Mt(n.href)+'"]');if(a)return t.instance=a,Oe(a),a;var i=le({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return a=(e.ownerDocument||e).createElement("style"),Oe(a),Be(a,"style",i),Wl(a,n.precedence,e),t.instance=a;case"stylesheet":i=zi(n.href);var s=e.querySelector(pr(i));if(s)return t.state.loading|=4,t.instance=s,Oe(s),s;a=Jb(n),(i=Ot.get(i))&&jd(a,i),s=(e.ownerDocument||e).createElement("link"),Oe(s);var r=s;return r._p=new Promise(function(l,o){r.onload=l,r.onerror=o}),Be(s,"link",a),t.state.loading|=4,Wl(s,n.precedence,e),t.instance=s;case"script":return s=Fi(n.src),(i=e.querySelector(yr(s)))?(t.instance=i,Oe(i),i):(a=n,(i=Ot.get(s))&&(a=le({},n),Vd(a,i)),e=e.ownerDocument||e,i=e.createElement("script"),Oe(i),Be(i,"link",a),e.head.appendChild(i),t.instance=i);case"void":return null;default:throw Error(C(443,t.type))}else t.type==="stylesheet"&&!(t.state.loading&4)&&(a=t.instance,t.state.loading|=4,Wl(a,n.precedence,e));return t.instance}function Wl(e,t,n){for(var a=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),i=a.length?a[a.length-1]:null,s=i,r=0;r<a.length;r++){var l=a[r];if(l.dataset.precedence===t)s=l;else if(s!==i)break}s?s.parentNode.insertBefore(e,s.nextSibling):(t=n.nodeType===9?n.head:n,t.insertBefore(e,t.firstChild))}function jd(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function Vd(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var Jl=null;function Sy(e,t,n){if(Jl===null){var a=new Map,i=Jl=new Map;i.set(n,a)}else i=Jl,a=i.get(n),a||(a=new Map,i.set(n,a));if(a.has(e))return a;for(a.set(e,null),n=n.getElementsByTagName(e),i=0;i<n.length;i++){var s=n[i];if(!(s[rr]||s[je]||e==="link"&&s.getAttribute("rel")==="stylesheet")&&s.namespaceURI!=="http://www.w3.org/2000/svg"){var r=s.getAttribute(t)||"";r=e+r;var l=a.get(r);l?l.push(s):a.set(r,[s])}}return a}function Ty(e,t,n){e=e.ownerDocument||e,e.head.insertBefore(n,t==="title"?e.querySelector("head > title"):null)}function p3(e,t,n){if(n===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function ev(e){return!(e.type==="stylesheet"&&!(e.state.loading&3))}var Ks=null;function y3(){}function b3(e,t,n){if(Ks===null)throw Error(C(475));var a=Ks;if(t.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&!(t.state.loading&4)){if(t.instance===null){var i=zi(n.href),s=e.querySelector(pr(i));if(s){e=s._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(a.count++,a=Oo.bind(a),e.then(a,a)),t.state.loading|=4,t.instance=s,Oe(s);return}s=e.ownerDocument||e,n=Jb(n),(i=Ot.get(i))&&jd(n,i),s=s.createElement("link"),Oe(s);var r=s;r._p=new Promise(function(l,o){r.onload=l,r.onerror=o}),Be(s,"link",n),t.instance=s}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(t,e),(e=t.state.preload)&&!(t.state.loading&3)&&(a.count++,t=Oo.bind(a),e.addEventListener("load",t),e.addEventListener("error",t))}}function v3(){if(Ks===null)throw Error(C(475));var e=Ks;return e.stylesheets&&e.count===0&&Kf(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&Kf(e,e.stylesheets),e.unsuspend){var a=e.unsuspend;e.unsuspend=null,a()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}function Oo(){if(this.count--,this.count===0){if(this.stylesheets)Kf(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var Ro=null;function Kf(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,Ro=new Map,t.forEach(S3,e),Ro=null,Oo.call(e))}function S3(e,t){if(!(t.state.loading&4)){var n=Ro.get(e);if(n)var a=n.get(null);else{n=new Map,Ro.set(e,n);for(var i=e.querySelectorAll("link[data-precedence],style[data-precedence]"),s=0;s<i.length;s++){var r=i[s];(r.nodeName==="LINK"||r.getAttribute("media")!=="not all")&&(n.set(r.dataset.precedence,r),a=r)}a&&n.set(null,a)}i=t.instance,r=i.getAttribute("data-precedence"),s=n.get(r)||a,s===a&&n.set(null,i),n.set(r,i),this.count++,a=Oo.bind(this),i.addEventListener("load",a),i.addEventListener("error",a),s?s.parentNode.insertBefore(i,s.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(i,e.firstChild)),t.state.loading|=4}}var Ws={$$typeof:on,Provider:null,Consumer:null,_currentValue:wa,_currentValue2:wa,_threadCount:0};function T3(e,t,n,a,i,s,r,l){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Oc(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Oc(0),this.hiddenUpdates=Oc(null),this.identifierPrefix=a,this.onUncaughtError=i,this.onCaughtError=s,this.onRecoverableError=r,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=l,this.incompleteTransitions=new Map}function tv(e,t,n,a,i,s,r,l,o,u,c,f){return e=new T3(e,t,n,r,l,o,u,f),t=1,s===!0&&(t|=24),s=dt(3,null,null,t),e.current=s,s.stateNode=e,t=yd(),t.refCount++,e.pooledCache=t,t.refCount++,s.memoizedState={element:a,isDehydrated:n,cache:t},vd(s),e}function nv(e){return e?(e=pi,e):pi}function av(e,t,n,a,i,s){i=nv(i),a.context===null?a.context=i:a.pendingContext=i,a=Hn(t),a.payload={element:n},s=s===void 0?null:s,s!==null&&(a.callback=s),n=Qn(e,a,t),n!==null&&(pt(n,e,t),Ns(n,e,t))}function Ey(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Fd(e,t){Ey(e,t),(e=e.alternate)&&Ey(e,t)}function iv(e){if(e.tag===13){var t=Qi(e,67108864);t!==null&&pt(t,e,67108864),Fd(e,67108864)}}var _o=!0;function E3(e,t,n,a){var i=N.T;N.T=null;var s=W.p;try{W.p=2,Gd(e,t,n,a)}finally{W.p=s,N.T=i}}function w3(e,t,n,a){var i=N.T;N.T=null;var s=W.p;try{W.p=8,Gd(e,t,n,a)}finally{W.p=s,N.T=i}}function Gd(e,t,n,a){if(_o){var i=Wf(a);if(i===null)nf(e,t,a,ko,n),wy(e,a);else if(C3(i,e,t,n,a))a.stopPropagation();else if(wy(e,a),t&4&&-1<M3.indexOf(e)){for(;i!==null;){var s=Hi(i);if(s!==null)switch(s.tag){case 3:if(s=s.stateNode,s.current.memoizedState.isDehydrated){var r=Sa(s.pendingLanes);if(r!==0){var l=s;for(l.pendingLanes|=2,l.entangledLanes|=2;r;){var o=1<<31-mt(r);l.entanglements[1]|=o,r&=~o}Xt(s),!(ne&6)&&(To=Gt()+500,gr(0,!1))}}break;case 13:l=Qi(s,2),l!==null&&pt(l,s,2),jo(),Fd(s,2)}if(s=Wf(a),s===null&&nf(e,t,a,ko,n),s===i)break;i=s}i!==null&&a.stopPropagation()}else nf(e,t,a,null,n)}}function Wf(e){return e=ld(e),$d(e)}var ko=null;function $d(e){if(ko=null,e=ci(e),e!==null){var t=nr(e);if(t===null)e=null;else{var n=t.tag;if(n===13){if(e=Ry(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return ko=e,null}function sv(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(fM()){case Ny:return 2;case Dy:return 8;case io:case dM:return 32;case By:return 268435456;default:return 32}default:return 32}}var Jf=!1,Fn=null,Gn=null,$n=null,Js=new Map,er=new Map,Dn=[],M3="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function wy(e,t){switch(e){case"focusin":case"focusout":Fn=null;break;case"dragenter":case"dragleave":Gn=null;break;case"mouseover":case"mouseout":$n=null;break;case"pointerover":case"pointerout":Js.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":er.delete(t.pointerId)}}function Es(e,t,n,a,i,s){return e===null||e.nativeEvent!==s?(e={blockedOn:t,domEventName:n,eventSystemFlags:a,nativeEvent:s,targetContainers:[i]},t!==null&&(t=Hi(t),t!==null&&iv(t)),e):(e.eventSystemFlags|=a,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function C3(e,t,n,a,i){switch(t){case"focusin":return Fn=Es(Fn,e,t,n,a,i),!0;case"dragenter":return Gn=Es(Gn,e,t,n,a,i),!0;case"mouseover":return $n=Es($n,e,t,n,a,i),!0;case"pointerover":var s=i.pointerId;return Js.set(s,Es(Js.get(s)||null,e,t,n,a,i)),!0;case"gotpointercapture":return s=i.pointerId,er.set(s,Es(er.get(s)||null,e,t,n,a,i)),!0}return!1}function rv(e){var t=ci(e.target);if(t!==null){var n=nr(t);if(n!==null){if(t=n.tag,t===13){if(t=Ry(n),t!==null){e.blockedOn=t,SM(e.priority,function(){if(n.tag===13){var a=gt();a=ad(a);var i=Qi(n,a);i!==null&&pt(i,n,a),Fd(n,a)}});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function eo(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Wf(e.nativeEvent);if(n===null){n=e.nativeEvent;var a=new n.constructor(n.type,n);pf=a,n.target.dispatchEvent(a),pf=null}else return t=Hi(n),t!==null&&iv(t),e.blockedOn=n,!1;t.shift()}return!0}function My(e,t,n){eo(e)&&n.delete(t)}function A3(){Jf=!1,Fn!==null&&eo(Fn)&&(Fn=null),Gn!==null&&eo(Gn)&&(Gn=null),$n!==null&&eo($n)&&($n=null),Js.forEach(My),er.forEach(My)}function Hl(e,t){e.blockedOn===t&&(e.blockedOn=null,Jf||(Jf=!0,Me.unstable_scheduleCallback(Me.unstable_NormalPriority,A3)))}var Ql=null;function Cy(e){Ql!==e&&(Ql=e,Me.unstable_scheduleCallback(Me.unstable_NormalPriority,function(){Ql===e&&(Ql=null);for(var t=0;t<e.length;t+=3){var n=e[t],a=e[t+1],i=e[t+2];if(typeof a!="function"){if($d(a||n)===null)continue;break}var s=Hi(n);s!==null&&(e.splice(t,3),t-=3,Uf(s,{pending:!0,data:i,method:n.method,action:a},a,i))}}))}function tr(e){function t(o){return Hl(o,e)}Fn!==null&&Hl(Fn,e),Gn!==null&&Hl(Gn,e),$n!==null&&Hl($n,e),Js.forEach(t),er.forEach(t);for(var n=0;n<Dn.length;n++){var a=Dn[n];a.blockedOn===e&&(a.blockedOn=null)}for(;0<Dn.length&&(n=Dn[0],n.blockedOn===null);)rv(n),n.blockedOn===null&&Dn.shift();if(n=(e.ownerDocument||e).$$reactFormReplay,n!=null)for(a=0;a<n.length;a+=3){var i=n[a],s=n[a+1],r=i[at]||null;if(typeof s=="function")r||Cy(n);else if(r){var l=null;if(s&&s.hasAttribute("formAction")){if(i=s,r=s[at]||null)l=r.formAction;else if($d(i)!==null)continue}else l=r.action;typeof l=="function"?n[a+1]=l:(n.splice(a,3),a-=3),Cy(n)}}}function Yd(e){this._internalRoot=e}$o.prototype.render=Yd.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(C(409));var n=t.current,a=gt();av(n,a,e,t,null,null)};$o.prototype.unmount=Yd.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;av(e.current,2,null,e,null,null),jo(),t[qi]=null}};function $o(e){this._internalRoot=e}$o.prototype.unstable_scheduleHydration=function(e){if(e){var t=Hy();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Dn.length&&t!==0&&t<Dn[n].priority;n++);Dn.splice(n,0,e),n===0&&rv(e)}};var Ay=xy.version;if(Ay!=="19.1.0")throw Error(C(527,Ay,"19.1.0"));W.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(C(188)):(e=Object.keys(e).join(","),Error(C(268,e)));return e=iM(t),e=e!==null?_y(e):null,e=e===null?null:e.stateNode,e};var x3={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:N,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__!="undefined"&&(ws=__REACT_DEVTOOLS_GLOBAL_HOOK__,!ws.isDisabled&&ws.supportsFiber))try{ar=ws.inject(x3),ht=ws}catch(e){}var ws;Yo.createRoot=function(e,t){if(!Oy(e))throw Error(C(299));var n=!1,a="",i=tb,s=nb,r=ab,l=null;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(a=t.identifierPrefix),t.onUncaughtError!==void 0&&(i=t.onUncaughtError),t.onCaughtError!==void 0&&(s=t.onCaughtError),t.onRecoverableError!==void 0&&(r=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(l=t.unstable_transitionCallbacks)),t=tv(e,1,!1,null,null,n,a,i,s,r,l,null),e[qi]=t.current,Qd(e),new Yd(t)};Yo.hydrateRoot=function(e,t,n){if(!Oy(e))throw Error(C(299));var a=!1,i="",s=tb,r=nb,l=ab,o=null,u=null;return n!=null&&(n.unstable_strictMode===!0&&(a=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onUncaughtError!==void 0&&(s=n.onUncaughtError),n.onCaughtError!==void 0&&(r=n.onCaughtError),n.onRecoverableError!==void 0&&(l=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(o=n.unstable_transitionCallbacks),n.formState!==void 0&&(u=n.formState)),t=tv(e,1,!0,t,n!=null?n:null,a,i,s,r,l,o,u),t.context=nv(null),n=t.current,a=gt(),a=ad(a),i=Hn(a),i.callback=null,Qn(n,i,a),n=a,t.current.lanes=n,sr(t,n),Xt(t),e[qi]=t.current,Qd(e),new $o(t)};Yo.version="19.1.0"});var Xo=D((U2,uv)=>{"use strict";function ov(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__=="undefined"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(ov)}catch(e){console.error(e)}}ov(),uv.exports=lv()});var fv=D(Io=>{"use strict";var O3=Symbol.for("react.transitional.element"),R3=Symbol.for("react.fragment");function cv(e,t,n){var a=null;if(n!==void 0&&(a=""+n),t.key!==void 0&&(a=""+t.key),"key"in t){n={};for(var i in t)i!=="key"&&(n[i]=t[i])}else n=t;return t=n.ref,{$$typeof:O3,type:e,key:a,ref:t!==void 0?t:null,props:n}}Io.Fragment=R3;Io.jsx=cv;Io.jsxs=cv});var za=D((D2,dv)=>{"use strict";dv.exports=fv()});var k3={};Id(k3,{default:()=>tu});module.exports=nu(k3);var I=require("obsidian");var U=require("obsidian");var au={googlePlay:"https://play.google.com/store/apps/details?id=app.readaloudai",appleAppStore:"https://apps.apple.com/app/id6743985203"},Ge={PLAYER_WIDTH:270,PLAYER_HEIGHT:170,QUEUE_WIDTH:340,QUEUE_HEIGHT:400,RIGHT_MARGIN:20,BOTTOM_MARGIN:20},ea={MAX_WORDS:5e3,MAX_CHARACTERS:3e4};var vr=require("obsidian");var It={en:{greaterThan:" greater than ",lessThan:" less than ",greaterThanOrEqual:" greater than or equal to ",lessThanOrEqual:" less than or equal to "},es:{greaterThan:" mayor que ",lessThan:" menor que ",greaterThanOrEqual:" mayor o igual que ",lessThanOrEqual:" menor o igual que "},fr:{greaterThan:" sup\xE9rieur \xE0 ",lessThan:" inf\xE9rieur \xE0 ",greaterThanOrEqual:" sup\xE9rieur ou \xE9gal \xE0 ",lessThanOrEqual:" inf\xE9rieur ou \xE9gal \xE0 "},de:{greaterThan:" gr\xF6\xDFer als ",lessThan:" kleiner als ",greaterThanOrEqual:" gr\xF6\xDFer oder gleich ",lessThanOrEqual:" kleiner oder gleich "},it:{greaterThan:" maggiore di ",lessThan:" minore di ",greaterThanOrEqual:" maggiore o uguale a ",lessThanOrEqual:" minore o uguale a "},pt:{greaterThan:" maior que ",lessThan:" menor que ",greaterThanOrEqual:" maior ou igual a ",lessThanOrEqual:" menor ou igual a "},ru:{greaterThan:" \u0431\u043E\u043B\u044C\u0448\u0435 \u0447\u0435\u043C ",lessThan:" \u043C\u0435\u043D\u044C\u0448\u0435 \u0447\u0435\u043C ",greaterThanOrEqual:" \u0431\u043E\u043B\u044C\u0448\u0435 \u0438\u043B\u0438 \u0440\u0430\u0432\u043D\u043E ",lessThanOrEqual:" \u043C\u0435\u043D\u044C\u0448\u0435 \u0438\u043B\u0438 \u0440\u0430\u0432\u043D\u043E "},ja:{greaterThan:" \u3088\u308A\u5927\u304D\u3044 ",lessThan:" \u3088\u308A\u5C0F\u3055\u3044 ",greaterThanOrEqual:" \u4EE5\u4E0A ",lessThanOrEqual:" \u4EE5\u4E0B "},ko:{greaterThan:" \uBCF4\uB2E4 \uD06C\uB2E4 ",lessThan:" \uBCF4\uB2E4 \uC791\uB2E4 ",greaterThanOrEqual:" \uC774\uC0C1 ",lessThanOrEqual:" \uC774\uD558 "},zh:{greaterThan:" \u5927\u4E8E ",lessThan:" \u5C0F\u4E8E ",greaterThanOrEqual:" \u5927\u4E8E\u7B49\u4E8E ",lessThanOrEqual:" \u5C0F\u4E8E\u7B49\u4E8E "}};function iu(){try{let e=(0,vr.getLanguage)();if(e){let t=e.toLowerCase();if(Object.keys(It).includes(t))return t}}catch(e){console.warn("Failed to get Obsidian language, falling back to browser locale:",e)}try{let t=(typeof navigator!="undefined"?navigator.language:"en").split("-")[0].toLowerCase();return Object.keys(It).includes(t)?t:"en"}catch(e){return console.warn("Failed to detect any language, defaulting to English:",e),"en"}}function kv(e){if(!e.symbolReplacement)return It.en;if(e.symbolReplacement.enableCustomReplacements)return e.symbolReplacement.customReplacements;let t=e.symbolReplacement.language;return t==="auto"&&(t=iu()),It[t]||It.en}function Ie(e,t=!0){if(!t)return e;let n=/^(---|\.\.\.)([\s\S]*?)\1\n?/;return e.replace(n,"").trim()}function Uv(e,t){if(!t||!t.textFiltering.replaceComparisonSymbols)return e.replace(/>=/g,"\u2265").replace(/<=/g,"\u2264");let n=kv(t);return e.split(`
`).map(s=>{if(/^\s*>\s/.test(s)||/<[^>]+>/.test(s)||/<!--.*?-->/.test(s)||/https?:\/\//.test(s)||/<[^@\s]+@[^@\s]+\.[^@\s]+>/.test(s))return s;let r=s,l="";for(;l!==r;)l=r,r=r.replace(/(\w+)\s*>=\s*(\w+)/g,"$1"+n.greaterThanOrEqual+"$2");for(l="";l!==r;)l=r,r=r.replace(/(\w+)\s*<=\s*(\w+)/g,"$1"+n.lessThanOrEqual+"$2");for(l="";l!==r;)l=r,r=r.replace(/(\w+)\s*>\s*(\w+)/g,"$1"+n.greaterThan+"$2");for(l="";l!==r;)l=r,r=r.replace(/(\w+)\s*<\s*(\w+)/g,"$1"+n.lessThan+"$2");return r}).join(`
`)}function Ze(e,t,n){let a=e;if((!t||t.filterFrontmatter)&&(a=a.replace(/^-{3}[\s\S]*?-{3}\n?/,"")),t!=null&&t.filterComments&&(a=a.replace(/<!--[\s\S]*?-->/g,""),a=a.replace(/%%[\s\S]*?%%/g,"")),t!=null&&t.filterMathExpressions&&(a=a.replace(/\$\$[\s\S]*?\$\$/g,""),a=a.replace(/\$[^\n$]+\$/g,"")),(!t||t.filterCodeBlocks)&&(a=a.replace(/```[\s\S]*?```/g,"")),t!=null&&t.filterTables&&(a=a.replace(/^\|.*\|$/gm,""),a=a.replace(/^\|[\s\-\|:]+\|$/gm,"")),t!=null&&t.filterImages&&(a=a.replace(/!\[([^\]]*)\]\([^)]*\)/g,""),a=a.replace(/!\[\[([^\]]*)\]\]/g,"")),t!=null&&t.filterCallouts){a=a.replace(/^>\s*\[![^\]]*\]\s*/gm,"");let r=a.split(`
`),l=!1;a=r.map(u=>u.match(/^>\s*\[![^\]]*\]/)?(l=!0,u.replace(/^>\s*\[![^\]]*\]\s*/,"")):l&&u.startsWith("> ")?u.substring(2):(l&&!u.startsWith(">")&&u.trim()!==""&&(l=!1),u)).join(`
`)}t!=null&&t.filterFootnotes&&(a=a.replace(/\[\^[^\]]+\]/g,""),a=a.replace(/^\[\^[^\]]+\]:\s*.*$/gm,"")),t!=null&&t.filterMarkdownLinks?a=a.replace(/\[([^\]]*)\]\([^)]*\)/g,""):a=a.replace(/\[([^\]]*)\]\([^)]*\)/g,"$1"),t!=null&&t.filterWikiLinks?(a=a.replace(/\[\[([^\]|]*)\|([^\]]*)\]\]/g,"$2"),a=a.replace(/\[\[([^\]]*)\]\]/g,"$1")):(a=a.replace(/\[\[([^\]|]*)\|([^\]]*)\]\]/g,"$2"),a=a.replace(/\[\[([^\]]*)\]\]/g,"$1")),a=a.replace(/https?:\/\/[^\s]+/g,"");let i=a;return i=i.replace(/(\*\*|__)(.*?)\1/g,"$2").replace(/(\*|_)(.*?)\1/g,"$2"),(!t||t.filterInlineCode)&&(i=i.replace(/`([^`]*)`/g,"$1")),t!=null&&t.filterHighlights&&(i=i.replace(/==([^=]*)==/g,"$1")),i=i.replace(/~~(.*?)~~/g,"$1"),i=i.replace(/^[#*-]+\s*/gm,"").replace(/^[\-\+\*]\s+/gm,"").replace(/^\d+\.\s+/gm,"").replace(/^>\s+/gm,"").replace(/^[-*]{3,}\s*$/gm,""),i=Uv(i,{textFiltering:t,symbolReplacement:n}),(!t||t.filterHtmlTags)&&(i=i.replace(/<([^>\s]+)[^>]*>/g,"")),i=i.replace(/\n{3,}/g,`

`).replace(/ {2,}/g," ").trim(),i}function Zt(e){let t=e.trim(),n=t.split(/\s+/).filter(y=>y.length>0),a=n.length,i=t.length,s=a>ea.MAX_WORDS,r=i>ea.MAX_CHARACTERS;if(!s&&!r)return{content:t,wasTruncated:!1,originalWordCount:a,originalCharCount:i,finalWordCount:a,finalCharCount:i};let l=a/ea.MAX_WORDS,o=i/ea.MAX_CHARACTERS,u=l>o?"words":"characters",c;if(u==="words"){let y=ea.MAX_WORDS-10;c=n.slice(0,y).join(" ")}else{let y=ea.MAX_CHARACTERS-50;c=t.substring(0,y)}c=Nv(c);let d=c.split(/\s+/).filter(y=>y.length>0).length,m=c.length;return{content:c,wasTruncated:!0,originalWordCount:a,originalCharCount:i,finalWordCount:d,finalCharCount:m,truncationReason:u}}function Nv(e){let t=/[.!?]\s+/g,n=Array.from(e.matchAll(t));if(n.length>0){let r=n[n.length-1],l=r.index+r[0].length-1;return e.substring(0,l)}let a=/\n\s*\n/g,i=Array.from(e.matchAll(a));if(i.length>0){let r=i[i.length-1];return e.substring(0,r.index)}let s=e.lastIndexOf(" ");return s>e.length*.8?e.substring(0,s):e}function ta(e){return!(!e.showNotices||vr.Platform.isMobile&&e.reducedNoticesOnMobile)}var Pv=Kd(),Lv=Wd(),zv=["en-US-AvaMultilingualNeural","en-US-BrianMultilingualNeural","en-US-AndrewNeural","en-US-AriaNeural","en-US-AvaNeural","en-US-ChristopherNeural","en-US-SteffanNeural","en-IE-ConnorNeural","en-GB-RyanNeural","en-GB-SoniaNeural","en-AU-NatashaNeural","en-AU-WilliamNeural"],na={selectedVoice:"en-US-AvaNeural",customVoice:"",playbackSpeed:1,showNotices:!0,showStatusBarButton:!0,showMenuItems:!0,generateMP3:!1,outputFolder:"Note Narration Audio",embedInNote:!1,replaceSpacesInFilenames:!1,textFiltering:{filterFrontmatter:!0,filterMarkdownLinks:!1,filterCodeBlocks:!0,filterInlineCode:!0,filterHtmlTags:!0,filterTables:!0,filterImages:!0,filterFootnotes:!0,filterComments:!0,filterMathExpressions:!0,filterWikiLinks:!1,filterHighlights:!0,filterCallouts:!1,replaceComparisonSymbols:!0},symbolReplacement:{enableCustomReplacements:!1,language:"auto",customReplacements:{greaterThan:" greater than ",lessThan:" less than ",greaterThanOrEqual:" greater than or equal to ",lessThanOrEqual:" less than or equal to "}},floatingPlayerPosition:null,disablePlaybackControlPopover:!1,enableReplayOption:!0,enableQueueFeature:!0,queueManagerPosition:null,autoPauseOnWindowBlur:!1,chunkSize:9e3,enableExperimentalFeatures:!1,reducedNoticesOnMobile:!0},su="note",Sr=class extends U.PluginSettingTab{constructor(n,a){super(n,a);this.plugin=a}async display(){let{containerEl:n}=this;n.empty();let a=n.createEl("div",{cls:"edge-tts-info-div"}),i=document.createElement("p"),s=document.createElement("a");s.href="https://tts.travisvn.com",s.text="tts.travisvn.com",i.append("You can sample available voices at "),i.append(s),a.appendChild(i),new U.Setting(n).setName("Select voice").setDesc("Choose from the top voices.").setClass("default-style").addDropdown(v=>{zv.forEach(T=>{v.addOption(T,T)}),v.setValue(this.plugin.settings.selectedVoice),v.onChange(async T=>{this.plugin.settings.selectedVoice=T,await this.plugin.saveSettings()})});let r=document.createDocumentFragment(),l=document.createElement("a");if(l.href="https://tts.travisvn.com",l.text="tts.travisvn.com",r.append("(OPTIONAL) Enter custom voice. Visit "),r.append(l),r.append(" for list of options. "),r.append("Leave empty to use the selected voice above."),new U.Setting(n).setName("Custom voice").setDesc(r).addText(v=>{v.setPlaceholder("e.g., fr-FR-HenriNeural"),v.setValue(this.plugin.settings.customVoice),v.onChange(async T=>{this.plugin.settings.customVoice=T,await this.plugin.saveSettings()})}),new U.Setting(n).setName("Playback speed").setDesc("Change playback speed multiplier (ex. 0.5 = 0.5x playback speed (50% speed)). Default = 1.0").addSlider(v=>{v.setLimits(.5,2,.1),v.setValue(this.plugin.settings.playbackSpeed),v.onChange(async T=>{this.plugin.settings.playbackSpeed=T,await this.plugin.saveSettings()}),v.setDynamicTooltip(),v.showTooltip()}),new U.Setting(n).setName("Show notices").setDesc("Toggle notices for processing status and errors.").addToggle(v=>{v.setValue(this.plugin.settings.showNotices),v.onChange(async T=>{this.plugin.settings.showNotices=T,await this.plugin.saveSettings()})}),new U.Setting(n).setName("Show status bar button").setDesc("Toggle playback button in the status bar.").addToggle(v=>{v.setValue(this.plugin.settings.showStatusBarButton),v.onChange(async T=>{this.plugin.settings.showStatusBarButton=T,await this.plugin.saveSettings(),T?this.plugin.uiManager.initializeStatusBar():this.plugin.uiManager.removeStatusBarButton()})}),new U.Setting(n).setName("Show file and editor menu items").setDesc("Toggle menu items in the file and editor menus.").addToggle(v=>{v.setValue(this.plugin.settings.showMenuItems),v.onChange(async T=>{this.plugin.settings.showMenuItems=T,await this.plugin.saveSettings(),T?this.plugin.uiManager.addPluginMenuItems():new U.Notice("Menu items will be removed after the next reload.")})}),new U.Setting(n).setName("Disable floating playback controls").setDesc("Hide the floating playback control popover during audio playback.").addToggle(v=>{v.setValue(this.plugin.settings.disablePlaybackControlPopover),v.onChange(async T=>{this.plugin.settings.disablePlaybackControlPopover=T,await this.plugin.saveSettings(),new U.Notice(`Floating playback controls ${T?"disabled":"enabled"}.`)})}),new U.Setting(n).setName("Enable replay option").setDesc("Keep playback controls open after audio finishes to allow replaying.").addToggle(v=>{v.setValue(this.plugin.settings.enableReplayOption),v.onChange(async T=>{this.plugin.settings.enableReplayOption=T,await this.plugin.saveSettings(),new U.Notice(`Replay option ${T?"enabled":"disabled"}.`)})}),new U.Setting(n).setName("Enable queue feature").setDesc("Enable the playback queue functionality including queue manager and queue-related commands.").addToggle(v=>{v.setValue(this.plugin.settings.enableQueueFeature),v.onChange(async T=>{this.plugin.settings.enableQueueFeature=T,await this.plugin.saveSettings(),new U.Notice(`Queue feature ${T?"enabled":"disabled"}. Restart Obsidian to fully apply changes.`)})}),new U.Setting(n).setName("Auto-pause on window focus loss").setDesc("Automatically pause playback when Obsidian loses focus and resume when it regains focus.").addToggle(v=>{v.setValue(this.plugin.settings.autoPauseOnWindowBlur),v.onChange(async T=>{this.plugin.settings.autoPauseOnWindowBlur=T,await this.plugin.saveSettings(),new U.Notice(`Auto-pause on focus loss ${T?"enabled":"disabled"}.`)})}),n.createEl("h3",{text:"Saving .mp3 of narration"}),U.Platform.isMobile){let v=n.createEl("div",{cls:"edge-tts-info-div"}),T=document.createElement("p");T.style.fontSize="13px",T.style.color="var(--text-muted)",T.innerHTML=`
        <strong>Note:</strong> MP3 file generation is not available on mobile devices due to file system limitations. 
        However, you can still use the audio playback feature to listen to your notes.
      `,v.appendChild(T)}else{let v=n.createEl("div",{cls:"edge-tts-info-div"}),T=document.createElement("p");T.style.fontSize="13px",T.style.color="var(--text-muted)",T.innerHTML=`
        <strong>Note:</strong> For long notes (over ~1500 words or 9000 characters), MP3 generation will automatically use 
        a chunked approach. This splits the text into smaller parts, generates audio for each part, then combines them. 
        A progress indicator will show the status of each chunk during generation.
      `,v.appendChild(T),new U.Setting(n).setName("Generate MP3 file").setDesc('Enable option to select "Generate MP3" in the file and editor menus.').addToggle(P=>{P.setValue(this.plugin.settings.generateMP3),P.onChange(async ee=>{this.plugin.settings.generateMP3=ee,await this.plugin.saveSettings(),ee||new U.Notice("Menu items will be removed after the next reload.")})}),new U.Setting(n).setName("Output folder").setDesc("Specify the folder to save generated MP3 files.").addText(P=>{P.setPlaceholder("e.g., Note Narration Audio").setValue(this.plugin.settings.outputFolder).onChange(async ee=>{this.plugin.settings.outputFolder=ee.trim(),await this.plugin.saveSettings()})}),new U.Setting(n).setName("Embed MP3 in note").setDesc("Embed a link to the generated MP3 file in the note.").addToggle(P=>{P.setValue(this.plugin.settings.embedInNote),P.onChange(async ee=>{this.plugin.settings.embedInNote=ee,await this.plugin.saveSettings()})}),new U.Setting(n).setName("Replace spaces in filenames").setDesc("Replaces spaces in mp3 file name with underscores (used for system compatibility).").addToggle(P=>{P.setValue(this.plugin.settings.replaceSpacesInFilenames),P.onChange(async ee=>{this.plugin.settings.replaceSpacesInFilenames=ee,await this.plugin.saveSettings()})})}let o=n.createDiv({cls:"edge-tts-mobile-app-section"});o.createEl("h3",{text:"\u{1F4F1} Mobile support"}),o.createEl("p",{text:"Create audio narration for your Obsidian notes using our free mobile app."});let u=o.createDiv({cls:"edge-tts-app-store-buttons"}),c=u.createEl("a",{cls:"edge-tts-app-store-button",href:au.googlePlay,attr:{target:"_blank",rel:"noopener"}});c.innerHTML=Pv;let f=c.querySelector("svg");f&&f.addClass("edge-tts-app-store-icon"),c.createSpan({text:"Google Play"});let d=u.createEl("a",{cls:"edge-tts-app-store-button",href:au.appleAppStore,attr:{target:"_blank",rel:"noopener"}});d.innerHTML=Lv;let m=d.querySelector("svg");m&&m.addClass("edge-tts-app-store-icon"),d.createSpan({text:"App Store"});let y=n.createEl("div",{cls:"edge-tts-star-section"});y.createEl("p",{text:"Please star this project on GitHub if you find it useful \u2B50\uFE0F",cls:"edge-tts-star-message"}),y.createEl("a",{text:"GitHub: Edge TTS Plugin",href:"https://github.com/travisvn/obsidian-edge-tts",cls:"external-link",attr:{target:"_blank",rel:"noopener"}}),n.createEl("h3",{text:"Advanced settings"}),new U.Setting(n).setName("Enable experimental features").setDesc("Enable access to experimental features that may not be fully stable. These features are gated behind this toggle for safety.").addToggle(v=>{v.setValue(this.plugin.settings.enableExperimentalFeatures),v.onChange(async T=>{this.plugin.settings.enableExperimentalFeatures=T,await this.plugin.saveSettings(),new U.Notice(`Experimental features ${T?"enabled":"disabled"}.`)})}),U.Platform.isMobile&&new U.Setting(n).setName("Reduced notices on mobile").setDesc("Show fewer notification popups on mobile devices to reduce screen clutter. Disable this for more verbose feedback.").addToggle(v=>{v.setValue(this.plugin.settings.reducedNoticesOnMobile),v.onChange(async T=>{this.plugin.settings.reducedNoticesOnMobile=T,await this.plugin.saveSettings(),new U.Notice(`Mobile notices ${T?"reduced":"verbose"}.`)})});let E=n.createEl("div",{cls:"setting-item setting-item-heading edge-tts-collapsible-header",attr:{style:"cursor: pointer; user-select: none;"}}),M=E.createEl("div",{cls:"setting-item-info"}),g=M.createEl("div",{cls:"setting-item-name"}),h=g.createEl("span",{text:"\u25B6 ",attr:{style:"display: inline-block; transition: transform 0.2s ease; margin-right: 8px;"}});g.createSpan({text:"Text filtering"}),M.createEl("div",{cls:"setting-item-description",text:"Configure what content is filtered from notes before speech generation. Click to expand options."});let p=n.createEl("div",{attr:{style:"display: none; margin-left: 24px; border-left: 2px solid var(--background-modifier-border); padding-left: 16px; margin-top: 8px;"}}),S=!1;E.addEventListener("click",()=>{S=!S,p.style.display=S?"block":"none",h.style.transform=S?"rotate(90deg)":"rotate(0deg)"});let A=p.createEl("div",{cls:"edge-tts-info-div"}),_=document.createElement("p");_.style.fontSize="13px",_.style.color="var(--text-muted)",_.innerHTML=`
      <strong>Text filtering</strong> controls what content is removed from your notes before generating speech. 
      This helps ensure clean, readable narration by filtering out formatting elements that don't translate well to speech.
    `,A.appendChild(_),new U.Setting(p).setName("Filter frontmatter").setDesc("Remove YAML frontmatter (metadata between --- delimiters) from the beginning of notes.").addToggle(v=>{v.setValue(this.plugin.settings.textFiltering.filterFrontmatter),v.onChange(async T=>{this.plugin.settings.textFiltering.filterFrontmatter=T,await this.plugin.saveSettings()})}),new U.Setting(p).setName("Filter markdown links").setDesc("Remove markdown links [text](url) completely. When enabled, both the link text and URL are removed.").addToggle(v=>{v.setValue(this.plugin.settings.textFiltering.filterMarkdownLinks),v.onChange(async T=>{this.plugin.settings.textFiltering.filterMarkdownLinks=T,await this.plugin.saveSettings()})}),new U.Setting(p).setName("Filter wiki links").setDesc("Remove Obsidian wiki-style links [[link]] and keep only the display text.").addToggle(v=>{v.setValue(this.plugin.settings.textFiltering.filterWikiLinks),v.onChange(async T=>{this.plugin.settings.textFiltering.filterWikiLinks=T,await this.plugin.saveSettings()})}),new U.Setting(p).setName("Filter code blocks").setDesc("Remove fenced code blocks (```code```) completely.").addToggle(v=>{v.setValue(this.plugin.settings.textFiltering.filterCodeBlocks),v.onChange(async T=>{this.plugin.settings.textFiltering.filterCodeBlocks=T,await this.plugin.saveSettings()})}),new U.Setting(p).setName("Filter inline code").setDesc("Remove backtick markers from inline code (`code`) while keeping the code text.").addToggle(v=>{v.setValue(this.plugin.settings.textFiltering.filterInlineCode),v.onChange(async T=>{this.plugin.settings.textFiltering.filterInlineCode=T,await this.plugin.saveSettings()})}),new U.Setting(p).setName("Filter tables").setDesc("Remove markdown tables completely.").addToggle(v=>{v.setValue(this.plugin.settings.textFiltering.filterTables),v.onChange(async T=>{this.plugin.settings.textFiltering.filterTables=T,await this.plugin.saveSettings()})}),new U.Setting(p).setName("Filter images").setDesc("Remove image embeds ![alt](url) and attachments ![[image.png]].").addToggle(v=>{v.setValue(this.plugin.settings.textFiltering.filterImages),v.onChange(async T=>{this.plugin.settings.textFiltering.filterImages=T,await this.plugin.saveSettings()})}),new U.Setting(p).setName("Filter callouts").setDesc("Remove Obsidian callout blocks (> [!note], > [!warning], etc.) while keeping the content.").addToggle(v=>{v.setValue(this.plugin.settings.textFiltering.filterCallouts),v.onChange(async T=>{this.plugin.settings.textFiltering.filterCallouts=T,await this.plugin.saveSettings()})}),new U.Setting(p).setName("Filter highlights").setDesc("Remove highlight markers ==text== while keeping the highlighted text.").addToggle(v=>{v.setValue(this.plugin.settings.textFiltering.filterHighlights),v.onChange(async T=>{this.plugin.settings.textFiltering.filterHighlights=T,await this.plugin.saveSettings()})}),new U.Setting(p).setName("Filter footnotes").setDesc("Remove footnote references [^1] and footnote definitions.").addToggle(v=>{v.setValue(this.plugin.settings.textFiltering.filterFootnotes),v.onChange(async T=>{this.plugin.settings.textFiltering.filterFootnotes=T,await this.plugin.saveSettings()})}),new U.Setting(p).setName("Filter comments").setDesc("Remove HTML comments <!-- comment --> and Obsidian comments %%comment%%.").addToggle(v=>{v.setValue(this.plugin.settings.textFiltering.filterComments),v.onChange(async T=>{this.plugin.settings.textFiltering.filterComments=T,await this.plugin.saveSettings()})}),new U.Setting(p).setName("Filter math expressions").setDesc("Remove LaTeX math expressions ($inline$ and $$block$$).").addToggle(v=>{v.setValue(this.plugin.settings.textFiltering.filterMathExpressions),v.onChange(async T=>{this.plugin.settings.textFiltering.filterMathExpressions=T,await this.plugin.saveSettings()})}),new U.Setting(p).setName("Filter HTML tags").setDesc("Remove HTML tags while preserving the text content.").addToggle(v=>{v.setValue(this.plugin.settings.textFiltering.filterHtmlTags),v.onChange(async T=>{this.plugin.settings.textFiltering.filterHtmlTags=T,await this.plugin.saveSettings()})}),new U.Setting(p).setName("Replace comparison symbols").setDesc("Replace < and > symbols with words to prevent XML parsing issues with multiple symbols on the same line. Language and text can be customized in Symbol Replacement section below.").addToggle(v=>{v.setValue(this.plugin.settings.textFiltering.replaceComparisonSymbols),v.onChange(async T=>{this.plugin.settings.textFiltering.replaceComparisonSymbols=T,await this.plugin.saveSettings()})});let O=n.createEl("div",{cls:"setting-item setting-item-heading edge-tts-collapsible-header",attr:{style:"cursor: pointer; user-select: none;"}}),R=O.createEl("div",{cls:"setting-item-info"}),q=R.createEl("div",{cls:"setting-item-name"}),k=q.createEl("span",{text:"\u25B6 ",attr:{style:"display: inline-block; transition: transform 0.2s ease; margin-right: 8px;"}});q.createSpan({text:"Symbol replacement"}),R.createEl("div",{cls:"setting-item-description",text:"Configure how comparison symbols are replaced with words in different languages. Click to expand options."});let b=n.createEl("div",{attr:{style:"display: none; margin-left: 24px; border-left: 2px solid var(--background-modifier-border); padding-left: 16px; margin-top: 8px;"}}),x=!1;O.addEventListener("click",()=>{x=!x,b.style.display=x?"block":"none",k.style.transform=x?"rotate(90deg)":"rotate(0deg)"});let B=b.createEl("div",{cls:"edge-tts-info-div"}),$=document.createElement("p");if($.style.fontSize="13px",$.style.color="var(--text-muted)",$.innerHTML=`
      <strong>Symbol replacement</strong> converts comparison symbols like &gt; and &lt; into words 
      to prevent XML parsing issues in TTS generation. You can choose from built-in languages or 
      create custom replacements.
    `,B.appendChild($),new U.Setting(b).setName("Language").setDesc('Choose the language for symbol replacement words. "Auto" detects from your Obsidian interface language, with browser locale as fallback.').addDropdown(v=>{v.addOption("auto","Auto-detect"),v.addOption("en","English"),v.addOption("es","Espa\xF1ol"),v.addOption("fr","Fran\xE7ais"),v.addOption("de","Deutsch"),v.addOption("it","Italiano"),v.addOption("pt","Portugu\xEAs"),v.addOption("ru","\u0420\u0443\u0441\u0441\u043A\u0438\u0439"),v.addOption("ja","\u65E5\u672C\u8A9E"),v.addOption("ko","\uD55C\uAD6D\uC5B4"),v.addOption("zh","\u4E2D\u6587"),v.setValue(this.plugin.settings.symbolReplacement.language),v.onChange(async T=>{this.plugin.settings.symbolReplacement.language=T,await this.plugin.saveSettings(),!this.plugin.settings.symbolReplacement.enableCustomReplacements&&T!=="custom"&&this.display()})}),new U.Setting(b).setName("Use custom replacements").setDesc("Enable to define your own replacement text instead of using built-in language translations.").addToggle(v=>{v.setValue(this.plugin.settings.symbolReplacement.enableCustomReplacements),v.onChange(async T=>{this.plugin.settings.symbolReplacement.enableCustomReplacements=T,await this.plugin.saveSettings(),this.display()})}),this.plugin.settings.symbolReplacement.enableCustomReplacements)new U.Setting(b).setName("Greater than (>)").setDesc('Text to replace ">" symbols with (e.g., " greater than ", " maior que ")').addText(v=>{v.setPlaceholder(" greater than ").setValue(this.plugin.settings.symbolReplacement.customReplacements.greaterThan).onChange(async T=>{this.plugin.settings.symbolReplacement.customReplacements.greaterThan=T,await this.plugin.saveSettings()})}),new U.Setting(b).setName("Less than (<)").setDesc('Text to replace "<" symbols with (e.g., " less than ", " menor que ")').addText(v=>{v.setPlaceholder(" less than ").setValue(this.plugin.settings.symbolReplacement.customReplacements.lessThan).onChange(async T=>{this.plugin.settings.symbolReplacement.customReplacements.lessThan=T,await this.plugin.saveSettings()})}),new U.Setting(b).setName("Greater than or equal (>=)").setDesc('Text to replace ">=" symbols with (e.g., " greater than or equal to ")').addText(v=>{v.setPlaceholder(" greater than or equal to ").setValue(this.plugin.settings.symbolReplacement.customReplacements.greaterThanOrEqual).onChange(async T=>{this.plugin.settings.symbolReplacement.customReplacements.greaterThanOrEqual=T,await this.plugin.saveSettings()})}),new U.Setting(b).setName("Less than or equal (<=)").setDesc('Text to replace "<=" symbols with (e.g., " less than or equal to ")').addText(v=>{v.setPlaceholder(" less than or equal to ").setValue(this.plugin.settings.symbolReplacement.customReplacements.lessThanOrEqual).onChange(async T=>{this.plugin.settings.symbolReplacement.customReplacements.lessThanOrEqual=T,await this.plugin.saveSettings()})});else{let v=this.plugin.settings.symbolReplacement.language;v==="auto"&&(v=iu());let T=It[v]||It.en,ee=new U.Setting(b).setName("Current translations").setDesc(`Preview of how symbols will be replaced in ${v==="auto"?"auto-detected":v} language:`).settingEl.createEl("div",{attr:{style:"margin-top: 8px; padding: 8px; background-color: var(--background-secondary); border-radius: 4px; font-family: monospace; font-size: 12px;"}});ee.innerHTML=`
        <div>></span> \u2192 "<strong>${T.greaterThan.trim()}</strong>"</div>
        <div>&lt; \u2192 "<strong>${T.lessThan.trim()}</strong>"</div>
        <div>>= \u2192 "<strong>${T.greaterThanOrEqual.trim()}</strong>"</div>
        <div>&lt;= \u2192 "<strong>${T.lessThanOrEqual.trim()}</strong>"</div>
      `}n.createEl("h3",{text:"Extra settings"}),new U.Setting(n).setName("Chunk size for long notes").setDesc("Maximum characters per chunk when generating MP3 for long notes. Smaller chunks may be more reliable but take longer. Default: 9000").addSlider(v=>{v.setLimits(5e3,15e3,1e3),v.setValue(this.plugin.settings.chunkSize),v.onChange(async T=>{this.plugin.settings.chunkSize=T,await this.plugin.saveSettings()}),v.setDynamicTooltip(),v.showTooltip()})}};var j=require("obsidian");var Rg=require("obsidian"),kw=Rg.Platform.isMobile,l2=!kw&&typeof window!="undefined"&&window.require,ml;typeof Buffer=="undefined"&&typeof window!="undefined"&&(ml={from:e=>e instanceof ArrayBuffer?new Uint8Array(e):e instanceof Uint8Array?e:typeof e=="string"?new TextEncoder().encode(e):new Uint8Array(0),concat:e=>{let t=e.reduce((i,s)=>i+s.length,0),n=new Uint8Array(t),a=0;for(let i of e)n.set(i,a),a+=i.length;return n}},globalThis.Buffer=ml);var zt;try{try{zt=dh(),console.log("Loaded edge-tts-universal/browser (Node.js-free)")}catch(e){console.warn("Could not load 'edge-tts-universal/browser', trying isomorphic fallback.",e);try{zt=ym(),console.log("Loaded edge-tts-universal/isomorphic as fallback")}catch(t){console.warn("Could not load 'edge-tts-universal/isomorphic', falling back to main entry point.",t),zt=Og(),console.log("Loaded edge-tts-universal main entry point as final fallback")}}}catch(e){console.error("Failed to import edge-tts-universal package:",e),zt=null}var en=(zt==null?void 0:zt.OUTPUT_FORMAT)||{AUDIO_24KHZ_48KBITRATE_MONO_MP3:"audio-24khz-48kbitrate-mono-mp3",WEBM_24KHZ_16BIT_MONO_OPUS:"webm-24khz-16bit-mono-opus"};function Uw(e){let t={};if(e!==void 0){let n=Math.round((e-1)*100);n!==0&&(t.rate=n>0?`+${n}%`:`${n}%`)}return t}var Jt=class{constructor(){if(!zt)throw new Error("edge-tts-universal package not available");if(this.CommunicateClass=zt.Communicate||zt.IsomorphicCommunicate,!this.CommunicateClass)throw new Error("No suitable Communicate class found in the loaded edge-tts-universal package. Expected Communicate or IsomorphicCommunicate.")}async setMetadata(t,n){this.currentVoice=t,this.currentFormat=n}toStream(t,n){if(!this.CommunicateClass)throw new Error("TTS client not initialized");let a={};if(n&&typeof n.rate=="number"){let i=Uw(n.rate);a.rate=i.rate}try{let i={voice:this.currentVoice};a.rate&&(i.rate=a.rate),a.pitch&&(i.pitch=a.pitch),a.volume&&(i.volume=a.volume);let s;try{s=new this.CommunicateClass(t,i)}catch(o){console.warn("Failed to create Communicate with prosody options, trying with voice only:",o);let u={voice:this.currentVoice};s=new this.CommunicateClass(t,u)}let r=s.stream();return{listeners:new Map,on(o,u){this.listeners.has(o)||this.listeners.set(o,[]),this.listeners.get(o).push(u),o==="data"&&!this.isConsuming&&this.consumeAsyncGenerator()},emit(o,...u){(this.listeners.get(o)||[]).forEach(f=>f(...u))},isConsuming:!1,async consumeAsyncGenerator(){this.isConsuming=!0;try{for await(let o of r)if(o.type==="audio"&&o.data){let u;o.data instanceof Uint8Array?u=o.data:o.data instanceof ArrayBuffer?u=new Uint8Array(o.data):typeof Buffer!="undefined"&&Buffer.isBuffer&&Buffer.isBuffer(o.data)?u=new Uint8Array(o.data):ml&&typeof o.data=="object"?u=ml.from(o.data):(console.warn("Unexpected audio data type:",typeof o.data,o.data),u=new Uint8Array(0)),this.emit("data",u)}this.emit("end")}catch(o){console.error("TTS stream: error consuming generator:",o),this.emit("error",o)}}}}catch(i){throw console.error("Error creating TTS stream:",i),new Error(`Failed to create TTS stream: ${i instanceof Error?i.message:"Unknown error"}`)}}};var dc=class{constructor(){}},gl=class{constructor(t,n,a,i,s,r,l){this.isPaused=!1;this.currentPlaybackId=0;this.wasPlayingBeforeBlur=!1;this.playbackQueue=[];this.currentQueueIndex=-1;this.isPlayingFromQueue=!1;this.loopEnabled=!1;this.sleepTimerTimeout=null;this.sleepTimerMinutes=0;this.mediaSource=null;this.sourceBuffer=null;this.completeMp3BufferArray=[];this.mseAudioQueue=[];this.isAppendingBuffer=!1;this.isStreamingWithMSE=!1;this.isSwitchingToFullFile=!1;this.streamedPlaybackTimeBeforeSwitch=0;this.mediaSessionSupported=!1;this.settings=t,this.updateStatusBarCallback=n,this.showFloatingPlayerCallback=a,this.hideFloatingPlayerCallback=i,this.updateFloatingPlayerCallback=s,this.fileManager=r,this.app=l,this.audioElement=new Audio,this.audioElement.preload="auto",this.setupAudioEventListeners(),this.setupAutoPauseListeners(),this.initializeMediaSession()}setupAudioEventListeners(){this.audioElement.onloadedmetadata=()=>{if(!(this.isStreamingWithMSE&&!this.isSwitchingToFullFile)){if(this.isSwitchingToFullFile){this.audioElement.currentTime=this.streamedPlaybackTimeBeforeSwitch,this.isSwitchingToFullFile=!1,this.isPaused||this.audioElement.play().catch(t=>console.error("Error playing after source switch:",t)),this.updateFloatingPlayerCallback({currentTime:this.audioElement.currentTime,duration:this.audioElement.duration,isPlaying:!this.audioElement.paused,isLoading:!1});return}this.updateFloatingPlayerCallback({currentTime:this.audioElement.currentTime,duration:this.audioElement.duration,isPlaying:!this.audioElement.paused,isLoading:!1}),this.updateMediaSessionMetadata(this.getCurrentAudioTitle(),this.audioElement.duration)}},this.audioElement.ontimeupdate=()=>{var t;this.settings.disablePlaybackControlPopover||this.updateFloatingPlayerCallback({currentTime:this.audioElement.currentTime,duration:this.isStreamingWithMSE&&!((t=this.mediaSource)!=null&&t.duration)?1/0:this.audioElement.duration,isPlaying:!this.audioElement.paused,isLoading:!1}),this.updateMediaSessionPosition()},this.audioElement.onended=()=>{if(this.isStreamingWithMSE&&this.mediaSource&&this.mediaSource.readyState==="ended"){console.log("MSE stream ended."),this.settings.enableReplayOption&&!this.settings.disablePlaybackControlPopover?(this.isPaused=!0,this.updateFloatingPlayerCallback({currentTime:this.audioElement.currentTime,duration:this.audioElement.duration,isPlaying:!1,isLoading:!1})):(this.resetPlaybackStateAndHidePlayer(),this.updateStatusBarCallback(!1));return}if(ta(this.settings)&&new j.Notice("Finished reading aloud."),this.isPlayingFromQueue){setTimeout(()=>this.playNextInQueue(),1e3);return}this.settings.enableReplayOption&&!this.settings.disablePlaybackControlPopover?(this.isPaused=!0,this.updateStatusBarCallback(!1),this.settings.disablePlaybackControlPopover||this.updateFloatingPlayerCallback({currentTime:this.audioElement.duration,duration:this.audioElement.duration,isPlaying:!1,isLoading:!1})):(this.resetPlaybackStateAndHidePlayer(),this.updateStatusBarCallback(!1))},this.audioElement.onpause=()=>{var t;this.isPaused=!0,this.updateStatusBarCallback(!0),this.settings.disablePlaybackControlPopover||this.updateFloatingPlayerCallback({currentTime:this.audioElement.currentTime,duration:this.isStreamingWithMSE&&!((t=this.mediaSource)!=null&&t.duration)?1/0:this.audioElement.duration,isPlaying:!1,isLoading:!1}),this.mediaSessionSupported&&navigator.mediaSession&&(navigator.mediaSession.playbackState="paused")},this.audioElement.onplay=()=>{var t;this.isPaused=!1,this.updateStatusBarCallback(!0),this.settings.disablePlaybackControlPopover||this.updateFloatingPlayerCallback({currentTime:this.audioElement.currentTime,duration:this.isStreamingWithMSE&&!((t=this.mediaSource)!=null&&t.duration)?1/0:this.audioElement.duration,isPlaying:!0,isLoading:!1}),this.mediaSessionSupported&&navigator.mediaSession&&(navigator.mediaSession.playbackState="playing",this.isAndroid()?(this.setupMediaSessionHandlers(),setTimeout(()=>{this.updateMediaSessionMetadata(this.getCurrentAudioTitle(),this.audioElement.duration)},300)):setTimeout(()=>{this.updateMediaSessionMetadata(this.getCurrentAudioTitle(),this.audioElement.duration)},100))}}setupAutoPauseListeners(){window.addEventListener("blur",()=>{this.settings.autoPauseOnWindowBlur&&!this.audioElement.paused&&(this.wasPlayingBeforeBlur=!0,this.pausePlayback())}),window.addEventListener("focus",()=>{this.settings.autoPauseOnWindowBlur&&this.wasPlayingBeforeBlur&&this.audioElement.paused&&(this.wasPlayingBeforeBlur=!1,this.resumePlayback())})}initializeMediaSession(){if(!this.settings.enableExperimentalFeatures){console.log("Media Session: Experimental features disabled");return}typeof window!="undefined"&&"mediaSession"in navigator?(this.mediaSessionSupported=!0,console.log("Media Session: API supported, setting up handlers"),this.isAndroid()?(console.log("Media Session: Android detected, using delayed initialization"),setTimeout(()=>{this.setupMediaSessionHandlers()},500)):this.setupMediaSessionHandlers()):console.log("Media Session: API not supported in this browser")}setupMediaSessionHandlers(){if(!(!this.mediaSessionSupported||!navigator.mediaSession))try{navigator.mediaSession.setActionHandler("play",()=>{this.resumePlayback()}),navigator.mediaSession.setActionHandler("pause",()=>{this.pausePlayback()}),navigator.mediaSession.setActionHandler("stop",()=>{this.stopPlayback()}),navigator.mediaSession.setActionHandler("seekbackward",t=>{let n=t.seekOffset||10;this.jumpBackward(n)}),navigator.mediaSession.setActionHandler("seekforward",t=>{let n=t.seekOffset||10;this.jumpForward(n)}),navigator.mediaSession.setActionHandler("seekto",t=>{t.seekTime!==void 0&&this.seekPlayback(t.seekTime)}),this.settings.enableQueueFeature&&(navigator.mediaSession.setActionHandler("previoustrack",()=>{this.isPlayingFromQueue&&this.currentQueueIndex>0&&this.playQueueItem(this.currentQueueIndex-1)}),navigator.mediaSession.setActionHandler("nexttrack",()=>{this.isPlayingFromQueue&&this.currentQueueIndex<this.playbackQueue.length-1&&this.playQueueItem(this.currentQueueIndex+1)}))}catch(t){console.warn("Failed to set up Media Session handlers:",t)}}updateMediaSessionMetadata(t,n){if(!(!this.mediaSessionSupported||!navigator.mediaSession))try{let a={title:t||"Edge TTS Audio",artist:"Obsidian Edge TTS",album:"Text-to-Speech"};if(this.isPlayingFromQueue&&this.playbackQueue.length>0){let s=` (${this.currentQueueIndex+1}/${this.playbackQueue.length})`;a.title=(t||"Queue Item")+s}console.log("Media Session: Setting metadata:",a),navigator.mediaSession.metadata=new MediaMetadata(a);let i=this.audioElement.paused?"paused":"playing";console.log("Media Session: Setting playback state:",i),navigator.mediaSession.playbackState=i,n&&n!==1/0&&(console.log("Media Session: Setting position state:",{duration:n,position:this.audioElement.currentTime}),this.isAndroid()?setTimeout(()=>{var s;try{(s=navigator.mediaSession)==null||s.setPositionState({duration:n,playbackRate:this.audioElement.playbackRate,position:this.audioElement.currentTime})}catch(r){console.warn("Android position state failed:",r)}},100):navigator.mediaSession.setPositionState({duration:n,playbackRate:this.audioElement.playbackRate,position:this.audioElement.currentTime}))}catch(a){console.warn("Failed to update Media Session metadata:",a)}}updateMediaSessionPosition(){if(!(!this.mediaSessionSupported||!navigator.mediaSession))try{let t=this.audioElement.duration;t&&t!==1/0&&!isNaN(t)&&navigator.mediaSession.setPositionState({duration:t,playbackRate:this.audioElement.playbackRate,position:this.audioElement.currentTime})}catch(t){}}clearMediaSession(){if(!(!this.mediaSessionSupported||!navigator.mediaSession))try{navigator.mediaSession.metadata=null,navigator.mediaSession.playbackState="none"}catch(t){console.warn("Failed to clear Media Session:",t)}}getCurrentAudioTitle(){return this.isPlayingFromQueue&&this.currentQueueIndex>=0&&this.playbackQueue[this.currentQueueIndex]?this.playbackQueue[this.currentQueueIndex].title||"Queue Item":"Edge TTS Audio"}isAndroid(){return/Android/i.test(navigator.userAgent)}appendNextChunkToSourceBuffer(){if(this.isAppendingBuffer||!this.sourceBuffer||this.sourceBuffer.updating||this.mseAudioQueue.length===0)return;if(this.mediaSource&&this.mediaSource.readyState==="ended"){this.isAppendingBuffer=!1;return}this.isAppendingBuffer=!0;let t=this.mseAudioQueue.shift();try{this.sourceBuffer.appendBuffer(t)}catch(n){if(console.error("Error appending buffer:",n),n.name==="QuotaExceededError"){console.warn("MSE QuotaExceededError. Playback might be affected."),this.isAppendingBuffer=!1,this.stopPlaybackInternal(),this.settings.showNotices&&new j.Notice("Audio buffer limit reached. Playback stopped.");return}this.isAppendingBuffer=!1}}isMSESupported(){return typeof window!="undefined"&&"MediaSource"in window&&typeof MediaSource!="undefined"&&MediaSource.isTypeSupported&&MediaSource.isTypeSupported("audio/mpeg")}async startPlayback(t){this.stopPlaybackInternal(),this.currentPlaybackId++;let n=this.currentPlaybackId,a=this.isMSESupported();if(this.isStreamingWithMSE=a,this.isPaused=!1,this.completeMp3BufferArray=[],this.mseAudioQueue=[],this.isAppendingBuffer=!1,this.settings.disablePlaybackControlPopover||this.showFloatingPlayerCallback({currentTime:0,duration:a?1/0:0,isPlaying:!1,isLoading:!0}),!t.trim()){ta(this.settings)&&new j.Notice("No text selected or available."),this.settings.disablePlaybackControlPopover||this.hideFloatingPlayerCallback(),this.isStreamingWithMSE=!1;return}let i=this.settings?Ze(Ie(t),this.settings.textFiltering,this.settings.symbolReplacement):Ze(Ie(t));if(!i.trim()){this.settings.showNotices&&new j.Notice("No readable text after filtering."),this.settings.disablePlaybackControlPopover||this.hideFloatingPlayerCallback(),this.isStreamingWithMSE=!1;return}a?await this.startMSEPlayback(i,n):await this.startFallbackPlayback(i,n)}async startMSEPlayback(t,n){this.mediaSource=new MediaSource,this.audioElement.src=URL.createObjectURL(this.mediaSource),this.mediaSource.addEventListener("sourceopen",()=>{if(!(this.currentPlaybackId!==n||!this.mediaSource)){URL.revokeObjectURL(this.audioElement.src);try{this.sourceBuffer=this.mediaSource.addSourceBuffer("audio/mpeg"),this.sourceBuffer.mode="sequence",this.sourceBuffer.addEventListener("updateend",()=>{if(this.isAppendingBuffer=!1,!this.isPaused&&this.audioElement.paused&&this.sourceBuffer&&this.sourceBuffer.buffered.length>0){let a=this.audioElement.play();a!==void 0&&a.then(i=>{this.currentPlaybackId===n&&!this.settings.disablePlaybackControlPopover&&this.updateFloatingPlayerCallback({currentTime:this.audioElement.currentTime,duration:1/0,isPlaying:!0,isLoading:!0})}).catch(i=>{console.error("Error starting MSE playback:",i),this.settings.showNotices&&new j.Notice("Error starting audio playback."),this.stopPlaybackInternal()})}this.appendNextChunkToSourceBuffer()}),this.sourceBuffer.addEventListener("error",a=>{console.error("SourceBuffer error",a),this.settings.showNotices&&new j.Notice("Audio playback error (SourceBuffer)."),this.stopPlaybackInternal()}),this.appendNextChunkToSourceBuffer()}catch(a){console.error("Error setting up MediaSource SourceBuffer:",a),this.settings.showNotices&&new j.Notice("Failed to initialize audio stream."),this.stopPlaybackInternal()}}}),this.mediaSource.addEventListener("sourceended",()=>{}),this.mediaSource.addEventListener("sourceclose",()=>{this.isStreamingWithMSE=!1}),await this.processTTSStream(t,n,en.AUDIO_24KHZ_48KBITRATE_MONO_MP3,!0)}async startFallbackPlayback(t,n){await this.processTTSStream(t,n,en.AUDIO_24KHZ_48KBITRATE_MONO_MP3,!1)}async processTTSStream(t,n,a,i){try{this.settings.showNotices&&new j.Notice(i?"Starting audio stream...":"Generating audio..."),this.updateStatusBarCallback(!0);let s=new Jt,r=this.settings.customVoice.trim()||this.settings.selectedVoice;await s.setMetadata(r,a);let l=new dc;l.rate=this.settings.playbackSpeed;let o=s.toStream(t,l);o.on("data",u=>{this.currentPlaybackId===n&&(this.completeMp3BufferArray.push(u),i&&(this.mseAudioQueue.push(u),this.appendNextChunkToSourceBuffer()))}),o.on("end",async()=>{this.currentPlaybackId===n&&(i?await this.finishMSEPlayback(n):await this.finishFallbackPlayback(n))})}catch(s){console.error("Error processing TTS stream:",s),this.currentPlaybackId===n&&(this.settings.showNotices&&new j.Notice("Failed to read note aloud."),this.stopPlaybackInternal())}}async finishMSEPlayback(t){if(await(async()=>{for(;this.mseAudioQueue.length>0||this.isAppendingBuffer;)await new Promise(s=>setTimeout(s,50))})(),this.mediaSource&&this.mediaSource.readyState==="open"&&this.sourceBuffer)try{if(!this.sourceBuffer.updating)this.mediaSource.endOfStream();else{let s=()=>{var r;if(this.mediaSource&&this.mediaSource.readyState==="open")try{this.mediaSource.endOfStream()}catch(l){console.warn("Error in endOfStream (onUpdateEnd)",l)}(r=this.sourceBuffer)==null||r.removeEventListener("updateend",s)};this.sourceBuffer.addEventListener("updateend",s)}}catch(s){console.warn("Error calling endOfStream on TTS end:",s)}if(this.completeMp3BufferArray.length===0){this.settings.showNotices&&new j.Notice("TTS stream was empty."),this.settings.disablePlaybackControlPopover||(this.updateFloatingPlayerCallback({currentTime:0,duration:0,isPlaying:!1,isLoading:!1}),this.hideFloatingPlayerCallback()),this.updateStatusBarCallback(!1),this.isStreamingWithMSE=!1;return}let a=Buffer.concat(this.completeMp3BufferArray),i=await this.fileManager.saveTempAudioFile(a);if(this.currentPlaybackId!==t){this.isStreamingWithMSE=!1;return}!i&&this.settings.showNotices&&!j.Platform.isMobile&&new j.Notice("Failed to save temporary audio for playback."),this.settings.disablePlaybackControlPopover||this.updateFloatingPlayerCallback({currentTime:this.audioElement.currentTime,duration:this.audioElement.duration,isPlaying:!this.audioElement.paused,isLoading:!1})}async finishFallbackPlayback(t){if(this.completeMp3BufferArray.length===0){this.settings.showNotices&&new j.Notice("TTS stream was empty."),this.settings.disablePlaybackControlPopover||(this.updateFloatingPlayerCallback({currentTime:0,duration:0,isPlaying:!1,isLoading:!1}),this.hideFloatingPlayerCallback()),this.updateStatusBarCallback(!1);return}try{this.settings.showNotices&&new j.Notice("Processing audio for playback...");let n;if(j.Platform.isMobile||typeof Buffer=="undefined"){let i=this.completeMp3BufferArray.reduce((l,o)=>l+o.length,0),s=new Uint8Array(i),r=0;for(let l of this.completeMp3BufferArray)s.set(l,r),r+=l.length;n=s}else n=Buffer.concat(this.completeMp3BufferArray);if(this.currentPlaybackId!==t)return;if(await this.fileManager.saveTempAudioFile(n)){this.audioElement.src=this.fileManager.getTempAudioFileResourcePath()||"",this.audioElement.load();let i=()=>{if(this.currentPlaybackId!==t)return;this.settings.disablePlaybackControlPopover||this.updateFloatingPlayerCallback({currentTime:0,duration:this.audioElement.duration,isPlaying:!1,isLoading:!1});let r=this.audioElement.play();r!==void 0&&r.then(()=>{this.currentPlaybackId===t&&!this.settings.disablePlaybackControlPopover&&this.updateFloatingPlayerCallback({currentTime:this.audioElement.currentTime,duration:this.audioElement.duration,isPlaying:!0,isLoading:!1})}).catch(l=>{console.error("Error starting temp file playback:",l),this.settings.showNotices&&new j.Notice("Error starting audio playback."),this.stopPlaybackInternal()}),this.audioElement.removeEventListener("loadedmetadata",i)},s=()=>{this.settings.showNotices&&new j.Notice("Failed to load temp audio file."),this.fallbackToBlobPlayback(n,t),this.audioElement.removeEventListener("error",s),this.audioElement.removeEventListener("loadedmetadata",i)};this.audioElement.addEventListener("loadedmetadata",i,{once:!0}),this.audioElement.addEventListener("error",s,{once:!0})}else this.fallbackToBlobPlayback(n,t)}catch(n){console.error("Error in finishFallbackPlayback:",n),this.settings.showNotices&&new j.Notice("Failed to process audio for mobile playback."),this.settings.disablePlaybackControlPopover||(this.updateFloatingPlayerCallback({currentTime:0,duration:0,isPlaying:!1,isLoading:!1}),this.hideFloatingPlayerCallback()),this.updateStatusBarCallback(!1)}}fallbackToBlobPlayback(t,n){try{let a;if(t instanceof Uint8Array)a=t.buffer.slice(t.byteOffset,t.byteOffset+t.byteLength);else{let o=new Uint8Array(t);a=o.buffer.slice(o.byteOffset,o.byteOffset+o.byteLength)}let i=new Blob([a],{type:"audio/mpeg"}),s=URL.createObjectURL(i);this.audioElement.src=s,this.audioElement.load();let r=()=>{this.settings.showNotices&&new j.Notice("Audio playback failed on mobile device."),this.settings.disablePlaybackControlPopover||(this.updateFloatingPlayerCallback({currentTime:0,duration:0,isPlaying:!1,isLoading:!1}),this.hideFloatingPlayerCallback()),this.updateStatusBarCallback(!1),URL.revokeObjectURL(s),this.audioElement.removeEventListener("error",r),this.audioElement.removeEventListener("loadedmetadata",l)},l=()=>{if(this.currentPlaybackId!==n){URL.revokeObjectURL(s);return}this.settings.disablePlaybackControlPopover||this.updateFloatingPlayerCallback({currentTime:0,duration:this.audioElement.duration,isPlaying:!1,isLoading:!1});let o=this.audioElement.play();o!==void 0&&o.then(()=>{this.currentPlaybackId===n&&!this.settings.disablePlaybackControlPopover&&this.updateFloatingPlayerCallback({currentTime:this.audioElement.currentTime,duration:this.audioElement.duration,isPlaying:!0,isLoading:!1})}).catch(u=>{console.error("Error starting blob playback:",u),this.settings.showNotices&&new j.Notice("Final audio playback attempt failed."),this.stopPlaybackInternal()}),this.audioElement.addEventListener("ended",()=>{URL.revokeObjectURL(s)},{once:!0}),this.audioElement.removeEventListener("loadedmetadata",l),this.audioElement.removeEventListener("error",r)};this.audioElement.addEventListener("loadedmetadata",l,{once:!0}),this.audioElement.addEventListener("error",r,{once:!0})}catch(a){console.error("Error in fallbackToBlobPlayback:",a),this.settings.showNotices&&new j.Notice("Critical audio playback error."),this.settings.disablePlaybackControlPopover||(this.updateFloatingPlayerCallback({currentTime:0,duration:0,isPlaying:!1,isLoading:!1}),this.hideFloatingPlayerCallback()),this.updateStatusBarCallback(!1)}}pausePlayback(){this.audioElement&&!this.audioElement.paused&&this.audioElement.pause()}resumePlayback(){this.audioElement&&this.audioElement.paused&&this.audioElement.play().catch(t=>console.error("Error resuming playback:",t))}stopPlayback(){this.currentPlaybackId++,this.stopPlaybackInternal()}stopPlaybackInternal(){if(this.currentPlaybackId++,this.isStreamingWithMSE=!1,this.isSwitchingToFullFile=!1,this.streamedPlaybackTimeBeforeSwitch=0,this.mseAudioQueue=[],this.isAppendingBuffer=!1,this.cancelSleepTimer(),this.mediaSource){if(this.mediaSource.readyState==="open"&&this.sourceBuffer&&this.sourceBuffer.updating)try{this.sourceBuffer.abort()}catch(t){console.warn("Error aborting sourceBuffer:",t)}if(this.mediaSource.readyState==="open")try{this.sourceBuffer&&this.mediaSource.endOfStream()}catch(t){console.warn("Error calling endOfStream on mediaSource stop:",t)}this.audioElement.src.startsWith("blob:")&&URL.revokeObjectURL(this.audioElement.src),this.mediaSource=null,this.sourceBuffer=null}this.audioElement&&(this.audioElement.pause(),this.audioElement.src&&!this.audioElement.src.startsWith("blob:")?(this.audioElement.src="",this.audioElement.load()):this.audioElement.src||this.audioElement.load(),this.settings.disablePlaybackControlPopover||this.updateFloatingPlayerCallback({currentTime:0,duration:0,isPlaying:!1,isLoading:!1}),this.resetPlaybackStateAndHidePlayer(),this.updateStatusBarCallback(!1)),this.completeMp3BufferArray=[],this.clearMediaSession()}resetPlaybackStateAndHidePlayer(){this.isPaused=!1,this.settings.disablePlaybackControlPopover||this.hideFloatingPlayerCallback()}replayPlayback(){this.audioElement&&this.audioElement.src&&this.audioElement.duration>0?(this.audioElement.currentTime=0,this.isPaused=!1,this.audioElement.play().catch(t=>{console.error("Error during replay:",t),this.settings.showNotices&&new j.Notice("Error replaying audio."),this.stopPlaybackInternal()})):(this.settings.showNotices&&new j.Notice("No audio to replay or audio not fully loaded."),this.stopPlaybackInternal())}isPlaybackPaused(){return this.isPaused}isPlaying(){return this.audioElement!==null&&!this.audioElement.paused}jumpForward(t=10){if(this.audioElement&&this.audioElement.duration>0){let n=Math.min(this.audioElement.currentTime+t,this.audioElement.duration);this.seekPlayback(n)}}jumpBackward(t=10){if(this.audioElement&&this.audioElement.duration>0){let n=Math.max(this.audioElement.currentTime-t,0);this.seekPlayback(n)}}updateSettings(t){this.settings=t,this.initializeMediaSession()}seekPlayback(t){if(this.audioElement&&this.audioElement.seekable&&this.audioElement.seekable.length>0){let n=Math.max(0,Math.min(t,this.audioElement.duration));isFinite(n)&&isFinite(this.audioElement.duration)&&this.audioElement.duration>0?this.audioElement.currentTime=n:console.warn("Cannot seek: Audio duration is not yet available or is invalid.",{currentTime:this.audioElement.currentTime,duration:this.audioElement.duration,readyState:this.audioElement.readyState,seekable:this.audioElement.seekable})}else console.warn("Cannot seek: Audio element is not seekable or has no seekable ranges.",{readyState:this.audioElement.readyState,seekable:this.audioElement.seekable})}setFloatingPlayerCallbacks(t,n,a){this.showFloatingPlayerCallback=i=>{this.settings.disablePlaybackControlPopover||t(i)},this.hideFloatingPlayerCallback=()=>{this.settings.disablePlaybackControlPopover||n()},this.updateFloatingPlayerCallback=i=>{this.settings.disablePlaybackControlPopover||a(i)}}setQueueChangeCallback(t){this.queueChangeCallback=t}setQueueUIUpdateCallback(t){this.queueUIUpdateCallback=t}notifyQueueChange(){this.queueChangeCallback&&this.queueChangeCallback()}notifyQueueUIUpdate(){this.queueUIUpdateCallback&&this.queueUIUpdateCallback()}addToQueue(t,n){this.playbackQueue.push({text:t,title:n}),this.settings.showNotices&&new j.Notice(`Added "${n||"text"}" to playback queue (${this.playbackQueue.length} items)`),this.notifyQueueChange()}async playQueue(){if(this.playbackQueue.length===0){this.settings.showNotices&&new j.Notice("Playback queue is empty.");return}this.currentQueueIndex=0,this.isPlayingFromQueue=!0,this.notifyQueueUIUpdate(),await this.playCurrentQueueItem()}async playNextInQueue(){if(!this.isPlayingFromQueue||this.currentQueueIndex>=this.playbackQueue.length-1)if(this.loopEnabled&&this.playbackQueue.length>0){this.currentQueueIndex=0,this.notifyQueueUIUpdate(),this.settings.showNotices&&new j.Notice("Queue looping - restarting from beginning."),await this.playCurrentQueueItem();return}else{this.isPlayingFromQueue=!1,this.currentQueueIndex=-1,this.notifyQueueUIUpdate(),this.settings.showNotices&&new j.Notice("Finished playing queue.");return}this.currentQueueIndex++,this.notifyQueueUIUpdate(),await this.playCurrentQueueItem()}async playCurrentQueueItem(){let t=this.playbackQueue[this.currentQueueIndex];t&&(this.settings.showNotices&&new j.Notice(`Playing ${this.currentQueueIndex+1}/${this.playbackQueue.length}: ${t.title||"Untitled"}`),await this.startPlayback(t.text))}clearQueue(){this.playbackQueue=[],this.currentQueueIndex=-1,this.isPlayingFromQueue=!1,this.settings.showNotices&&new j.Notice("Playback queue cleared."),this.notifyQueueChange(),this.notifyQueueUIUpdate()}getQueueStatus(){return{queue:[...this.playbackQueue],currentIndex:this.currentQueueIndex,isPlayingFromQueue:this.isPlayingFromQueue}}async playQueueItem(t){if(t<0||t>=this.playbackQueue.length){this.settings.showNotices&&new j.Notice("Invalid queue item index.");return}this.currentQueueIndex=t,this.isPlayingFromQueue=!0,this.notifyQueueUIUpdate(),await this.playCurrentQueueItem()}removeQueueItem(t){if(t<0||t>=this.playbackQueue.length){this.settings.showNotices&&new j.Notice("Invalid queue item index.");return}let n=this.playbackQueue.splice(t,1)[0];this.isPlayingFromQueue&&(t<this.currentQueueIndex?this.currentQueueIndex--:t===this.currentQueueIndex&&this.currentQueueIndex>=this.playbackQueue.length&&(this.isPlayingFromQueue=!1,this.currentQueueIndex=-1,this.stopPlayback())),this.settings.showNotices&&new j.Notice(`Removed "${n.title||"Untitled"}" from queue.`),this.notifyQueueChange(),this.notifyQueueUIUpdate()}moveQueueItem(t,n){if(t<0||t>=this.playbackQueue.length||n<0||n>=this.playbackQueue.length||t===n)return;let a=this.playbackQueue.splice(t,1)[0];this.playbackQueue.splice(n,0,a),this.isPlayingFromQueue&&(t===this.currentQueueIndex?this.currentQueueIndex=n:t<this.currentQueueIndex&&n>=this.currentQueueIndex?this.currentQueueIndex--:t>this.currentQueueIndex&&n<=this.currentQueueIndex&&this.currentQueueIndex++),this.settings.showNotices&&new j.Notice(`Moved "${a.title||"Untitled"}" in queue.`),this.notifyQueueChange(),this.notifyQueueUIUpdate()}setSleepTimer(t){this.cancelSleepTimer(),this.sleepTimerMinutes=t,this.sleepTimerTimeout=window.setTimeout(()=>{this.settings.showNotices&&new j.Notice("Sleep timer expired. Stopping playback."),this.stopPlayback(),this.sleepTimerTimeout=null,this.sleepTimerMinutes=0},t*60*1e3),this.settings.showNotices&&new j.Notice(`Sleep timer set for ${t} minute${t!==1?"s":""}`)}cancelSleepTimer(){this.sleepTimerTimeout&&(clearTimeout(this.sleepTimerTimeout),this.sleepTimerTimeout=null,this.sleepTimerMinutes>0&&this.settings.showNotices&&new j.Notice("Sleep timer cancelled"),this.sleepTimerMinutes=0)}getSleepTimerStatus(){return{isActive:this.sleepTimerTimeout!==null,remainingMinutes:this.sleepTimerMinutes}}setLoopEnabled(t){this.loopEnabled=t}getLoopEnabled(){return this.loopEnabled}};var Z=require("obsidian");var qt=null,Nw=null;if(!Z.Platform.isMobile)try{qt=require("path"),Nw=require("os")}catch(e){console.warn("Node.js modules not available:",e)}function Dw(e){if(qt&&qt.dirname)return qt.dirname(e);let t=Math.max(e.lastIndexOf("/"),e.lastIndexOf("\\"));return t===-1?".":e.substring(0,t)}function Bw(e,t){if(qt&&qt.basename)return qt.basename(e,t);let n=Math.max(e.lastIndexOf("/"),e.lastIndexOf("\\")),a=n===-1?e:e.substring(n+1);return t&&a.endsWith(t)&&(a=a.substring(0,a.length-t.length)),a}function Pw(...e){return qt&&qt.join?qt.join(...e):(0,Z.normalizePath)(e.join("/"))}var pl=class{constructor(t,n,a){this.tempAudioPath=null;this.app=t,this.settings=n,a&&(this.tempAudioPath=a)}async extractFileContent(t){let n=this.app.vault.getAbstractFileByPath(t);if(n instanceof Z.TFile)try{return await this.app.vault.read(n)}catch(a){return console.error("Error reading file content:",a),null}else return console.warn("The specified file is not a TFile (markdown file)."),null}async embedMP3InNote(t,n,a){try{if(a){let i=a.getSelection(),s=a.getCursor("to"),r=`

![[${t}]]
`;i?a.replaceSelection(`${i}${r}`):a.replaceRange(r,s),this.settings.showNotices&&new Z.Notice("MP3 embedded after the selected text.")}else{if(!n){console.error("Error embedding MP3 in note due to filePath and editor not being passed to embedMP3InNote"),this.settings.showNotices&&new Z.Notice("Failed to embed MP3 in note.");return}let i=this.app.vault.getAbstractFileByPath(n);if(!(i instanceof Z.TFile)){this.settings.showNotices&&new Z.Notice(`File not found or is not a valid markdown file: ${n}`);return}let s=await this.app.vault.read(i),r=`![[${t}]]`,l=`${s}

${r}`;await this.app.vault.modify(i,l),this.settings.showNotices&&new Z.Notice("MP3 embedded in note.")}}catch(i){console.error("Error embedding MP3 in note:",i),this.settings.showNotices&&new Z.Notice("Failed to embed MP3 in note.")}}async saveMP3File(t,n){try{let a;if(t instanceof Uint8Array&&!(t instanceof Buffer))if(typeof Buffer!="undefined"&&Buffer.from)a=Buffer.from(t);else throw new Error("Buffer not available and cannot convert Uint8Array");else a=t;let i=this.settings.replaceSpacesInFilenames?"Note_Narration_Audio":"Note Narration Audio",s=(0,Z.normalizePath)(this.settings.outputFolder||i);await this.app.vault.adapter.exists(s)||await this.app.vault.adapter.mkdir(s);let r=(0,Z.getLanguage)(),l=new Date,u=new Intl.DateTimeFormat(r,{month:"short",day:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit",hour12:!1}).format(l).replace(/,/g,"").trim();u=u.replace(/[\\/:*?"<>|]/g,"-");let c=n?Bw(n,".md")||su:su;c=c.replace(/[\\/:*?"<>|]/g,"-"),this.settings.replaceSpacesInFilenames&&(c=c.replace(/\s+/g,"_"),u=u.replace(/\s+/g,"_"));let f=this.settings.replaceSpacesInFilenames?`${c}_-_${u}.mp3`:`${c} - ${u}.mp3`,d=await this.generateUniqueFileName(s,f),m=(0,Z.normalizePath)(`${s}/${d}`);await this.app.vault.createBinary(m,a);let y=this.app.vault.adapter;if(y instanceof Z.FileSystemAdapter){let E=y.getBasePath(),M=Pw(E,s,d);this.settings.showNotices&&new Z.Notice(`MP3 saved to: ${M}`)}else this.settings.showNotices&&new Z.Notice(`MP3 saved to: ${m}`);return m}catch(a){return console.error("Error saving MP3:",a),this.settings.showNotices&&new Z.Notice("Failed to save MP3 file."),null}}async saveTempAudioFile(t){if(Z.Platform.isMobile)return console.log("Temporary file saving is not supported on mobile platforms."),null;if(!this.tempAudioPath)return console.error("Temporary audio path is not set in FileOperationsManager."),this.settings.showNotices&&new Z.Notice("Failed to save temporary audio: path not set."),null;let n=this.app.vault.adapter;if(!(n instanceof Z.FileSystemAdapter))return console.error("File system adapter not available for temp audio."),this.settings.showNotices&&new Z.Notice("Unable to save temporary audio file."),null;let a;if(t instanceof Uint8Array&&!(t instanceof Buffer))if(typeof Buffer!="undefined"&&Buffer.from)a=Buffer.from(t);else return console.error("Buffer not available for temp file creation"),null;else a=t;try{let i=Dw(this.tempAudioPath);return await n.exists(i)||await n.mkdir(i),await n.writeBinary(this.tempAudioPath,a),this.app.vault.adapter.getResourcePath(this.tempAudioPath)}catch(i){return console.error("Error saving temporary audio file:",i),this.settings.showNotices&&new Z.Notice("Failed to save temporary audio file."),null}}getTempAudioFileResourcePath(){return Z.Platform.isMobile||!this.tempAudioPath?null:this.app.vault.adapter.getResourcePath(this.tempAudioPath)}async cleanupTempAudioFile(){if(Z.Platform.isMobile||!this.tempAudioPath)return;let t=this.app.vault.adapter;if(t instanceof Z.FileSystemAdapter)try{await t.exists(this.tempAudioPath)&&await t.remove(this.tempAudioPath)}catch(n){console.error("Error cleaning up temporary audio file:",n)}}async generateUniqueFileName(t,n){let a=(0,Z.normalizePath)(t),i=n,s=1,r=100;for(;s<=r;){let l=(0,Z.normalizePath)(`${a}/${i}`);try{if(!await this.app.vault.adapter.exists(l))return i;let u=n.lastIndexOf(".");if(u===-1)i=`${n} (${s})`;else{let c=n.substring(0,u),f=n.substring(u);i=`${c} (${s})${f}`}s++}catch(o){return console.error("Error checking file existence:",o),i}}return console.warn("generateUniqueFileName: reached maximum attempts limit"),i}updateSettings(t){this.settings=t}};var Je=require("obsidian");var ba=require("obsidian");var yl=class{constructor(){}};var bl=class{constructor(t){this.tasks=new Map;this.processingQueue=[];this.isProcessing=!1;this.settings=t}createTask(t,n){if(ba.Platform.isMobile)throw new Error("MP3 generation is not supported on mobile devices due to file system limitations. Use audio playback instead.");let a=Zt(t);if(a.wasTruncated){let o=a.truncationReason==="words"?"word":"character",u=a.truncationReason==="words"?"5,000 words":"30,000 characters";this.settings.showNotices&&new ba.Notice(`Content exceeds MP3 generation limit (${u}). Generating MP3 for the first ${a.finalWordCount.toLocaleString()} words (${a.finalCharCount.toLocaleString()} characters). Original content had ${a.originalWordCount.toLocaleString()} words.`,8e3)}let i=Ze(Ie(a.content,this.settings.textFiltering.filterFrontmatter),this.settings.textFiltering,this.settings.symbolReplacement);if(!i.trim())throw new Error("No readable text after filtering");let s=`tts-${Date.now()}-${Math.floor(Math.random()*1e3)}`,r=this.settings.customVoice.trim()||this.settings.selectedVoice,l={id:s,text:i,status:"pending",progress:0,createdAt:new Date,outputFormat:n,voice:r,playbackSpeed:this.settings.playbackSpeed};return this.tasks.set(s,l),this.processingQueue.push(s),this.isProcessing||this.processNextTask(),l}async processNextTask(){if(this.processingQueue.length===0){this.isProcessing=!1;return}this.isProcessing=!0;let t=this.processingQueue.shift(),n=this.tasks.get(t);if(!n){setTimeout(()=>this.processNextTask(),0);return}n.status="processing",this.tasks.set(t,n);try{let a=new Jt;await a.setMetadata(n.voice,n.outputFormat);let i=new yl;i.rate=n.playbackSpeed;let s=a.toStream(n.text,i),r=[],l=n.text.length*50,o=0;s.on("data",u=>{r.push(u),o+=u.length,n.progress=Math.min(99,Math.floor(o/l*100)),this.tasks.set(t,n)}),await new Promise((u,c)=>{s.on("end",()=>{let d;if(ba.Platform.isMobile||typeof Buffer=="undefined"){let m=r.reduce((E,M)=>E+M.length,0);d=new Uint8Array(m);let y=0;for(let E of r)d.set(E,y),y+=E.length}else d=Buffer.concat(r);n.buffer=d,n.status="completed",n.progress=100,n.completedAt=new Date,this.tasks.set(t,n),u()});let f=setTimeout(()=>{n.status!=="completed"&&(n.status="failed",n.error="Timed out waiting for TTS stream to complete",this.tasks.set(t,n),c(new Error("TTS processing timeout")))},6e4);s.on("end",()=>{clearTimeout(f)})})}catch(a){n.status="failed",n.error=a.message||"Unknown error",this.tasks.set(t,n),console.error("TTS generation error:",a)}setTimeout(()=>this.processNextTask(),10)}getTask(t){return this.tasks.get(t)}getAllTasks(){return Array.from(this.tasks.values())}cleanupOldTasks(t=36e5){let n=new Date().getTime();for(let[a,i]of this.tasks.entries())(i.status==="completed"||i.status==="failed")&&n-i.createdAt.getTime()>t&&this.tasks.delete(a)}async generateAudioBuffer(t){try{let n=Zt(t);if(n.wasTruncated){let u=n.truncationReason==="words"?"word":"character",c=n.truncationReason==="words"?"5,000 words":"30,000 characters";this.settings.showNotices&&new ba.Notice(`Content exceeds playback limit (${c}). Playing first ${n.finalWordCount.toLocaleString()} words (${n.finalCharCount.toLocaleString()} characters). Original content had ${n.originalWordCount.toLocaleString()} words.`,8e3)}let a=Ze(Ie(n.content,this.settings.textFiltering.filterFrontmatter),this.settings.textFiltering,this.settings.symbolReplacement);if(!a.trim())throw new Error("No readable text after filtering");let i=this.settings.customVoice.trim()||this.settings.selectedVoice,s=new Jt;await s.setMetadata(i,en.WEBM_24KHZ_16BIT_MONO_OPUS);let r=new yl;r.rate=this.settings.playbackSpeed;let l=s.toStream(a,r),o=[];return new Promise((u,c)=>{l.on("data",d=>{o.push(d)}),l.on("end",async()=>{let d;if(ba.Platform.isMobile||typeof Buffer=="undefined"){let m=o.reduce((M,g)=>M+g.length,0),y=new Uint8Array(m),E=0;for(let M of o)y.set(M,E),E+=M.length;d=y.buffer}else{let m=Buffer.concat(o);d=m.buffer.slice(m.byteOffset,m.byteOffset+m.byteLength)}try{let y=await new AudioContext().decodeAudioData(d);u(y)}catch(m){c(m)}});let f=setTimeout(()=>{c(new Error("TTS processing timeout"))},3e4);l.on("end",()=>{clearTimeout(f)})})}catch(n){return console.error("Error generating audio buffer:",n),null}}updateSettings(t){this.settings=t}};var vl=class{constructor(t,n,a,i){this.statusBarEl=null;this.ribbonIconEl=null;this.plugin=t,this.settings=n,this.audioManager=a,this.ttsEngine=i}initializeStatusBar(){this.statusBarEl=this.plugin.addStatusBarItem(),this.updateStatusBar()}removeStatusBarButton(){this.statusBarEl&&(this.statusBarEl.remove(),this.statusBarEl=null)}updateStatusBar(t=!1){if(!this.statusBarEl)return;if(!this.settings.showStatusBarButton){this.removeStatusBarButton();return}if(this.statusBarEl.empty(),this.hasActiveTasks())this.renderTaskProgressIndicator();else if(t){let a=createEl("span",{cls:"edge-tts-status-bar-control"});(0,Je.setTooltip)(a,this.audioManager.isPlaybackPaused()?"Resume":"Pause",{placement:"top"}),(0,Je.setIcon)(a,this.audioManager.isPlaybackPaused()?"circle-play":"circle-pause"),a.onclick=()=>this.audioManager.isPlaybackPaused()?this.audioManager.resumePlayback():this.audioManager.pausePlayback(),this.statusBarEl.appendChild(a);let i=createEl("span",{cls:"edge-tts-status-bar-control"});(0,Je.setTooltip)(i,"Stop",{placement:"top"}),(0,Je.setIcon)(i,"square"),i.onclick=()=>this.audioManager.stopPlayback(),this.statusBarEl.appendChild(i)}else{let a=createEl("span",{cls:"edge-tts-status-bar-control"});(0,Je.setTooltip)(a,"Read note aloud",{placement:"top"}),(0,Je.setIcon)(a,"audio-lines"),a.onclick=()=>this.plugin.readNoteAloud(),this.statusBarEl.appendChild(a)}}renderTaskProgressIndicator(){if(!this.statusBarEl||!this.ttsEngine)return;let t=this.ttsEngine.getAllTasks(),n=t.filter(r=>r.status==="processing"),a=t.filter(r=>r.status==="pending"),i=createEl("div",{cls:"edge-tts-status-bar-progress-container"}),s=createEl("span",{cls:"edge-tts-status-bar-icon"});if((0,Je.setIcon)(s,"cpu"),i.appendChild(s),n.length>0){let r=n[0],l=createEl("span",{cls:"edge-tts-status-bar-text",text:`${r.progress}%`});i.appendChild(l);let o=n.length+a.length,u=o>1?`${o} tasks`:"1 task";(0,Je.setTooltip)(i,`Processing TTS: ${u}`,{placement:"top"})}else if(a.length>0){let r=createEl("span",{cls:"edge-tts-status-bar-text",text:"Waiting..."});i.appendChild(r);let l=a.length,o=l>1?`${l} tasks`:"1 task";(0,Je.setTooltip)(i,`Waiting to process: ${o}`,{placement:"top"})}this.statusBarEl.appendChild(i)}hasActiveTasks(){return this.ttsEngine?this.ttsEngine.getAllTasks().some(n=>n.status==="pending"||n.status==="processing"):!1}addPluginRibbonIcon(){this.ribbonIconEl&&this.ribbonIconEl.remove(),this.ribbonIconEl=this.plugin.addRibbonIcon("audio-lines","Read note aloud",()=>{this.plugin.readNoteAloud()})}removePluginRibbonIcon(){this.ribbonIconEl&&(this.ribbonIconEl.remove(),this.ribbonIconEl=null)}addPluginMenuItems(){this.plugin.registerEvent(this.plugin.app.workspace.on("file-menu",(t,n)=>{t.addItem(a=>{a.setTitle("Read note aloud").setIcon("audio-lines").onClick(async()=>{this.plugin.readNoteAloud(void 0,void 0,n.path)})}),this.settings.enableQueueFeature&&t.addItem(a=>{a.setTitle("Add to playback queue").setIcon("list-plus").onClick(async()=>{let i=await this.plugin.fileManager.extractFileContent(n.path);i&&(this.audioManager.addToQueue(i,n.basename),this.plugin.queueUIManager&&!this.plugin.queueUIManager.getIsQueueVisible()&&this.plugin.queueUIManager.showQueue())})}),this.settings.generateMP3&&!Je.Platform.isMobile&&t.addItem(a=>{a.setTitle("Generate MP3").setIcon("microphone").onClick(async()=>{await this.plugin.generateMP3(void 0,void 0,n.path)})})})),this.plugin.registerEvent(this.plugin.app.workspace.on("editor-menu",(t,n,a)=>{t.addItem(i=>{i.setTitle("Read note aloud").setIcon("audio-lines").onClick(async()=>{this.plugin.readNoteAloud(n,a)})}),this.settings.enableQueueFeature&&t.addItem(i=>{i.setTitle("Add selection to queue").setIcon("list-plus").onClick(async()=>{var l;let s=n.getSelection(),r=((l=a.file)==null?void 0:l.basename)||"Untitled";s.trim()?this.audioManager.addToQueue(s,`${r} (selection)`):this.audioManager.addToQueue(n.getValue(),r),this.plugin.queueUIManager&&!this.plugin.queueUIManager.getIsQueueVisible()&&this.plugin.queueUIManager.showQueue()})}),this.settings.generateMP3&&!Je.Platform.isMobile&&t.addItem(i=>{i.setTitle("Generate MP3").setIcon("microphone").onClick(async()=>{await this.plugin.generateMP3(n,a)})})}))}setTTSEngine(t){this.ttsEngine=t}updateSettings(t){this.settings=t}};var gv=Ce(tn()),pv=Ce(Xo());var Fe=Ce(tn()),hv=require("obsidian"),G=Ce(za()),Sn=({icon:e,className:t,size:n=20})=>{let a=(0,Fe.useRef)(null);return(0,Fe.useEffect)(()=>{if(a.current){a.current.innerHTML="",(0,hv.setIcon)(a.current,e);let i=a.current.querySelector("svg");i&&n&&(i.style.width=`${n}px`,i.style.height=`${n}px`)}},[e,n]),(0,G.jsx)("span",{ref:a,className:t,style:{display:"flex",alignItems:"center",justifyContent:"center"}})},mv=({isVisible:e,onClose:t,onPause:n,onResume:a,onStop:i,isPaused:s,initialPosition:r={x:50,y:50},onDragEnd:l,currentTime:o=0,duration:u=0,onSeek:c,onReplay:f,onJumpForward:d,onJumpBackward:m,isLoading:y=!1,queueInfo:E,onToggleQueue:M,isQueueVisible:g=!1})=>{let[h,p]=(0,Fe.useState)(r),[S,A]=(0,Fe.useState)(!1),_=(0,Fe.useRef)({x:0,y:0}),O=(0,Fe.useRef)(null),R=(0,Fe.useCallback)(P=>{if("touches"in P){let ee=P.touches[0]||P.changedTouches[0];return{clientX:ee.clientX,clientY:ee.clientY}}else return{clientX:P.clientX,clientY:P.clientY}},[]),q=(0,Fe.useCallback)(P=>{let ee=P.target;if(!(ee.closest("button")||ee.closest(".seek-slider")||ee.classList.contains("floating-player-close-button-icon"))){if(A(!0),O.current){let Wn=O.current.getBoundingClientRect(),be=R(P.nativeEvent);_.current={x:be.clientX-Wn.left,y:be.clientY-Wn.top}}P.preventDefault()}},[R]),k=(0,Fe.useCallback)(P=>{if(S){let ee=R(P);p({x:ee.clientX-_.current.x,y:ee.clientY-_.current.y})}},[S,R]),b=(0,Fe.useCallback)(()=>{S&&(A(!1),l&&l(h))},[S,l,h]);(0,Fe.useEffect)(()=>(S?(document.addEventListener("mousemove",k),document.addEventListener("mouseup",b),document.addEventListener("touchmove",k,{passive:!1}),document.addEventListener("touchend",b)):(document.removeEventListener("mousemove",k),document.removeEventListener("mouseup",b),document.removeEventListener("touchmove",k),document.removeEventListener("touchend",b)),()=>{document.removeEventListener("mousemove",k),document.removeEventListener("mouseup",b),document.removeEventListener("touchmove",k),document.removeEventListener("touchend",b)}),[S,k,b]),(0,Fe.useEffect)(()=>{p(r)},[r]);let x=P=>{c&&c(parseFloat(P.target.value))},B=P=>{if(P===1/0)return"\u221E";let ee=Math.floor(P/60),Wn=Math.floor(P%60).toString().padStart(2,"0");return`${ee}:${Wn}`};if(!e)return null;let $=s,v=u>0&&o>=u-.1,T=$&&v&&!!f;return(0,G.jsxs)("div",{ref:O,className:"floating-player-ui",style:{left:`${h.x}px`,top:`${h.y}px`,cursor:S?"grabbing":"grab",touchAction:"none"},onMouseDown:q,onTouchStart:q,children:[(0,G.jsx)("div",{onClick:t,className:"floating-player-close-button","aria-label":"Close Player",children:(0,G.jsx)(Sn,{icon:"x",size:16,className:"floating-player-close-button-icon"})}),M&&(0,G.jsx)("div",{onClick:M,className:`floating-player-queue-button ${E&&E.isPlayingFromQueue||y?"top-position":"bottom-position"}`,"aria-label":g?"Hide Queue":"Show Queue",children:(0,G.jsx)(Sn,{icon:"list-music",size:12})}),!y&&(0,G.jsx)("div",{onClick:i,"aria-label":"Stop",className:"player-control-button player-stop-button",children:(0,G.jsx)(Sn,{icon:"square",size:16,className:"floating-player-close-button-icon"})}),(0,G.jsxs)("div",{className:"player-content",children:[E&&E.isPlayingFromQueue&&(0,G.jsxs)("div",{className:"queue-info",style:{fontSize:"11px",color:"var(--text-muted)",marginBottom:"4px",textAlign:"center"},children:["Queue: ",E.currentIndex+1,"/",E.totalItems,E.currentTitle&&(0,G.jsx)("div",{style:{fontSize:"10px",opacity:.8},children:E.currentTitle})]}),u>0&&(0,G.jsx)("div",{className:"player-progress",children:y?(0,G.jsx)("div",{style:{fontStyle:"italic",color:"var(--text-muted)",fontSize:"12px",textAlign:"center",width:"100%"},children:"Streaming..."}):(0,G.jsxs)(G.Fragment,{children:[(0,G.jsx)("span",{children:B(o)}),(0,G.jsx)("input",{type:"range",min:"0",max:u,value:o,onChange:x,className:"seek-slider",disabled:!c||u===0||y,"aria-label":"Seek"}),(0,G.jsx)("span",{children:B(u)})]})}),(0,G.jsxs)("div",{className:"player-controls",children:[y&&(0,G.jsx)("div",{className:"player-loading-indicator",title:"Loading...","aria-label":"Loading...",children:(0,G.jsx)(Sn,{icon:"loader-2",size:18})}),!y&&(0,G.jsx)(G.Fragment,{children:(0,G.jsxs)("div",{className:"player-main-controls",children:[T&&f&&(0,G.jsx)("button",{onClick:f,"aria-label":"Replay",children:(0,G.jsx)(Sn,{icon:"rotate-cw"})}),!T&&m&&u!=1/0&&(0,G.jsx)("div",{onClick:m,"aria-label":"Jump Backward 10s",className:"player-control-button",children:(0,G.jsx)(Sn,{icon:"rotate-ccw"})}),!T&&s&&a&&(0,G.jsx)("button",{onClick:a,"aria-label":"Resume",children:(0,G.jsx)(Sn,{icon:"play"})}),!T&&!s&&n&&(0,G.jsx)("button",{onClick:n,"aria-label":"Pause",children:(0,G.jsx)(Sn,{icon:"pause"})}),!T&&d&&u!=1/0&&(0,G.jsx)("div",{onClick:d,"aria-label":"Jump Forward 10s",className:"player-control-button",children:(0,G.jsx)(Sn,{icon:"rotate-cw"})})]})})]})]})]})};var Zo=class{constructor(t){this.hostElement=null;this.reactRoot=null;this.lastPosition=void 0;this.isPlayerVisible=!1;this.currentPlaybackState={currentTime:0,duration:0,isPlaying:!1,isLoading:!1};this.resizeDebounceTimeout=null;this.RESIZE_DEBOUNCE_DELAY=250;this.debouncedWindowResize=()=>{this.resizeDebounceTimeout&&clearTimeout(this.resizeDebounceTimeout),this.resizeDebounceTimeout=window.setTimeout(()=>{this.handleWindowResize()},this.RESIZE_DEBOUNCE_DELAY)};this.audioManager=t.audioManager,this.savePosition=t.savePositionCallback,this.queueUIManager=t.queueUIManager,this.enableQueueFeature=t.enableQueueFeature,this.handleWindowResize=this.handleWindowResize.bind(this),window.addEventListener("resize",this.debouncedWindowResize),this.renderComponent()}handleWindowResize(){if(!this.isPlayerVisible||!this.lastPosition)return;let{innerWidth:t,innerHeight:n}=window,a=this.lastPosition.x+Ge.PLAYER_WIDTH,i=this.lastPosition.y+Ge.PLAYER_HEIGHT;(this.lastPosition.x>t-Ge.RIGHT_MARGIN||this.lastPosition.y>n-Ge.BOTTOM_MARGIN||a<Ge.RIGHT_MARGIN||i<Ge.BOTTOM_MARGIN)&&this.resetPlayerPosition()}createHostElement(){this.hostElement||(this.hostElement=document.createElement("div"),this.hostElement.id="obsidian-edge-tts-floating-player-host",document.body.appendChild(this.hostElement),this.reactRoot=pv.default.createRoot(this.hostElement))}showPlayer(t){this.createHostElement(),this.isPlayerVisible=!0,t?this.currentPlaybackState=t:this.currentPlaybackState={currentTime:0,duration:0,isPlaying:!1,isLoading:this.currentPlaybackState.isLoading},this.renderComponent()}hidePlayer(){this.isPlayerVisible=!1,this.lastPosition&&this.savePosition(this.lastPosition).catch(t=>{console.error("Failed to save player position on hide:",t)}),this.renderComponent()}togglePlayerVisibility(){this.isPlayerVisible?this.hidePlayer():this.showPlayer()}updatePlayerState(t){this.currentPlaybackState=t,this.isPlayerVisible&&this.renderComponent()}getIsPlayerVisible(){return this.isPlayerVisible}resetPlayerPosition(){let t=typeof window!="undefined"?window.innerWidth-Ge.PLAYER_WIDTH:50,n=typeof window!="undefined"?window.innerHeight-Ge.PLAYER_HEIGHT:50;this.lastPosition={x:t,y:n},this.savePosition(this.lastPosition).catch(a=>{console.error("Failed to save player position on reset:",a)}),this.isPlayerVisible&&this.renderComponent()}renderComponent(){var t,n;if(this.reactRoot){let a=typeof window!="undefined"?window.innerWidth-Ge.PLAYER_WIDTH:50,i=typeof window!="undefined"?window.innerHeight-Ge.PLAYER_HEIGHT:50,s=(t=this.lastPosition)!=null?t:{x:a,y:i},r=this.enableQueueFeature?this.audioManager.getQueueStatus():{queue:[],currentIndex:-1,isPlayingFromQueue:!1},l=this.enableQueueFeature&&r.isPlayingFromQueue?{currentIndex:r.currentIndex,totalItems:r.queue.length,currentTitle:(n=r.queue[r.currentIndex])==null?void 0:n.title,isPlayingFromQueue:r.isPlayingFromQueue}:void 0;this.reactRoot.render(gv.default.createElement(mv,{isVisible:this.isPlayerVisible,onClose:()=>this.hidePlayer(),onPause:this.audioManager.isPlaybackPaused()?void 0:()=>this.audioManager.pausePlayback(),onResume:this.audioManager.isPlaybackPaused()?()=>this.audioManager.resumePlayback():void 0,onStop:()=>this.audioManager.stopPlayback(),isPaused:!this.currentPlaybackState.isPlaying,initialPosition:s,onDragEnd:o=>{this.lastPosition=o,this.savePosition(o).catch(u=>{console.error("Failed to save player position on drag end:",u)})},currentTime:this.currentPlaybackState.currentTime,duration:this.currentPlaybackState.duration,onSeek:o=>this.audioManager.seekPlayback(o),onReplay:()=>this.audioManager.replayPlayback(),onJumpForward:()=>this.audioManager.jumpForward(),onJumpBackward:()=>this.audioManager.jumpBackward(),isLoading:this.currentPlaybackState.isLoading,queueInfo:l,onToggleQueue:this.enableQueueFeature&&this.queueUIManager?()=>{var o;return(o=this.queueUIManager)==null?void 0:o.toggleQueueVisibility()}:void 0,isQueueVisible:this.enableQueueFeature&&this.queueUIManager?this.queueUIManager.getIsQueueVisible():!1}))}}setInitialSavedPosition(t){t&&(this.lastPosition=t,this.isPlayerVisible&&this.renderComponent())}getCurrentPosition(){return this.lastPosition}setQueueUIManager(t){this.queueUIManager=t}updateQueueFeatureEnabled(t){this.enableQueueFeature=t,this.isPlayerVisible&&this.renderComponent()}destroy(){this.reactRoot&&(this.reactRoot.unmount(),this.reactRoot=null),this.hostElement&&(this.hostElement.remove(),this.hostElement=null),this.isPlayerVisible=!1,window.removeEventListener("resize",this.debouncedWindowResize),this.resizeDebounceTimeout&&clearTimeout(this.resizeDebounceTimeout)}};var vv=Ce(tn()),Sv=Ce(Xo());var st=Ce(tn()),yv=require("obsidian"),L=Ce(za()),Dt=({icon:e,className:t,size:n=16})=>{let a=(0,st.useRef)(null);return(0,st.useEffect)(()=>{if(a.current){a.current.innerHTML="",(0,yv.setIcon)(a.current,e);let i=a.current.querySelector("svg");i&&n&&(i.style.width=`${n}px`,i.style.height=`${n}px`)}},[e,n]),(0,L.jsx)("span",{ref:a,className:t,style:{display:"flex",alignItems:"center",justifyContent:"center"}})},bv=({isVisible:e,onClose:t,queue:n,currentIndex:a,isPlayingFromQueue:i,onPlayItem:s,onRemoveItem:r,onClearQueue:l,onMoveItem:o,onPlayQueue:u,loopEnabled:c,onToggleLoop:f,initialPosition:d={x:100,y:100},onDragEnd:m})=>{let[y,E]=(0,st.useState)(d),[M,g]=(0,st.useState)(!1),[h,p]=(0,st.useState)({x:0,y:0}),[S,A]=(0,st.useState)(!0),_=(0,st.useRef)(null),O=(0,st.useRef)(null),R=v=>{if("touches"in v){let T=v.touches[0]||v.changedTouches[0];return{clientX:T.clientX,clientY:T.clientY}}else return{clientX:v.clientX,clientY:v.clientY}},q=v=>{let T=v.target;if(!(!T.closest(".queue-header")||T.closest(".queue-header-buttons"))){if(g(!0),_.current){let P=_.current.getBoundingClientRect(),ee=R(v.nativeEvent);p({x:ee.clientX-P.left,y:ee.clientY-P.top})}v.preventDefault()}},k=v=>{if(M){let T=R(v);E({x:T.clientX-h.x,y:T.clientY-h.y})}},b=()=>{M&&(g(!1),m&&m(y))};(0,st.useEffect)(()=>{if(S&&i&&a>=0&&O.current){let v=O.current.querySelector(`.queue-item:nth-child(${a+1})`);v&&v.scrollIntoView({behavior:"smooth",block:"nearest",inline:"nearest"})}},[S,i,a]),(0,st.useEffect)(()=>(M?(document.addEventListener("mousemove",k),document.addEventListener("mouseup",b),document.addEventListener("touchmove",k,{passive:!1}),document.addEventListener("touchend",b)):(document.removeEventListener("mousemove",k),document.removeEventListener("mouseup",b),document.removeEventListener("touchmove",k),document.removeEventListener("touchend",b)),()=>{document.removeEventListener("mousemove",k),document.removeEventListener("mouseup",b),document.removeEventListener("touchmove",k),document.removeEventListener("touchend",b)}),[M,h]),(0,st.useEffect)(()=>{E(d)},[d]);let x=(v,T=25)=>v.length>T?v.substring(0,T)+"...":v,B=()=>{A(!S)},$=()=>{f(!c)};return e?(0,L.jsxs)("div",{ref:_,className:"queue-manager-ui",style:{left:`${y.x}px`,top:`${y.y}px`,cursor:M?"grabbing":void 0,touchAction:"none"},onMouseDown:q,onTouchStart:q,children:[(0,L.jsxs)("div",{className:"queue-header",children:[(0,L.jsxs)("div",{className:"queue-header-title-section",children:[(0,L.jsx)(Dt,{icon:"list-music",size:18}),(0,L.jsx)("span",{className:"queue-header-title",children:"Playback Queue"}),(0,L.jsx)("span",{className:"queue-header-count",children:n.length})]}),(0,L.jsxs)("div",{className:"queue-header-buttons",children:[(0,L.jsxs)("div",{className:"queue-header-buttons-row",children:[n.length>0&&(0,L.jsx)("button",{onClick:B,className:`queue-mini-button ${S?"active":""}`,"aria-label":S?"Disable auto-scroll":"Enable auto-scroll",title:S?"Auto-scroll enabled":"Auto-scroll disabled",children:(0,L.jsx)(Dt,{icon:S?"scroll-text":"scroll",size:12})}),n.length>0&&(0,L.jsx)("button",{onClick:$,className:`queue-mini-button ${c?"active":""}`,"aria-label":c?"Disable loop":"Enable loop",title:c?"Loop enabled":"Loop disabled",children:(0,L.jsx)(Dt,{icon:"repeat",size:12})}),(0,L.jsx)("button",{onClick:t,className:"queue-mini-button","aria-label":"Close Queue",children:(0,L.jsx)(Dt,{icon:"x",size:12})})]}),(0,L.jsxs)("div",{className:"queue-header-buttons-row",children:[n.length>0&&u&&(0,L.jsx)("button",{onClick:u,className:"queue-mini-button play-button","aria-label":"Play Queue from Beginning",children:(0,L.jsx)(Dt,{icon:"play",size:12})}),n.length>0&&(0,L.jsx)("button",{onClick:l,className:"queue-mini-button","aria-label":"Clear Queue",children:(0,L.jsx)(Dt,{icon:"trash-2",size:12})})]})]})]}),(0,L.jsx)("div",{ref:O,className:`queue-content-area ${n.length===0?"has-empty-message":""}`,children:n.length===0?(0,L.jsxs)("div",{className:"queue-empty-message",children:[(0,L.jsx)(Dt,{icon:"list-music",size:48}),(0,L.jsx)("div",{className:"queue-empty-title",children:"Queue is empty"}),(0,L.jsx)("div",{className:"queue-empty-instructions",children:"Add notes to your playback queue by:"}),(0,L.jsxs)("div",{className:"queue-empty-instruction-list",children:[(0,L.jsx)("div",{children:"\u2022 Right-clicking on notes"}),(0,L.jsx)("div",{children:'\u2022 Using "Add to queue" menu items'}),(0,L.jsx)("div",{children:"\u2022 Using keyboard shortcuts"})]})]}):n.map((v,T)=>(0,L.jsxs)("div",{className:`queue-item ${T===a&&i?"is-playing":""}`,onClick:()=>s(T),children:[(0,L.jsx)("div",{className:"queue-item-number-status",children:T===a&&i?(0,L.jsx)(Dt,{icon:"volume-2",size:14}):T+1}),(0,L.jsx)("div",{className:"queue-item-title",title:v.title||"Untitled",children:x(v.title||"Untitled")}),T===a&&i&&(0,L.jsx)("div",{className:"queue-item-playing-indicator"}),(0,L.jsxs)("div",{className:"queue-item-controls",children:[T>0&&(0,L.jsx)("button",{onClick:P=>{P.stopPropagation(),o(T,T-1)},className:"queue-item-control-button",title:"Move Up",children:(0,L.jsx)(Dt,{icon:"chevron-up",size:14})}),T<n.length-1&&(0,L.jsx)("button",{onClick:P=>{P.stopPropagation(),o(T,T+1)},className:"queue-item-control-button",title:"Move Down",children:(0,L.jsx)(Dt,{icon:"chevron-down",size:14})}),(0,L.jsx)("button",{onClick:P=>{P.stopPropagation(),r(T)},className:"queue-item-control-button queue-item-remove-button",title:"Remove from Queue",children:(0,L.jsx)(Dt,{icon:"x",size:14})})]})]},T))})]}):null};var Ko=class{constructor(t){this.hostElement=null;this.reactRoot=null;this.lastPosition=void 0;this.isQueueVisible=!1;this.resizeDebounceTimeout=null;this.RESIZE_DEBOUNCE_DELAY=250;this.debouncedWindowResize=()=>{this.resizeDebounceTimeout&&clearTimeout(this.resizeDebounceTimeout),this.resizeDebounceTimeout=window.setTimeout(()=>{this.handleWindowResize()},this.RESIZE_DEBOUNCE_DELAY)};this.audioManager=t.audioManager,this.savePosition=t.savePositionCallback,this.handleWindowResize=this.handleWindowResize.bind(this),window.addEventListener("resize",this.debouncedWindowResize),this.renderComponent()}handleWindowResize(){if(!this.isQueueVisible||!this.hostElement)return;let t=this.hostElement.querySelector(".queue-manager-ui");if(!t)return;let n=t.getBoundingClientRect(),{innerWidth:a,innerHeight:i}=window,s=50;(n.right<s||n.left>a-s||n.bottom<s||n.top>i-s)&&(console.log("Queue manager is out of bounds, repositioning..."),this.repositionQueueInBounds())}createHostElement(){this.hostElement||(this.hostElement=document.createElement("div"),this.hostElement.id="obsidian-edge-tts-queue-manager-host",document.body.appendChild(this.hostElement),this.reactRoot=Sv.default.createRoot(this.hostElement))}showQueue(){this.createHostElement(),this.isQueueVisible=!0,this.renderComponent()}hideQueue(){this.isQueueVisible=!1,this.lastPosition&&this.savePosition(this.lastPosition).catch(t=>{console.error("Failed to save queue position on hide:",t)}),this.renderComponent()}toggleQueueVisibility(){this.isQueueVisible?this.hideQueue():this.showQueue()}getIsQueueVisible(){return this.isQueueVisible}updateQueue(){this.isQueueVisible&&this.renderComponent()}resetQueuePosition(){this.repositionQueueInBounds()}repositionQueueInBounds(){if(!this.hostElement)return;let t=this.hostElement.querySelector(".queue-manager-ui");if(!t)return;let n=t.getBoundingClientRect(),{innerWidth:a,innerHeight:i}=window,s=30,r=a-n.width-s,l=i-n.height-Ge.PLAYER_HEIGHT-s,o=r,u=l;o<s&&(o=s),u<s&&(u=s),o+n.width>a-s&&(o=a-n.width-s),u+n.height>i-s&&(u=i-n.height-s),o=Math.max(s,Math.min(o,a-n.width-s)),u=Math.max(s,Math.min(u,i-n.height-s)),this.lastPosition={x:o,y:u},this.savePosition(this.lastPosition).catch(c=>{console.error("Failed to save queue position on reposition:",c)}),this.isQueueVisible&&this.renderComponent()}renderComponent(){var t;if(this.reactRoot){let n=typeof window!="undefined"?window.innerWidth-Ge.QUEUE_WIDTH:50,a=typeof window!="undefined"?window.innerHeight-Ge.QUEUE_HEIGHT:50,i=(t=this.lastPosition)!=null?t:{x:n,y:a},s=this.audioManager.getQueueStatus();this.reactRoot.render(vv.default.createElement(bv,{isVisible:this.isQueueVisible,onClose:()=>this.hideQueue(),queue:s.queue,currentIndex:s.currentIndex,isPlayingFromQueue:s.isPlayingFromQueue,onPlayItem:r=>this.audioManager.playQueueItem(r),onRemoveItem:r=>{this.audioManager.removeQueueItem(r),this.updateQueue()},onClearQueue:()=>{this.audioManager.clearQueue(),this.updateQueue()},onMoveItem:(r,l)=>{this.audioManager.moveQueueItem(r,l),this.updateQueue()},onPlayQueue:()=>this.audioManager.playQueue(),loopEnabled:this.audioManager.getLoopEnabled(),onToggleLoop:r=>{this.audioManager.setLoopEnabled(r),this.updateQueue()},initialPosition:i,onDragEnd:r=>{this.lastPosition=r,this.savePosition(r).catch(l=>{console.error("Failed to save queue position on drag end:",l)})}}))}}setInitialSavedPosition(t){t&&(this.lastPosition=t,this.isQueueVisible&&this.renderComponent())}getCurrentPosition(){return this.lastPosition}destroy(){this.reactRoot&&(this.reactRoot.unmount(),this.reactRoot=null),this.hostElement&&(this.hostElement.remove(),this.hostElement=null),this.isQueueVisible=!1,window.removeEventListener("resize",this.debouncedWindowResize),this.resizeDebounceTimeout&&clearTimeout(this.resizeDebounceTimeout)}};var wv=Ce(tn()),Mv=Ce(Xo());var Jo=Ce(tn()),Tv=require("obsidian"),Y=Ce(za()),Wo=({icon:e,className:t,size:n=16})=>{let a=(0,Jo.useRef)(null);return(0,Jo.useEffect)(()=>{if(a.current){a.current.innerHTML="",(0,Tv.setIcon)(a.current,e);let i=a.current.querySelector("svg");i&&n&&(i.style.width=`${n}px`,i.style.height=`${n}px`)}},[e,n]),(0,Y.jsx)("span",{ref:a,className:t,style:{display:"flex",alignItems:"center",justifyContent:"center"}})};var Ev=({isVisible:e,onClose:t,totalChunks:n,chunks:a,currentPhase:i,overallProgress:s,errorMessage:r,noteTitle:l="Unknown Note"})=>{if(!e)return null;let o=()=>{switch(i){case"splitting":return"Splitting note into chunks...";case"generating":return"Generating audio chunks...";case"combining":return"Combining audio files...";case"completed":return"MP3 generation completed!";case"error":return"Error occurred during generation";default:return"Processing..."}},u=()=>{switch(i){case"splitting":return"scissors";case"generating":return"cpu";case"combining":return"package";case"completed":return"check-circle";case"error":return"alert-circle";default:return"loader-2"}},c=y=>{switch(y){case"pending":return"clock";case"processing":return"loader-2";case"completed":return"check";case"failed":return"x";default:return"circle"}},f=y=>{switch(y){case"pending":return"Waiting";case"processing":return"Processing";case"completed":return"Done";case"failed":return"Failed";default:return"Unknown"}},d=a.filter(y=>y.status==="completed").length,m=a.filter(y=>y.status==="failed").length;return(0,Y.jsxs)("div",{className:"chunked-progress-ui",children:[(0,Y.jsx)("div",{className:"chunked-progress-header",children:(0,Y.jsxs)("div",{className:"chunked-progress-title-section",children:[(0,Y.jsx)(Wo,{icon:u(),size:18,className:i==="generating"?"spinning":""}),(0,Y.jsx)("span",{className:"chunked-progress-title",children:"MP3 Generation"}),(i==="completed"||i==="error")&&(0,Y.jsx)("button",{onClick:t,className:"chunked-progress-close-button","aria-label":"Close",children:(0,Y.jsx)(Wo,{icon:"x",size:14})})]})}),(0,Y.jsxs)("div",{className:"chunked-progress-content",children:[(0,Y.jsx)("div",{className:"chunked-progress-note-title",title:l,children:l.length>30?l.substring(0,30)+"...":l}),(0,Y.jsxs)("div",{className:"chunked-progress-phase",children:[(0,Y.jsx)("span",{className:"chunked-progress-phase-text",children:o()}),i!=="completed"&&i!=="error"&&(0,Y.jsxs)("span",{className:"chunked-progress-percentage",children:[Math.round(s),"%"]})]}),i==="error"&&r&&(0,Y.jsxs)("div",{className:"chunked-progress-error",children:[(0,Y.jsx)(Wo,{icon:"alert-triangle",size:14}),(0,Y.jsx)("span",{children:r})]}),(0,Y.jsx)("div",{className:"chunked-progress-overall-bar",children:(0,Y.jsx)("div",{className:`chunked-progress-overall-fill ${i==="error"?"error":""}`,style:{width:`${s}%`}})}),(0,Y.jsxs)("div",{className:"chunked-progress-stats",children:[(0,Y.jsxs)("span",{children:["Total Chunks: ",n]}),a.length>0&&(0,Y.jsxs)(Y.Fragment,{children:[(0,Y.jsxs)("span",{children:["Completed: ",d]}),m>0&&(0,Y.jsxs)("span",{className:"error-text",children:["Failed: ",m]})]})]}),a.length>0&&i==="generating"&&(0,Y.jsxs)("div",{className:"chunked-progress-chunks",children:[(0,Y.jsx)("div",{className:"chunked-progress-chunks-header",children:(0,Y.jsx)("span",{children:"Chunk Progress:"})}),(0,Y.jsx)("div",{className:"chunked-progress-chunks-list",children:a.map((y,E)=>(0,Y.jsxs)("div",{className:`chunked-progress-chunk ${y.status}`,title:y.error||`Chunk ${E+1}: ${f(y.status)}`,children:[(0,Y.jsx)("span",{className:"chunked-progress-chunk-number",children:E+1}),(0,Y.jsx)(Wo,{icon:c(y.status),size:12,className:y.status==="processing"?"spinning":""}),y.status==="processing"&&y.progress>0&&(0,Y.jsxs)("span",{className:"chunked-progress-chunk-progress",children:[y.progress,"%"]})]},y.id))})]})]})]})};var eu=class{constructor(){this.hostElement=null;this.reactRoot=null;this.state={isVisible:!1,totalChunks:0,chunks:[],currentPhase:"splitting",overallProgress:0};this.resizeDebounceTimeout=null;this.RESIZE_DEBOUNCE_DELAY=250;this.debouncedWindowResize=()=>{this.resizeDebounceTimeout&&clearTimeout(this.resizeDebounceTimeout),this.resizeDebounceTimeout=window.setTimeout(()=>{this.handleWindowResize()},this.RESIZE_DEBOUNCE_DELAY)};this.handleWindowResize=this.handleWindowResize.bind(this),window.addEventListener("resize",this.debouncedWindowResize)}handleWindowResize(){!this.state.isVisible||!this.hostElement||this.updatePosition()}createHostElement(){this.hostElement||(this.hostElement=document.createElement("div"),this.hostElement.id="obsidian-edge-tts-chunked-progress-host",document.body.appendChild(this.hostElement),this.reactRoot=Mv.default.createRoot(this.hostElement),this.updatePosition())}updatePosition(){if(!this.hostElement)return;let t=20,n=30,a=200,i=t,s=n+t;this.hostElement.style.position="fixed",this.hostElement.style.right=`${i}px`,this.hostElement.style.bottom=`${s}px`,this.hostElement.style.zIndex="1000",this.hostElement.style.pointerEvents=this.state.isVisible?"auto":"none"}show(t={}){this.createHostElement(),this.state={...this.state,...t,isVisible:!0},this.renderComponent()}hide(){this.state.isVisible=!1,this.renderComponent()}updateState(t){this.state={...this.state,...t},this.state.isVisible&&this.renderComponent()}updateChunk(t,n){let a=this.state.chunks.findIndex(i=>i.id===t);if(a!==-1){let i=[...this.state.chunks];i[a]={...i[a],...n},this.updateState({chunks:i})}}addChunk(t){this.updateState({chunks:[...this.state.chunks,t]})}setChunks(t){this.updateState({chunks:t})}getState(){return{...this.state}}calculateOverallProgress(){if(this.state.currentPhase==="splitting")return 5;if(this.state.currentPhase==="generating"){if(this.state.chunks.length===0)return 5;let t=this.state.chunks.filter(l=>l.status==="completed").length,n=this.state.chunks.filter(l=>l.status==="processing"),a=t*100;n.forEach(l=>{a+=Math.max(l.progress,10)});let i=n.filter(l=>l.progress===0).length;a+=i*5;let r=5+a/(this.state.chunks.length*100)*80;return Math.min(85,r)}else{if(this.state.currentPhase==="combining")return 90;if(this.state.currentPhase==="completed")return 100;if(this.state.currentPhase==="error")return this.state.overallProgress}return 0}renderComponent(){if(this.reactRoot){let t=this.calculateOverallProgress();this.reactRoot.render(wv.default.createElement(Ev,{isVisible:this.state.isVisible,onClose:()=>this.hide(),totalChunks:this.state.totalChunks,chunks:this.state.chunks,currentPhase:this.state.currentPhase,overallProgress:t,errorMessage:this.state.errorMessage,noteTitle:this.state.noteTitle})),this.state.isVisible&&this.updatePosition()}}destroy(){this.reactRoot&&(this.reactRoot.unmount(),this.reactRoot=null),this.hostElement&&(this.hostElement.remove(),this.hostElement=null),this.state.isVisible=!1,window.removeEventListener("resize",this.debouncedWindowResize),this.resizeDebounceTimeout&&clearTimeout(this.resizeDebounceTimeout)}};var Xd=class{constructor(){}},Tn=class{static splitTextIntoChunks(t,n=Tn.DEFAULT_MAX_CHUNK_LENGTH){let a=[],i="",s=t.split(/\n\s*\n/);for(let r of s)if(r.length>n){i.trim()&&(a.push(i.trim()),i="");let l=r.split(/(?<=[.!?])\s+/);for(let o of l)if(o.length>n){let u=o.split(/\s+/),c="";for(let f of u)(c+" "+f).length>n?(c.trim()&&a.push(c.trim()),c=f):c+=(c?" ":"")+f;c.trim()&&(i=c.trim())}else(i+`

`+o).length>n?(i.trim()&&a.push(i.trim()),i=o):i+=(i?" ":"")+o}else{let l=i+(i?`

`:"")+r;l.length>n&&i.trim()?(a.push(i.trim()),i=r):i=l}return i.trim()&&a.push(i.trim()),a.filter(r=>r.trim().length>0)}static needsChunking(t,n){let a=n?Ze(Ie(t,n.textFiltering.filterFrontmatter),n.textFiltering,n.symbolReplacement):Ze(Ie(t)),i=(n==null?void 0:n.chunkSize)||Tn.DEFAULT_MAX_CHUNK_LENGTH,s=a.length,r=a.split(/\s+/).length;return s>i||r>Tn.MAX_WORD_LENGTH}static async generateChunkedMP3(t){let{text:n,settings:a,progressManager:i,noteTitle:s="Note",maxChunkLength:r=Tn.DEFAULT_MAX_CHUNK_LENGTH}=t;try{let l=Zt(n);if(l.wasTruncated){let M=l.truncationReason==="words"?"word":"character",g=l.truncationReason==="words"?"5,000 words":"30,000 characters";i.updateState({currentPhase:"splitting",noteTitle:s,overallProgress:0,errorMessage:`Content truncated: exceeded ${g} limit. Processing first ${l.finalWordCount.toLocaleString()} words.`}),a.showNotices&&console.warn(`Content exceeds MP3 generation limit (${g}). Generating MP3 for the first ${l.finalWordCount.toLocaleString()} words. Original content had ${l.originalWordCount.toLocaleString()} words.`),await new Promise(h=>setTimeout(h,2e3))}i.updateState({currentPhase:"splitting",noteTitle:s,overallProgress:0,errorMessage:void 0});let o=Ze(Ie(l.content,a.textFiltering.filterFrontmatter),a.textFiltering,a.symbolReplacement);if(!o.trim())throw new Error("No readable text after filtering");let u=Tn.splitTextIntoChunks(o,r);if(u.length===0)throw new Error("No valid chunks created from text");let c=u.map((M,g)=>({id:`chunk-${g}`,text:M,status:"pending",progress:0}));i.updateState({currentPhase:"generating",totalChunks:c.length,chunks:c});let f=a.customVoice.trim()||a.selectedVoice,d=en.AUDIO_24KHZ_48KBITRATE_MONO_MP3,m=new Xd;m.rate=a.playbackSpeed;let y=[];for(let M=0;M<c.length;M++){let g=c[M];try{i.updateChunk(g.id,{status:"processing",progress:0}),i.updateState({});let h=new Jt;await h.setMetadata(f,d);let p=h.toStream(g.text,m),S=[],A=g.text.length*50,_=0;p.on("data",R=>{S.push(R),_+=R.length;let q=Math.min(99,Math.floor(_/A*100));i.updateChunk(g.id,{progress:q}),q%10===0&&i.updateState({})});let O=await new Promise((R,q)=>{p.on("end",()=>{let b=Buffer.concat(S);R(b)});let k=b=>{console.error(`Stream error for chunk ${M+1}:`,b),q(b)};try{p.on("error",k)}catch(b){}setTimeout(()=>{q(new Error(`Timeout generating chunk ${M+1}`))},12e4)});i.updateChunk(g.id,{status:"completed",progress:100}),i.updateState({}),y.push(O)}catch(h){console.error(`Error generating chunk ${M+1}:`,h),i.updateChunk(g.id,{status:"failed",error:h instanceof Error?h.message:"Unknown error"})}M<c.length-1&&await new Promise(h=>setTimeout(h,500))}if(y.length===0)throw new Error("Failed to generate any audio chunks");i.updateState({currentPhase:"combining"});let E=Buffer.concat(y);return i.updateState({currentPhase:"completed"}),E}catch(l){return console.error("Chunked MP3 generation error:",l),i.updateState({currentPhase:"error",errorMessage:l instanceof Error?l.message:"Unknown error occurred"}),null}}static estimateChunkCount(t,n){let a=n?Ze(Ie(t,n.textFiltering.filterFrontmatter),n.textFiltering):Ze(Ie(t)),i=(n==null?void 0:n.chunkSize)||Tn.DEFAULT_MAX_CHUNK_LENGTH;return Math.ceil(a.length/i)}static getRecommendedChunkSize(t,n){let a=(n==null?void 0:n.chunkSize)||Tn.DEFAULT_MAX_CHUNK_LENGTH,i=n?Ze(Ie(t,n.textFiltering.filterFrontmatter),n.textFiltering):Ze(Ie(t));return i.length>5e4?Math.min(a,7e3):i.length>2e4?Math.min(a,8e3):a}},En=Tn;En.DEFAULT_MAX_CHUNK_LENGTH=9e3,En.MAX_WORD_LENGTH=1500;var tu=class extends I.Plugin{constructor(){super(...arguments);this.mp3GenerationTasks=new Map}async onload(){await this.loadSettings();let n=this.app.vault.configDir+"/plugins/"+this.manifest.id+"/temp",a=n+"/tts-temp-audio.mp3";this.fileManager=new pl(this.app,this.settings,a),await this.fileManager.cleanupTempAudioFile(),await this.app.vault.adapter.exists(n)||await this.app.vault.adapter.mkdir(n),this.ttsEngine=new bl(this.settings),this.audioManager=new gl(this.settings,this.updateStatusBar.bind(this),i=>{console.warn("showFloatingPlayerCallback not yet initialized with data:",i)},()=>{console.warn("hideFloatingPlayerCallback not yet initialized")},i=>{console.warn("updateFloatingPlayerCallback not yet initialized with data:",i)},this.fileManager,this.app),this.floatingUIManager=new Zo({audioManager:this.audioManager,savePositionCallback:async i=>{this.settings.floatingPlayerPosition=i,await this.saveSettings()},enableQueueFeature:this.settings.enableQueueFeature}),this.settings.enableQueueFeature&&(this.queueUIManager=new Ko({audioManager:this.audioManager,savePositionCallback:async i=>{this.settings.queueManagerPosition=i,await this.saveSettings()}}),this.queueUIManager&&this.floatingUIManager.setQueueUIManager(this.queueUIManager)),this.audioManager.setFloatingPlayerCallbacks(i=>this.floatingUIManager.showPlayer(i),()=>this.floatingUIManager.hidePlayer(),i=>this.floatingUIManager.updatePlayerState(i)),this.settings.enableQueueFeature&&this.queueUIManager&&(this.audioManager.setQueueChangeCallback(()=>{var i;(i=this.queueUIManager)==null||i.updateQueue()}),this.audioManager.setQueueUIUpdateCallback(()=>{var i;(i=this.queueUIManager)==null||i.updateQueue()})),this.uiManager=new vl(this,this.settings,this.audioManager,this.ttsEngine),this.settings.floatingPlayerPosition&&this.floatingUIManager.setInitialSavedPosition(this.settings.floatingPlayerPosition),this.settings.enableQueueFeature&&this.queueUIManager&&this.settings.queueManagerPosition&&this.queueUIManager.setInitialSavedPosition(this.settings.queueManagerPosition),this.chunkedProgressManager=new eu,this.addSettingTab(new Sr(this.app,this)),this.uiManager.addPluginRibbonIcon(),this.settings.showStatusBarButton&&this.uiManager.initializeStatusBar(),this.settings.showMenuItems&&this.uiManager.addPluginMenuItems(),this.registerInterval(window.setInterval(()=>this.monitorTasks(),1e3)),this.addCommand({id:"read-note-aloud",name:"Read note aloud",editorCallback:(i,s)=>{this.readNoteAloud(i,s)}}),I.Platform.isMobile||this.addCommand({id:"generate-mp3",name:"Generate MP3",editorCallback:(i,s)=>{this.generateMP3(i,s)}}),this.addCommand({id:"stop-tts-playback",name:"Stop TTS playback",callback:()=>{this.audioManager.stopPlayback()}}),this.addCommand({id:"pause-resume-playback",name:"Pause/Resume playback",callback:()=>{this.audioManager.isPlaybackPaused()?this.audioManager.resumePlayback():this.audioManager.pausePlayback()}}),this.addCommand({id:"jump-forward-10s",name:"Jump forward 10 seconds",callback:()=>this.audioManager.jumpForward()}),this.addCommand({id:"jump-backward-10s",name:"Jump backward 10 seconds",callback:()=>this.audioManager.jumpBackward()}),this.addCommand({id:"read-selected-text",name:"Read selected text aloud",editorCallback:(i,s)=>{let r=i.getSelection();r.trim()?this.audioManager.startPlayback(r):ta(this.settings)&&new I.Notice("No text selected.")}}),this.addCommand({id:"show-floating-playback-controls",name:"Show floating playback controls",callback:()=>{this.floatingUIManager.getIsPlayerVisible()||this.floatingUIManager.showPlayer()}}),this.addCommand({id:"reset-floating-player-position",name:"Reset floating player position",callback:()=>{this.floatingUIManager.resetPlayerPosition(),ta(this.settings)&&new I.Notice("Floating player position reset.")}}),this.settings.enableQueueFeature&&(this.addCommand({id:"add-note-to-queue",name:"Add current note to playback queue",editorCallback:(i,s)=>{var o;let r=((o=s.file)==null?void 0:o.basename)||"Untitled",l=i.getValue();this.audioManager.addToQueue(l,r)}}),this.addCommand({id:"add-selection-to-queue",name:"Add selected text to playback queue",editorCallback:(i,s)=>{var l;let r=i.getSelection();if(r.trim()){let o=((l=s.file)==null?void 0:l.basename)||"Untitled";this.audioManager.addToQueue(r,`${o} (selection)`)}else ta(this.settings)&&new I.Notice("No text selected.")}}),this.addCommand({id:"play-queue",name:"Play entire queue",callback:()=>{this.audioManager.playQueue()}}),this.addCommand({id:"clear-queue",name:"Clear playback queue",callback:()=>{this.audioManager.clearQueue()}}),this.addCommand({id:"show-queue-status",name:"Show queue status",callback:()=>{let i=this.audioManager.getQueueStatus(),s=i.queue.length===0?"Playback queue is empty.":`Queue has ${i.queue.length} items. ${i.isPlayingFromQueue?`Currently playing item ${i.currentIndex+1}.`:"Not currently playing from queue."}`;this.settings.showNotices&&new I.Notice(s)}})),this.addCommand({id:"set-sleep-timer-15min",name:"Set sleep timer (15 minutes)",callback:()=>{this.audioManager.setSleepTimer(15)}}),this.addCommand({id:"set-sleep-timer-30min",name:"Set sleep timer (30 minutes)",callback:()=>{this.audioManager.setSleepTimer(30)}}),this.addCommand({id:"set-sleep-timer-60min",name:"Set sleep timer (60 minutes)",callback:()=>{this.audioManager.setSleepTimer(60)}}),this.addCommand({id:"cancel-sleep-timer",name:"Cancel sleep timer",callback:()=>{this.audioManager.cancelSleepTimer()}}),this.settings.enableQueueFeature&&this.queueUIManager&&(this.addCommand({id:"show-queue-manager",name:"Show queue manager",callback:()=>{var i;(i=this.queueUIManager)==null||i.showQueue()}}),this.addCommand({id:"toggle-queue-manager",name:"Toggle queue manager",callback:()=>{var i;(i=this.queueUIManager)==null||i.toggleQueueVisibility()}}),this.addCommand({id:"reset-queue-position",name:"Reset queue manager position",callback:()=>{var i;(i=this.queueUIManager)==null||i.resetQueuePosition(),this.settings.showNotices&&new I.Notice("Queue manager position reset.")}})),I.Platform.isMobile||this.addCommand({id:"force-chunked-mp3-generation",name:"Force chunked MP3 generation",editorCallback:(i,s)=>{var l;let r=i.getSelection()||i.getValue();if(r.trim()){let o=Zt(r);if(o.wasTruncated){let u=o.truncationReason==="words"?"word":"character",c=o.truncationReason==="words"?"5,000 words":"30,000 characters";this.settings.showNotices&&new I.Notice(`Content exceeds MP3 generation limit (${c}). Generating MP3 for the first ${o.finalWordCount.toLocaleString()} words (${o.finalCharCount.toLocaleString()} characters). Original content had ${o.originalWordCount.toLocaleString()} words.`,8e3)}this.generateChunkedMP3(o.content,i,(l=s.file)==null?void 0:l.path)}else this.settings.showNotices&&new I.Notice("No text available for chunked generation.")}}),this.addCommand({id:"debug-media-session",name:"Debug Media Session API support",callback:()=>{let i="mediaSession"in navigator,s=i&&"MediaMetadata"in window,r=this.settings.enableExperimentalFeatures,l=/Android/i.test(navigator.userAgent),o=/iPad|iPhone|iPod/.test(navigator.userAgent),u=[`Media Session API: ${i?"\u2705 Supported":"\u274C Not supported"}`,`MediaMetadata: ${s?"\u2705 Supported":"\u274C Not supported"}`,`Experimental features: ${r?"\u2705 Enabled":"\u274C Disabled"}`,`Platform: ${I.Platform.isMobile?"Mobile":"Desktop"}`,`OS: ${l?"Android":o?"iOS":"Other"}`,`Expected to work: ${i&&r?o?"\u2705 Yes (iOS)":l?"\u26A0\uFE0F Maybe (Android)":"\u26A0\uFE0F Unknown":"\u274C No"}`];console.log("Media Session Debug Info:",u),new I.Notice(`Media Session Debug:
${u.join(`
`)}`,12e3)}})}monitorTasks(){for(let[n,a]of this.mp3GenerationTasks.entries()){let i=this.ttsEngine.getTask(a.taskId);if(!i){this.mp3GenerationTasks.delete(n);continue}i.status==="completed"&&i.buffer?(this.mp3GenerationTasks.delete(n),this.fileManager.saveMP3File(i.buffer,a.filePath).then(s=>{s&&this.settings.embedInNote&&this.fileManager.embedMP3InNote(s,a.filePath,a.editor)}),this.settings.showNotices&&new I.Notice("MP3 generation complete")):i.status==="failed"&&(this.mp3GenerationTasks.delete(n),this.settings.showNotices&&new I.Notice(`MP3 generation failed: ${i.error||"Unknown error"}`))}Math.random()<.0033&&this.ttsEngine.cleanupOldTasks()}updateStatusBar(n=!1){this.uiManager.updateStatusBar(n)}async readNoteAloud(n,a,i){let s="";if(i){let l=await this.fileManager.extractFileContent(i);if(l)s=l;else{this.settings.showNotices&&new I.Notice("Failed to read note aloud.");return}}else{let l=a!=null?a:this.app.workspace.getActiveViewOfType(I.MarkdownView);!n&&l&&(n=l.editor),n&&l&&(s=n.getSelection()||n.getValue())}let r=Zt(s);if(r.wasTruncated){let l=r.truncationReason==="words"?"word":"character",o=r.truncationReason==="words"?"5,000 words":"30,000 characters";this.settings.showNotices&&new I.Notice(`Content exceeds playback limit (${o}). Playing first ${r.finalWordCount.toLocaleString()} words (${r.finalCharCount.toLocaleString()} characters). Original content had ${r.originalWordCount.toLocaleString()} words.`,8e3)}await this.audioManager.startPlayback(r.content)}async generateMP3(n,a,i){if(I.Platform.isMobile){this.settings.showNotices&&new I.Notice("MP3 generation is not supported on mobile devices due to file system limitations. Use audio playback instead.");return}let s="";if(i){let o=await this.fileManager.extractFileContent(i);if(o)s=o;else{this.settings.showNotices&&new I.Notice("Failed to generate MP3: could not read file.");return}}else{let o=a!=null?a:this.app.workspace.getActiveViewOfType(I.MarkdownView);!n&&o&&(n=o.editor),n&&o&&(s=n.getSelection()||n.getValue())}if(!s.trim()){this.settings.showNotices&&new I.Notice("No text selected or available.");return}let r=Zt(s);if(r.wasTruncated){let o=r.truncationReason==="words"?"word":"character",u=r.truncationReason==="words"?"5,000 words":"30,000 characters";this.settings.showNotices&&new I.Notice(`Content exceeds MP3 generation limit (${u}). Generating MP3 for the first ${r.finalWordCount.toLocaleString()} words (${r.finalCharCount.toLocaleString()} characters). Original content had ${r.originalWordCount.toLocaleString()} words.`,8e3)}let l=r.content;if(En.needsChunking(l,this.settings)){await this.generateChunkedMP3(l,n,i);return}try{this.settings.showNotices&&new I.Notice("Starting MP3 generation in background...");let o=this.ttsEngine.createTask(l,en.AUDIO_24KHZ_48KBITRATE_MONO_MP3),u=`mp3-${Date.now()}`;this.mp3GenerationTasks.set(u,{taskId:o.id,editor:n,filePath:i})}catch(o){console.error("Error starting MP3 generation:",o),this.settings.showNotices&&new I.Notice("Failed to start MP3 generation.")}}async generateChunkedMP3(n,a,i){var s,r;if(I.Platform.isMobile){this.settings.showNotices&&new I.Notice("MP3 generation is not supported on mobile devices due to file system limitations. Use audio playback instead.");return}try{let l=i&&((r=(s=this.app.vault.getAbstractFileByPath(i))==null?void 0:s.name)==null?void 0:r.replace(/\.md$/,""))||"Note";this.chunkedProgressManager.show({noteTitle:l,totalChunks:En.estimateChunkCount(n,this.settings)});let o=await En.generateChunkedMP3({text:n,settings:this.settings,progressManager:this.chunkedProgressManager,noteTitle:l,maxChunkLength:En.getRecommendedChunkSize(n,this.settings)});if(o){let u=await this.fileManager.saveMP3File(o,i);u&&this.settings.embedInNote&&await this.fileManager.embedMP3InNote(u,i,a),this.settings.showNotices&&new I.Notice("Chunked MP3 generation completed successfully!"),setTimeout(()=>{this.chunkedProgressManager.hide()},3e3)}else this.settings.showNotices&&new I.Notice("Failed to generate chunked MP3."),setTimeout(()=>{this.chunkedProgressManager.hide()},5e3)}catch(l){console.error("Error in chunked MP3 generation:",l),this.chunkedProgressManager.updateState({currentPhase:"error",errorMessage:l instanceof Error?l.message:"Unknown error occurred"}),this.settings.showNotices&&new I.Notice("Failed to generate chunked MP3."),setTimeout(()=>{this.chunkedProgressManager.hide()},5e3)}}async loadSettings(){let n=await this.loadData();this.settings=Object.assign({},na,n),this.settings.textFiltering?this.settings.textFiltering=Object.assign({},na.textFiltering,this.settings.textFiltering):this.settings.textFiltering=na.textFiltering,typeof this.settings.textFiltering.replaceComparisonSymbols=="undefined"&&(this.settings.textFiltering.replaceComparisonSymbols=na.textFiltering.replaceComparisonSymbols),this.settings.symbolReplacement?this.settings.symbolReplacement=Object.assign({},na.symbolReplacement,this.settings.symbolReplacement):this.settings.symbolReplacement=na.symbolReplacement}async saveSettings(){await this.saveData(this.settings),this.audioManager.updateSettings(this.settings),this.fileManager.updateSettings(this.settings),this.uiManager.updateSettings(this.settings),this.ttsEngine.updateSettings(this.settings),this.floatingUIManager.updateQueueFeatureEnabled(this.settings.enableQueueFeature)}onunload(){var s,r,l,o,u;console.log("Unloading Obsidian Edge TTS Plugin"),this.uiManager.removePluginRibbonIcon(),this.audioManager.stopPlayback(),this.uiManager.removeStatusBarButton();let n=this.floatingUIManager.getCurrentPosition(),a=(s=this.queueUIManager)==null?void 0:s.getCurrentPosition(),i=!1;n&&(((r=this.settings.floatingPlayerPosition)==null?void 0:r.x)!==n.x||((l=this.settings.floatingPlayerPosition)==null?void 0:l.y)!==n.y)&&(this.settings.floatingPlayerPosition=n,i=!0),a&&(((o=this.settings.queueManagerPosition)==null?void 0:o.x)!==a.x||((u=this.settings.queueManagerPosition)==null?void 0:u.y)!==a.y)&&(this.settings.queueManagerPosition=a,i=!0),i&&this.saveData(this.settings).catch(c=>console.error("Failed to save UI positions on unload:",c)),this.floatingUIManager.destroy(),this.queueUIManager&&this.queueUIManager.destroy(),this.chunkedProgressManager.destroy()}};
/*! Bundled license information:

axios/dist/browser/axios.cjs:
  (*! Axios v1.9.0 Copyright (c) 2025 Matt Zabriskie and contributors *)

react/cjs/react.production.js:
  (**
   * @license React
   * react.production.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)

scheduler/cjs/scheduler.production.js:
  (**
   * @license React
   * scheduler.production.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)

react-dom/cjs/react-dom.production.js:
  (**
   * @license React
   * react-dom.production.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)

react-dom/cjs/react-dom-client.production.js:
  (**
   * @license React
   * react-dom-client.production.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)

react/cjs/react-jsx-runtime.production.js:
  (**
   * @license React
   * react-jsx-runtime.production.js
   *
   * Copyright (c) Meta Platforms, Inc. and affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)
*/

/* nosourcemap */