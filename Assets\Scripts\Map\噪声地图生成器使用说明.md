# 噪声地图生成器使用说明

## 项目概述

本项目基于Red Blob Games的技术方案实现了一个功能完整的Unity噪声地图生成器，专门用于生成随机地形。该系统采用柏林噪声（Perlin Noise）作为核心算法，支持多种地形生成和可视化功能。

## 核心功能

### 1. 基础噪声生成
- **柏林噪声算法**：使用Unity内置的Mathf.PerlinNoise函数
- **种子控制**：支持固定种子以确保地图的可重现性
- **参数可调**：地图尺寸、噪声缩放、偏移量等全部可调

### 2. 多倍频程噪声叠加（Octaves）
- **标准模式**：使用persistence和lacunarity参数控制
- **自定义振幅模式**：支持手动设置每个倍频程的振幅
- **预设配置**：
  - Red Blob Games预设：[1, 0.5, 0.25, 0.125, 0.0625, 0.03125]
  - 细节增强预设：[1, 1/2, 1/3, 1/4, 1/5]

### 3. 高度重分布系统
- **幂函数重分布**：创建平坦谷地和陡峭山峰
- **反向幂函数**：突出低地区域
- **自定义曲线**：使用AnimationCurve进行精确控制
- **梯田效果**：创建阶梯状地形
- **无重分布**：保持原始噪声形状

### 4. 地形着色系统
- **灰度模式**：简单的黑白高度图
- **生物群系模式**：基于高度的多层地形着色（海洋、海滩、草地、森林、山地、雪山）
- **高度着色模式**：蓝-绿-红渐变表示高度变化
- **自定义Shader**：支持平滑过渡和参数调整

### 5. 实时预览和编辑
- **自定义编辑器**：清晰的折叠式界面
- **实时更新**：参数修改后自动重新生成
- **预设按钮**：快速应用常用配置
- **随机种子生成**：一键生成新的随机种子

### 6. 统计和导出功能
- **地图统计**：高度范围、平均高度、水陆比例等
- **手动导出**：通过编辑器按钮导出PNG纹理
- **自动保存**：可选择生成后自动保存
- **文件命名**：基于种子和时间戳的智能命名

## 使用方法

### 基础设置
1. 在场景中创建一个空的GameObject
2. 添加NoiseMapGenerator组件
3. 创建一个Quad或Plane作为显示对象
4. 将显示对象的Renderer拖拽到"纹理渲染器"字段

### 参数调整
1. **地图基础设置**：
   - 地图宽度/高度：控制生成地图的分辨率
   - 随机种子：控制地图的随机性，相同种子产生相同地图

2. **噪声参数**：
   - 噪声缩放：控制地形特征的大小（值越小，特征越大）
   - 倍频程数量：控制细节层次（通常3-6个效果较好）
   - 持续性：控制高频细节的强度（0.3-0.7效果较好）
   - 间隙性：控制频率倍增系数（通常为2）

3. **高度重分布**：
   - 重分布类型：选择合适的地形塑造方式
   - 重分布指数：控制重分布强度（1.2-2.0效果较好）

4. **预览设置**：
   - 自动更新：开启后参数修改时自动重新生成
   - 地形着色：选择合适的可视化模式

### 推荐参数组合

#### 大陆地形
- 地图尺寸：512x512
- 噪声缩放：50
- 倍频程：4
- 持续性：0.5
- 重分布类型：Power
- 重分布指数：1.5

#### 岛屿地形
- 地图尺寸：256x256
- 噪声缩放：30
- 倍频程：5
- 持续性：0.6
- 重分布类型：Power
- 重分布指数：2.0

#### 山脉地形
- 地图尺寸：512x512
- 噪声缩放：80
- 倍频程：6
- 持续性：0.4
- 重分布类型：InversePower
- 重分布指数：1.8

## 技术实现细节

### 噪声生成算法
```csharp
// 多倍频程噪声叠加
for (int i = 0; i < octaves; i++)
{
    float sampleX = (x - halfWidth) / scale * frequency + octaveOffsets[i].x;
    float sampleY = (y - halfHeight) / scale * frequency + octaveOffsets[i].y;
    
    float perlinValue = Mathf.PerlinNoise(sampleX, sampleY) * 2 - 1;
    noiseHeight += perlinValue * amplitude;
    
    amplitude *= persistence;
    frequency *= lacunarity;
}
```

### 高度重分布
```csharp
// 幂函数重分布
redistributedValue = Mathf.Pow(originalValue, exponent);

// 梯田效果
float terraceValue = value * terraceCount;
float terraceFloor = Mathf.Floor(terraceValue);
```

### 生物群系着色
```csharp
// 基于高度的生物群系判断
if (height < 0.1f) return oceanColor;      // 海洋
if (height < 0.15f) return beachColor;     // 海滩
if (height < 0.3f) return grassColor;      // 草地
// ... 更多生物群系
```

## 性能优化建议

1. **地图尺寸**：大尺寸地图（1024x1024以上）会显著影响性能
2. **倍频程数量**：超过6个倍频程通常收益递减
3. **实时更新**：开发时可开启，发布时建议关闭
4. **纹理格式**：使用Point过滤模式以保持像素化效果

## 扩展建议

1. **湿度图**：添加第二个噪声图用于生物群系判断
2. **温度系统**：基于纬度的温度变化
3. **河流生成**：基于高度梯度的水流模拟
4. **侵蚀效果**：模拟自然侵蚀过程
5. **植被分布**：基于高度和湿度的植被生成

## 故障排除

### 常见问题
1. **地图全黑/全白**：检查噪声缩放参数，通常在10-100之间
2. **没有细节**：增加倍频程数量或调整持续性参数
3. **地形太平坦**：降低重分布指数或使用不同的重分布类型
4. **颜色不正确**：检查地形着色设置和Shader配置

### 调试技巧
1. 开启统计信息查看地图数据分布
2. 使用灰度模式检查原始噪声质量
3. 逐步调整参数，观察每个参数的影响
4. 使用固定种子进行参数对比测试

## 参考资料

- [Red Blob Games - Making maps with noise functions](https://www.redblobgames.com/maps/terrain-from-noise/)
- Unity官方文档：Mathf.PerlinNoise
- 程序化生成相关理论和实践

---

**版本信息**：v1.0  
**创建日期**：2024年  
**兼容性**：Unity 2020.3 LTS及以上版本
