Shader "Custom/TerrainHeightShader"
{
    Properties
    {
        _MainTex ("Height Map", 2D) = "white" {}
        
        [Header(Water)]
        _WaterLevel ("Water Level", Range(0, 1)) = 0.1
        _WaterColor ("Water Color", Color) = (0.2, 0.4, 0.8, 1)
        
        [Header(Beach)]
        _BeachLevel ("Beach Level", Range(0, 1)) = 0.15
        _BeachColor ("Beach Color", Color) = (0.9, 0.8, 0.6, 1)
        
        [Header(Grassland)]
        _GrassLevel ("Grass Level", Range(0, 1)) = 0.4
        _GrassColor ("Grass Color", Color) = (0.3, 0.6, 0.2, 1)
        
        [Header(Forest)]
        _ForestLevel ("Forest Level", Range(0, 1)) = 0.6
        _ForestColor ("Forest Color", Color) = (0.2, 0.4, 0.1, 1)
        
        [Header(Mountain)]
        _MountainLevel ("Mountain Level", Range(0, 1)) = 0.8
        _MountainColor ("Mountain Color", Color) = (0.5, 0.4, 0.3, 1)
        
        [Header(Snow)]
        _SnowColor ("Snow Color", Color) = (0.9, 0.9, 1.0, 1)
        
        [Header(Blending)]
        _BlendStrength ("Blend Strength", Range(0, 0.2)) = 0.05
    }
    
    SubShader
    {
        Tags { "RenderType"="Opaque" }
        LOD 200

        CGPROGRAM
        #pragma surface surf Standard fullforwardshadows
        #pragma target 3.0

        sampler2D _MainTex;

        struct Input
        {
            float2 uv_MainTex;
        };

        // 水域
        float _WaterLevel;
        fixed4 _WaterColor;
        
        // 海滩
        float _BeachLevel;
        fixed4 _BeachColor;
        
        // 草地
        float _GrassLevel;
        fixed4 _GrassColor;
        
        // 森林
        float _ForestLevel;
        fixed4 _ForestColor;
        
        // 山地
        float _MountainLevel;
        fixed4 _MountainColor;
        
        // 雪地
        fixed4 _SnowColor;
        
        // 混合强度
        float _BlendStrength;

        // 平滑混合函数
        float smoothBlend(float value, float threshold, float blendRange)
        {
            return smoothstep(threshold - blendRange, threshold + blendRange, value);
        }

        void surf (Input IN, inout SurfaceOutputStandard o)
        {
            // 获取高度值（灰度值）
            float height = tex2D(_MainTex, IN.uv_MainTex).r;
            
            // 初始化颜色
            fixed4 finalColor = _WaterColor;
            
            // 根据高度分层着色，使用平滑过渡
            if (height < _WaterLevel)
            {
                // 水域
                finalColor = _WaterColor;
            }
            else if (height < _BeachLevel)
            {
                // 海滩区域，在水和海滩之间混合
                float blend = smoothBlend(height, _WaterLevel, _BlendStrength);
                finalColor = lerp(_WaterColor, _BeachColor, blend);
            }
            else if (height < _GrassLevel)
            {
                // 草地区域，在海滩和草地之间混合
                float blend = smoothBlend(height, _BeachLevel, _BlendStrength);
                finalColor = lerp(_BeachColor, _GrassColor, blend);
            }
            else if (height < _ForestLevel)
            {
                // 森林区域，在草地和森林之间混合
                float blend = smoothBlend(height, _GrassLevel, _BlendStrength);
                finalColor = lerp(_GrassColor, _ForestColor, blend);
            }
            else if (height < _MountainLevel)
            {
                // 山地区域，在森林和山地之间混合
                float blend = smoothBlend(height, _ForestLevel, _BlendStrength);
                finalColor = lerp(_ForestColor, _MountainColor, blend);
            }
            else
            {
                // 雪地区域，在山地和雪地之间混合
                float blend = smoothBlend(height, _MountainLevel, _BlendStrength);
                finalColor = lerp(_MountainColor, _SnowColor, blend);
            }
            
            // 设置表面属性
            o.Albedo = finalColor.rgb;
            o.Metallic = 0.0;
            o.Smoothness = 0.2;
            o.Alpha = finalColor.a;
        }
        ENDCG
    }
    FallBack "Diffuse"
}
